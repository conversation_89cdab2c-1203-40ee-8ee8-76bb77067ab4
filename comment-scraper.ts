import fs from 'fs/promises';
import NodeFetchCache, { FileSystemCache } from 'node-fetch-cache';

// const fetch = NodeFetchCache.create({
//   cache: new FileSystemCache({
//     cacheDirectory: 'fb_cache',
//     ttl: 1000 * 60 * 60 * 24 * 7,
//   }),
//   shouldCacheResponse: (response) => response.ok,
// });

interface CommentAuthor {
  id: string;
  name: string;
  profilePicture: string | null;
}

interface CommentReaction {
  type: string;
  count: number;
}

interface Comment {
  id: string;
  text: string | null;
  author: CommentAuthor;
  timestamp: string | null;
  reactions: CommentReaction[];
  totalReactions: number;
  replies: Comment[];
  totalReplies: number;
  attachments: any[];
  depth: number;
}

interface PostWithComments {
  postId: string;
  feedbackId: string;
  comments: Comment[];
  totalComments: number;
}

function objectToQueryString(obj: Record<string, any>): string {
  const params = new URLSearchParams();

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];

      // Handle nested objects (e.g., JSON strings like `variables`)
      if (typeof value === 'object') {
        params.append(key, JSON.stringify(value));
      } else {
        params.append(key, value);
      }
    }
  }

  return params.toString();
}

function extractCommentData(commentNode: any): Comment {
  const comment: Comment = {
    id: commentNode.id || '',
    text: commentNode.body?.text || null,
    author: {
      id: commentNode.author?.id || '',
      name: commentNode.author?.name || commentNode.user?.name || '',
      profilePicture:
        commentNode.author?.profile_picture_depth_0?.uri ||
        commentNode.user?.profile_picture?.uri ||
        null,
    },
    timestamp: commentNode.created_time
      ? new Date(commentNode.created_time * 1000).toISOString()
      : null,
    reactions: [],
    totalReactions: 0,
    replies: [],
    totalReplies: commentNode.feedback?.replies_fields?.total_count || 0,
    attachments: commentNode.attachments || [],
    depth: commentNode.depth || 0,
  };

  // Extract reactions if available
  if (commentNode.feedback?.reaction_count?.count) {
    comment.totalReactions = commentNode.feedback.reaction_count.count;
  }

  if (commentNode.feedback?.top_reactions?.edges) {
    comment.reactions = commentNode.feedback.top_reactions.edges.map(
      (edge: any) => ({
        type: edge.node?.localized_name || edge.node?.key || 'unknown',
        count: edge.reaction_count || 0,
      })
    );
  }

  return comment;
}

async function fetchCommentsForPost(
  feedbackId: string,
  cursor: string | null = null
): Promise<Comment[]> {
  const obj = {
    fb_api_caller_class: 'RelayModern',
    fb_api_req_friendly_name: 'CommentListComponentsRootQuery',
    variables: {
      commentsAfterCount: -1,
      commentsAfterCursor: cursor,
      commentsBeforeCount: null,
      commentsBeforeCursor: null,
      commentsIntentToken: null,
      feedLocation: 'POST_PERMALINK_DIALOG',
      focusCommentID: null,
      scale: 1,
      useDefaultActor: false,
      id: feedbackId,
    },
    server_timestamps: 'true',
    doc_id: '24269275729371154',
  };
  console.log(JSON.stringify(obj, null, 2));

  try {
    const response = await fetch('https://www.facebook.com/api/graphql/', {
      headers: {
        accept: '*/*',
        'content-type': 'application/x-www-form-urlencoded',
        'sec-fetch-site': 'same-origin',
      },
      body: objectToQueryString(obj),
      method: 'POST',
    });

    const data: any = await response.json();
    const comments: Comment[] = [];

    await fs.writeFile(`test.json`, JSON.stringify(data, null, 2));

    if (
      data?.data?.node?.comment_rendering_instance_for_feed_location?.comments
        ?.edges
    ) {
      const edges =
        data.data.node.comment_rendering_instance_for_feed_location.comments
          .edges;

      for (const edge of edges) {
        const comment = extractCommentData(edge.node);
        comments.push(comment);
      }
    }

    // Check if there are more comments to fetch
    const pageInfo =
      data?.data?.node?.comment_rendering_instance_for_feed_location?.comments
        ?.page_info;

    console.log({ pageInfo });
    if (pageInfo?.has_next_page && pageInfo?.end_cursor) {
      const nextComments = await fetchCommentsForPost(
        feedbackId,
        pageInfo.end_cursor
      );
      comments.push(...nextComments);
    }

    console.log(comments.length);

    return comments;
  } catch (error) {
    console.error(`Error fetching comments for ${feedbackId}:`, error);
    return [];
  }
}

async function scrapeCommentsForAllPosts(): Promise<void> {
  try {
    // Read the existing posts data
    const postsData = await fs.readFile('all_facebook_posts.json', 'utf-8');
    const posts = JSON.parse(postsData);

    const postsWithComments: PostWithComments[] = [];

    for (const post of posts) {
      if (!post.feedbackId) {
        console.log(`Skipping post ${post.postId} - no feedbackId`);
        continue;
      }

      console.log(`Fetching comments for post ${post.postId}...`);

      const comments = await fetchCommentsForPost(post.feedbackId);

      const postWithComments: PostWithComments = {
        postId: post.postId,
        feedbackId: post.feedbackId,
        comments,
        totalComments: comments.length,
      };

      postsWithComments.push(postWithComments);

      // Save individual post comments
      await fs.writeFile(
        `comments_${post.postId}.json`,
        JSON.stringify(postWithComments, null, 2)
      );

      console.log(`Saved ${comments.length} comments for post ${post.postId}`);

      // Add a small delay to avoid rate limiting
      await new Promise((resolve) => setTimeout(resolve, 1000));
      break;
    }

    // Save all comments data
    await fs.writeFile(
      'all_posts_with_comments.json',
      JSON.stringify(postsWithComments, null, 2)
    );

    console.log(
      `Completed scraping comments for ${postsWithComments.length} posts`
    );
  } catch (error) {
    console.error('Error scraping comments:', error);
  }
}

// Enhanced function to merge posts with their comments
async function mergePostsWithComments(): Promise<void> {
  try {
    const postsData = await fs.readFile('all_facebook_posts.json', 'utf-8');
    const posts = JSON.parse(postsData);

    const commentsData = await fs.readFile(
      'all_posts_with_comments.json',
      'utf-8'
    );
    const postsWithComments = JSON.parse(commentsData);

    // Create a map for quick lookup
    const commentsMap = new Map();
    postsWithComments.forEach((postComments: PostWithComments) => {
      commentsMap.set(postComments.postId, postComments.comments);
    });

    // Merge posts with comments
    const mergedData = posts.map((post: any) => ({
      ...post,
      comments: commentsMap.get(post.postId) || [],
      commentsCount: (commentsMap.get(post.postId) || []).length,
    }));

    await fs.writeFile(
      'complete_posts_with_comments.json',
      JSON.stringify(mergedData, null, 2)
    );

    console.log('Successfully merged posts with comments');
  } catch (error) {
    console.error('Error merging posts with comments:', error);
  }
}

// Function to generate summary statistics
async function generateCommentsSummary(): Promise<void> {
  try {
    const data = await fs.readFile(
      'complete_posts_with_comments.json',
      'utf-8'
    );
    const posts = JSON.parse(data);

    const summary = {
      totalPosts: posts.length,
      postsWithComments: posts.filter((p: any) => p.comments.length > 0).length,
      totalComments: posts.reduce(
        (sum: number, p: any) => sum + p.comments.length,
        0
      ),
      averageCommentsPerPost: 0,
      topCommentedPosts: [] as any[],
      commentsByLanguage: {} as Record<string, number>,
      topCommenters: {} as Record<string, number>,
    };

    summary.averageCommentsPerPost = summary.totalComments / summary.totalPosts;

    // Top commented posts
    summary.topCommentedPosts = posts
      .sort((a: any, b: any) => b.comments.length - a.comments.length)
      .slice(0, 5)
      .map((p: any) => ({
        postId: p.postId,
        commentsCount: p.comments.length,
        postText: p.postText?.substring(0, 100) + '...',
      }));

    // Count comments by author
    posts.forEach((post: any) => {
      post.comments.forEach((comment: any) => {
        const author = comment.author.name;
        summary.topCommenters[author] =
          (summary.topCommenters[author] || 0) + 1;
      });
    });

    // Convert to sorted array
    const topCommentersArray = Object.entries(summary.topCommenters)
      .sort(([, a], [, b]) => (b as number) - (a as number))
      .slice(0, 10)
      .map(([name, count]) => ({ name, count }));

    const finalSummary = {
      ...summary,
      topCommenters: topCommentersArray,
    };

    await fs.writeFile(
      'comments_summary.json',
      JSON.stringify(finalSummary, null, 2)
    );

    console.log('\n=== COMMENTS SUMMARY ===');
    console.log(`Total Posts: ${summary.totalPosts}`);
    console.log(`Posts with Comments: ${summary.postsWithComments}`);
    console.log(`Total Comments: ${summary.totalComments}`);
    console.log(
      `Average Comments per Post: ${summary.averageCommentsPerPost.toFixed(2)}`
    );
    console.log('\nTop Commented Posts:');
    summary.topCommentedPosts.forEach((post, i) => {
      console.log(
        `${i + 1}. ${post.commentsCount} comments - ${post.postText}`
      );
    });
    console.log('\nTop Commenters:');
    topCommentersArray.slice(0, 5).forEach((commenter, i) => {
      console.log(`${i + 1}. ${commenter.name}: ${commenter.count} comments`);
    });
  } catch (error) {
    console.error('Error generating summary:', error);
  }
}

// Run the scraper
scrapeCommentsForAllPosts()
  .then(() => {
    console.log('Comment scraping completed');
    return mergePostsWithComments();
  })
  .then(() => {
    console.log('Data merging completed');
    return generateCommentsSummary();
  })
  .then(() => {
    console.log('Summary generation completed');
  });
