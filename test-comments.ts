import fs from 'fs/promises';

function objectToQueryString(obj) {
  const params = new URLSearchParams();

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];

      // Handle nested objects (e.g., JSON strings like `variables`)
      if (typeof value === 'object') {
        params.append(key, JSON.stringify(value));
      } else {
        params.append(key, value);
      }
    }
  }

  return params.toString(); // Outputs: key1=value1&key2=value2...
}

const obj = {
  fb_api_caller_class: 'RelayModern',
  fb_api_req_friendly_name: 'CommentListComponentsRootQuery',
  variables: JSON.stringify({
    commentsIntentToken: 'RANKED_UNFILTERED_CHRONOLOGICAL_REPLIES_INTENT_V1',
    // feedLocation: 'DEDICATED_COMMENTING_SURFACE',
    scale: 1,
    useDefaultActor: false,
    id: 'ZmVlZGJhY2s6NzEzNDM2ODU0OTc4NTM2', // feedback_id from the first post
  }),
  server_timestamps: 'true',
  doc_id: '24269275729371154',
};

fetch('https://www.facebook.com/api/graphql/', {
  headers: {
    accept: '*/*',
    'content-type': 'application/x-www-form-urlencoded',
    'sec-fetch-site': 'same-origin',
  },
  referrer: 'https://www.facebook.com/abdurrahmantalha.dev/',
  body: objectToQueryString(obj),
  method: 'POST',
})
  .then((res) => res.json())
  .then(
    async (data) =>
      await fs.writeFile('comments-test-data.json', JSON.stringify(data, null, 2))
  );
