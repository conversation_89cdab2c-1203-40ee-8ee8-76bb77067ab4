[{"data": {"node": {"__typename": "User", "id": "100089366829069", "timeline_list_feed_units": {"edges": [{"node": {"__typename": "Story", "__isFeedUnit": "Story", "debug_info": null, "id": "UzpfSTEwMDA4OTM2NjgyOTA2OTo3MTM0MzY4NTQ5Nzg1MzY6NzEzNDM2ODU0OTc4NTM2", "sponsored_data": null, "daspo_sto": null, "feedback": {"associated_group": null, "id": "ZmVlZGJhY2s6NzEzNDM2ODU0OTc4NTM2"}, "is_story_civic": null, "cache_id": "-6581904176457036805", "matched_terms": [], "post_id": "713436854978536", "cix_screen": null, "attachments": [{"media": {"__typename": "Photo", "__isNode": "Photo", "id": "713436824978539"}, "styles": {"__typename": "StoryAttachmentPhotoStyleRenderer", "__isStoryAttachmentStyleRendererUnion": "StoryAttachmentPhotoStyleRenderer", "is_prod_eligible": true, "attachment": {"media": {"__typename": "Photo", "__isMedia": "Photo", "accent_color": "FFDFCEBC", "viewer_image": {"height": 1280, "width": 960}, "photo_product_tags": [], "focus": {"x": 0.5, "y": 0.33}, "photo_image": {"uri": "https://scontent.fdac145-1.fna.fbcdn.net/v/t39.30808-6/509600711_713436828311872_7457334169256797299_n.jpg?stp=dst-jpg_p526x296_tt6&_nc_cat=105&ccb=1-7&_nc_sid=833d8c&_nc_ohc=EMr3XDCNE34Q7kNvwEJHwin&_nc_oc=AdlEo20DiHFyB-eYTeJAwbdw3LBDEtYtxodcRBCVdK3rb1N4Kb4bM7Leif5iXe6JKHo&_nc_zt=23&_nc_ht=scontent.fdac145-1.fna&_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&oh=00_AfRlIL36ILO6DQfljG0XsV_v-eWpwZXfwOrbW2uzBEdDbA&oe=687AFC26", "height": 701, "width": 526}, "accessibility_caption": "May be an image of 2 people, henna, flower, dais and wedding", "url": "https://www.facebook.com/photo.php?fbid=713436824978539&set=a.559526800369543&type=3", "id": "713436824978539", "feedback": {"can_viewer_comment": false, "id": "ZmVlZGJhY2s6NzEzNDM2ODU0OTc4NTM2"}, "__isNode": "Photo", "comet_product_tag_feed_overlay_renderer": null, "comet_product_tag_dot_hint_renderer": null, "creation_story": {"target_group": null, "id": "UzpfSTEwMDA4OTM2NjgyOTA2OTo3MTM0MzY4NTQ5Nzg1MzY6NzEzNDM2ODU0OTc4NTM2"}}}, "__module_operation_CometFeedStoryAttachmentMatchRenderer_attachment": {"__dr": "CometFeedStoryPhotoAttachmentStyle_styleTypeRenderer$normalization.graphql"}, "__module_component_CometFeedStoryAttachmentMatchRenderer_attachment": {"__dr": "CometFeedStoryPhotoAttachmentStyle.react"}}}], "future_of_feed_info": {"should_reverse_message_and_attachment_position": true, "should_overlay_header": true, "aspect_ratio_update": -1, "web_reshare_variant": "NORMAL"}, "attached_story": null, "bumpers": null, "comet_sections": {"__typename": "CometStorySections", "content": {"__typename": "CometFeedStoryDefaultContentStrategy", "__isICometStorySection": "CometFeedStoryDefaultContentStrategy", "is_prod_eligible": true, "story": {"feedback": {"id": "ZmVlZGJhY2s6NzEzNDM2ODU0OTc4NTM2"}, "comet_sections": {"above_message": null, "info_icon": null, "attachment_overlay": null, "attached_story": null, "message": {"__typename": "CometFeedStoryDefaultMessageRenderingStrategy", "__isICometStorySection": "CometFeedStoryDefaultMessageRenderingStrategy", "is_prod_eligible": true, "story": {"is_text_only_story": false, "message": {"delight_ranges": [], "image_ranges": [], "inline_style_ranges": [], "aggregated_ranges": [], "ranges": [{"entity": {"__typename": "User", "__isEntity": "User", "__isActor": "User", "id": "100076214209550", "url": "https://www.facebook.com/afnanferdousi2006", "profile_url": "https://www.facebook.com/afnanferdousi2006", "short_name": "Afnan", "work_info": null, "__module_operation_CometTextWithEntitiesRelay_textWithEntities": {"__dr": "ProfileGeminiWeakReferenceLink_user$normalization.graphql"}, "__module_component_CometTextWithEntitiesRelay_textWithEntities": {"__dr": "ProfileGeminiWeakReferenceLink.react"}, "work_foreign_entity_info": null, "is_verified": false, "mobileUrl": "https://m.facebook.com/afnanferdousi2006/", "__isNode": "User"}, "entity_is_weak_reference": false, "length": 14, "offset": 592}, {"entity": {"__typename": "User", "__isEntity": "User", "__isActor": "User", "id": "pfbid0JgRZCwiCsZ3QrZ1brLEuWCvXiAnbhV8GrTXnjZ98Gv6Z2VkMJ2vUq5Nn2Wtsjma7l", "url": null, "profile_url": null, "short_name": "<PERSON><PERSON>", "work_info": null, "__module_operation_CometTextWithEntitiesRelay_textWithEntities": {"__dr": "ProfileGeminiWeakReferenceLink_user$normalization.graphql"}, "__module_component_CometTextWithEntitiesRelay_textWithEntities": {"__dr": "ProfileGeminiWeakReferenceLink.react"}, "work_foreign_entity_info": null, "is_verified": false, "mobileUrl": null, "__isNode": "User"}, "entity_is_weak_reference": false, "length": 16, "offset": 1176}, {"entity": {"__typename": "User", "__isEntity": "User", "__isActor": "User", "id": "pfbid0265UndGNTrxyrQnzD7dKj6UBjsuBWgFRLDUYRsmMkyQ61Mf4WFjQLnR2fP67gT3Qal", "url": "https://www.facebook.com/knockoutez", "profile_url": "https://www.facebook.com/knockoutez", "short_name": "<PERSON><PERSON><PERSON><PERSON>", "work_info": null, "__module_operation_CometTextWithEntitiesRelay_textWithEntities": {"__dr": "ProfileGeminiWeakReferenceLink_user$normalization.graphql"}, "__module_component_CometTextWithEntitiesRelay_textWithEntities": {"__dr": "ProfileGeminiWeakReferenceLink.react"}, "work_foreign_entity_info": null, "is_verified": false, "mobileUrl": "https://m.facebook.com/knockoutez/", "__isNode": "User"}, "entity_is_weak_reference": false, "length": 11, "offset": 1957}, {"entity": {"__typename": "User", "__isEntity": "User", "__isActor": "User", "id": "100079809223454", "url": "https://www.facebook.com/towhid.khan.232618", "profile_url": "https://www.facebook.com/towhid.khan.232618", "short_name": "<PERSON><PERSON><PERSON><PERSON>", "work_info": null, "__module_operation_CometTextWithEntitiesRelay_textWithEntities": {"__dr": "ProfileGeminiWeakReferenceLink_user$normalization.graphql"}, "__module_component_CometTextWithEntitiesRelay_textWithEntities": {"__dr": "ProfileGeminiWeakReferenceLink.react"}, "work_foreign_entity_info": null, "is_verified": false, "mobileUrl": "https://m.facebook.com/towhid.khan.232618/", "__isNode": "User"}, "entity_is_weak_reference": false, "length": 11, "offset": 2324}], "color_ranges": [], "text": "For the past 2 days my sister got married. For some reason my brain still hasn't fully processed the fact that she is actually married now and now will live far away.\n\nThe past month might have been one of my most stressful months I have ever experienced too, going to tailors, buying clothes for everybody, changing the same panjabi for the hundredth time lol but this is probably going to be one of the most memorable months in my life too.\n\nUp until now I have never really taken part in organizing weddings since I always had uncles or aunts to help with almost everything but since it's <PERSON><PERSON>nan <PERSON> wedding I had to be wayyyy more involved. This is probably one of the most confusing things I have ever faced, there were SOOOOOOO many traditions that I have never even heard of (<PERSON><PERSON><PERSON> made fun of me every single time) but I still had sooo much fun lol.\n\nMost of my cousins live outside of sylhet(rajshahi and Dhaka) so most could only come for a few days at max. The days where everyone was some of the most fun j have ever had during the entire wedding, all of us were playing uno, doing Mehdi (they even let me do some Mehdi designs lmaoo) and so much other stuff! <PERSON><PERSON> also danced for the first time ever, everyone was so surprised on how well she danced haha.\n\nSince so many people came from outside of Dhaka I was basically constantly being called by someone for directions or to pick up deliveries. I was so surprised that I was even able to get so many people home safely without causing an accident or getting someone lost. I have an absolutely terrible sense of direction and will get lost very fast so even <PERSON><PERSON><PERSON> was a bit surprised.\n\nAlmost everyone was extremely sleep deprived for the past 3 or so days and barely awake the entire time. I'm surprised how most of us only had like 1-2 cups of tea of coffee a day. I was expecting so much more considering the fact almost everyone had like max 3 hours of sleep lol.\n\nAnyway, Towhid Khan is the husband btw. Tho we only meet a few months ago because of an event that both of us attended. We have gotten a little bit close and even vc sometimes which is always a lot of fun since most of the time we just watch either play games sometimes.\n\nJokes aside, This marriage will be one of the most precious events that will ever happen to me. I hope Towhid Khan and Afnan a really long lasting marriage that won't ever end :v (if you buy me chocolates it will never end uwu). \n\nI'm probably going to be third wheeling them for a little bit longer and eat a ton of free food uwu. I can't imagine anyone better for Afnan than towhid vaia, they are extremely similar and have really similar ways of handling some things which I really love :V. \n\nShe probably won't be around the house as much and her presence will be much less and the house will start feeling a bit more empty. After I started living at Dhaka the house always feels a bit empty according to my parents (my sisters love me too much to ever say that to my face). I'm sure she is going to come to sylhet often and visit everyone, but her presence will probably be missed around the house. When I'm at Dhaka the house is basically empty now, only 4 people at home now.\n\nAt the end of the day I love her (as long as she buys me jhalmuri) and is my therapist so I will miss her a bit but I'm sure that she will be extremely happy with towhid vaia (rip towhid vaia) and that's all that matters.\n\nAfnan is going to bully me about this post so much haha"}, "message_truncation_line_limit": null, "sponsored_data": null, "attachments": [{"action_links": []}], "id": "UzpfSTEwMDA4OTM2NjgyOTA2OTo3MTM0MzY4NTQ5Nzg1MzY6NzEzNDM2ODU0OTc4NTM2"}, "__module_operation_CometFeedStoryMessageMatchRenderer_story": {"__dr": "CometFeedStoryDefaultMessageRenderingStrategy_message$normalization.graphql"}, "__module_component_CometFeedStoryMessageMatchRenderer_story": {"__dr": "CometFeedStoryDefaultMessageRenderingStrategy.react"}}, "message_suffix": null, "message_container": {"__typename": "CometFeedStoryMessageContainerRenderingStrategy", "__isICometStorySection": "CometFeedStoryMessageContainerRenderingStrategy", "is_prod_eligible": true, "story": {"message": {"text": "For the past 2 days my sister got married. For some reason my brain still hasn't fully processed the fact that she is actually married now and now will live far away.\n\nThe past month might have been one of my most stressful months I have ever experienced too, going to tailors, buying clothes for everybody, changing the same panjabi for the hundredth time lol but this is probably going to be one of the most memorable months in my life too.\n\nUp until now I have never really taken part in organizing weddings since I always had uncles or aunts to help with almost everything but since it's <PERSON><PERSON>nan <PERSON> wedding I had to be wayyyy more involved. This is probably one of the most confusing things I have ever faced, there were SOOOOOOO many traditions that I have never even heard of (<PERSON><PERSON><PERSON> made fun of me every single time) but I still had sooo much fun lol.\n\nMost of my cousins live outside of sylhet(rajshahi and Dhaka) so most could only come for a few days at max. The days where everyone was some of the most fun j have ever had during the entire wedding, all of us were playing uno, doing Mehdi (they even let me do some Mehdi designs lmaoo) and so much other stuff! <PERSON><PERSON> also danced for the first time ever, everyone was so surprised on how well she danced haha.\n\nSince so many people came from outside of Dhaka I was basically constantly being called by someone for directions or to pick up deliveries. I was so surprised that I was even able to get so many people home safely without causing an accident or getting someone lost. I have an absolutely terrible sense of direction and will get lost very fast so even <PERSON><PERSON><PERSON> was a bit surprised.\n\nAlmost everyone was extremely sleep deprived for the past 3 or so days and barely awake the entire time. I'm surprised how most of us only had like 1-2 cups of tea of coffee a day. I was expecting so much more considering the fact almost everyone had like max 3 hours of sleep lol.\n\nAnyway, Towhid Khan is the husband btw. Tho we only meet a few months ago because of an event that both of us attended. We have gotten a little bit close and even vc sometimes which is always a lot of fun since most of the time we just watch either play games sometimes.\n\nJokes aside, This marriage will be one of the most precious events that will ever happen to me. I hope Towhid Khan and Afnan a really long lasting marriage that won't ever end :v (if you buy me chocolates it will never end uwu). \n\nI'm probably going to be third wheeling them for a little bit longer and eat a ton of free food uwu. I can't imagine anyone better for Afnan than towhid vaia, they are extremely similar and have really similar ways of handling some things which I really love :V. \n\nShe probably won't be around the house as much and her presence will be much less and the house will start feeling a bit more empty. After I started living at Dhaka the house always feels a bit empty according to my parents (my sisters love me too much to ever say that to my face). I'm sure she is going to come to sylhet often and visit everyone, but her presence will probably be missed around the house. When I'm at Dhaka the house is basically empty now, only 4 people at home now.\n\nAt the end of the day I love her (as long as she buys me jhalmuri) and is my therapist so I will miss her a bit but I'm sure that she will be extremely happy with towhid vaia (rip towhid vaia) and that's all that matters.\n\nAfnan is going to bully me about this post so much haha"}, "referenced_sticker": null, "attachments": [{"style_list": ["photo", "fallback"]}], "text_format_metadata": null, "comet_sections": {"message": null}, "id": "UzpfSTEwMDA4OTM2NjgyOTA2OTo3MTM0MzY4NTQ5Nzg1MzY6NzEzNDM2ODU0OTc4NTM2"}, "__module_operation_CometFeedStoryMessageContainerMatchRenderer_story": {"__dr": "CometFeedStoryMessageContainerRenderingStrategy_messageContainer$normalization.graphql"}, "__module_component_CometFeedStoryMessageContainerMatchRenderer_story": {"__dr": "CometFeedStoryMessageContainerRenderingStrategy.react"}}, "message_sticker": null, "aggregated_stories": null}, "encrypted_tracking": "AZXre8P5utXTTtqwzBIbj0ekd9YnJ3TrzkHGjKn3d-B5SpmCclHzKr17wwftCTAulcZ0lBhOCVzB53DPQePxBli4rwbEN8t9H5njiRL-E2ogLATkZUxjWRbCiOqxe3UuptWsML4zSTZsREbwTL9B0dMCrk4Y2cMqety3HtR3WFyzgDPA_8E3n46yXgM6B8CuYZF9kF1gyOm0spEYVxh8-fcaJ-YplfgIJOShyj0iyaBRkhhL-igEr22vKwHXhZ7Fg1qNmAdSoHnyAPd-sMyVVwJpBzt9MbGp-ReKPwNJWZJgGisbv2pJ3Hieg3VQLFMc3PuCMfg4zXCSpKXq2bHsz9s6XNpGc6nHCvMNFZOLcYOLWEztZA5zTAWY6Ea-OllMYoSONNa9oQftlj3MZbyPqVUn-gDMhcgGEpFcaZqWz1Do_2vcrUwHtiXd_fqVMbgJEuYCAgc-9Xxt2Tbp_tePOgIzpysviRRZlvgf75UEMJwlhcAZJ5Q81mCqsGHEXwhvce8VNYfT714d39hljxWnqHUJsVlqvmK6PE5boSDZjjeGTbsoClanx_pBWvUgLYTw4uDFuq-JC30eBHalHJxjG10rY9AckEItcJE4AOw8FQadRVdC0Kd1HbUlZxmd_kC5wbpSDtIce9LC9mmPihlij4ewHYnfPtNbn6kvcT9Z4YcvsXGEaDGPwRa0HEc-Ql0Zqe7VoQ-eZfyxG8Y00PbOj2kb657ajmquY_nkkuEV-MjOF6FNW9gG9tI1Fw2NMQb44reSVtuH-q4E7sPm58QJYGEQqVzR7NA8U0sx1f0O5yXeWN8YwY4kqkz0_adr7z021vtmWGAAA9QGqe_xgleSuyCG8lPUTGR494jKZcNH9eLDVBHaWQgx0Sce058H3qcoswvephqIscVr2CD4kJmTwve1_24EE5oRgyvZjzltUtDqRO-R_HstcxIeY-y9-aRPu3N58pw0am_e92uN2wTQq4FJ7EIynkidMjA9h6MEQdY94DrxAV-UKt0yKxKYxTJtP_l1cQSQDjVBnVWPfbpAjAK9Lcsrg9LLjMzVLw9owpvVNT5tDCzCX24XF-8KGGCwHBSaYAzGm7q8Tfvnvpk7_ZwgHeS4vDU0KGooSyX9rfU6RYAJd-57vTjBCgutXpeARIDOXYjQXa36N0yYufYSdAKhMcq_JVsRYd2q_M1c3tAjvIsYEoVCO448zcSe5pBzcNpoJY5g5ZFGyLNoJPAU_i7EfTq-a1zSxS4QQGe3mjW-80Nw32nVfqMKWKhaIH6SspQGaU7RNqM-EmJo4qUCZAh7-kXdik1LJNgqh8Hsl0oLi4tgOjnY7lx3xNjs7MyyowAh0IBu7CW_FqalIWg8MgX-6Th4jt66aWXHmYmZRlF5xJzbruqRgHAvfWrTjCyD3cAao6i_xzuPS59F81giCWUWdEQKXb-SE0X1a92MD6aNAm381eIYmWRsRFn5TUbsP1nrer0OgcmmzTCnu1xtXHLWVONwClUIQ_XYSFiNBVGhlOnZeKXPtHPq4bBL3pD-S8LUmLO7fCfGoTzdu6WHwuVJOyuXpu2tzuU_EZ4WXkigTdPdGPkM7ty2nuxJ7h100rfFaU5AxPdj9Vn52gB1m57q4WqKSr4iqXXTrGjR5tRVdU65ShHR_sP0QKcgyDd9hhe6eVDLfbbwivD1NiyHQJtvii-X8s-qaxJ0_VV173R_AsRom_6Jd93EesIno0i5YqzDF4DuFQlVg1H-UDnmy7qhp8NcGA1AQsvJYEils6sfp4Fg_bU3w7_NsDy8lAoUCy8WYBX0eCS65diErCsqGMkAYOOBoN3mgyTqDYClh-gAvyb-uZsxOhgyl0J0nIXuT_IJovny2ww5k6G8q0x0ZiezfGP_Ta4SMQD9OBGnJRyzI_0JrFMFugQHSqAx0UVu6vQ", "attachments": [{"deduplication_key": "2ad1e1b47c18f4d01d76357e4f30ab28", "target": {"__typename": "Photo", "id": "713436824978539"}, "__typename": "StoryAttachment", "style_list": ["photo", "fallback"], "styles": {"__typename": "StoryAttachmentPhotoStyleRenderer", "__isStoryAttachmentStyleRendererUnion": "StoryAttachmentPhotoStyleRenderer", "is_prod_eligible": true, "attachment": {"media": {"__typename": "Photo", "__isMedia": "Photo", "accent_color": "FFDFCEBC", "viewer_image": {"height": 1280, "width": 960}, "photo_product_tags": [], "focus": {"x": 0.5, "y": 0.33}, "photo_image": {"uri": "https://scontent.fdac145-1.fna.fbcdn.net/v/t39.30808-6/509600711_713436828311872_7457334169256797299_n.jpg?stp=dst-jpg_p526x296_tt6&_nc_cat=105&ccb=1-7&_nc_sid=833d8c&_nc_ohc=EMr3XDCNE34Q7kNvwEJHwin&_nc_oc=AdlEo20DiHFyB-eYTeJAwbdw3LBDEtYtxodcRBCVdK3rb1N4Kb4bM7Leif5iXe6JKHo&_nc_zt=23&_nc_ht=scontent.fdac145-1.fna&_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&oh=00_AfRlIL36ILO6DQfljG0XsV_v-eWpwZXfwOrbW2uzBEdDbA&oe=687AFC26", "height": 701, "width": 526}, "accessibility_caption": "May be an image of 2 people, henna, flower, dais and wedding", "url": "https://www.facebook.com/photo.php?fbid=713436824978539&set=a.559526800369543&type=3", "id": "713436824978539", "feedback": {"can_viewer_comment": false, "id": "ZmVlZGJhY2s6NzEzNDM2ODU0OTc4NTM2"}, "__isNode": "Photo", "comet_product_tag_feed_overlay_renderer": null, "comet_product_tag_dot_hint_renderer": null, "creation_story": {"target_group": null, "id": "UzpfSTEwMDA4OTM2NjgyOTA2OTo3MTM0MzY4NTQ5Nzg1MzY6NzEzNDM2ODU0OTc4NTM2"}}}, "__module_operation_CometFeedStoryAttachmentMatchRenderer_attachment": {"__dr": "CometFeedStoryPhotoAttachmentStyle_styleTypeRenderer$normalization.graphql"}, "__module_component_CometFeedStoryAttachmentMatchRenderer_attachment": {"__dr": "CometFeedStoryPhotoAttachmentStyle.react"}}, "throwbackStyles": null, "comet_footer_renderer": null, "comet_footer_disclaimer_renderer": null, "media": {"__typename": "Photo", "__isNode": "Photo", "id": "713436824978539"}, "all_subattachments": {"nodes": []}}], "future_of_feed_info": {"dominant_readable_color": "#AA3336"}, "sponsored_data": null, "daspo_sto": null, "text_format_metadata": null, "post_id": "713436854978536", "actors": [{"__typename": "User", "id": "100089366829069", "name": "<PERSON><PERSON>", "__isEntity": "User", "url": "https://www.facebook.com/abdurrahmantalha.dev"}], "message": {"__typename": "TextWithEntities", "text": "For the past 2 days my sister got married. For some reason my brain still hasn't fully processed the fact that she is actually married now and now will live far away.\n\nThe past month might have been one of my most stressful months I have ever experienced too, going to tailors, buying clothes for everybody, changing the same panjabi for the hundredth time lol but this is probably going to be one of the most memorable months in my life too.\n\nUp until now I have never really taken part in organizing weddings since I always had uncles or aunts to help with almost everything but since it's <PERSON><PERSON>nan <PERSON> wedding I had to be wayyyy more involved. This is probably one of the most confusing things I have ever faced, there were SOOOOOOO many traditions that I have never even heard of (<PERSON><PERSON><PERSON> made fun of me every single time) but I still had sooo much fun lol.\n\nMost of my cousins live outside of sylhet(rajshahi and Dhaka) so most could only come for a few days at max. The days where everyone was some of the most fun j have ever had during the entire wedding, all of us were playing uno, doing Mehdi (they even let me do some Mehdi designs lmaoo) and so much other stuff! <PERSON><PERSON> also danced for the first time ever, everyone was so surprised on how well she danced haha.\n\nSince so many people came from outside of Dhaka I was basically constantly being called by someone for directions or to pick up deliveries. I was so surprised that I was even able to get so many people home safely without causing an accident or getting someone lost. I have an absolutely terrible sense of direction and will get lost very fast so even <PERSON><PERSON><PERSON> was a bit surprised.\n\nAlmost everyone was extremely sleep deprived for the past 3 or so days and barely awake the entire time. I'm surprised how most of us only had like 1-2 cups of tea of coffee a day. I was expecting so much more considering the fact almost everyone had like max 3 hours of sleep lol.\n\nAnyway, Towhid Khan is the husband btw. Tho we only meet a few months ago because of an event that both of us attended. We have gotten a little bit close and even vc sometimes which is always a lot of fun since most of the time we just watch either play games sometimes.\n\nJokes aside, This marriage will be one of the most precious events that will ever happen to me. I hope Towhid Khan and Afnan a really long lasting marriage that won't ever end :v (if you buy me chocolates it will never end uwu). \n\nI'm probably going to be third wheeling them for a little bit longer and eat a ton of free food uwu. I can't imagine anyone better for Afnan than towhid vaia, they are extremely similar and have really similar ways of handling some things which I really love :V. \n\nShe probably won't be around the house as much and her presence will be much less and the house will start feeling a bit more empty. After I started living at Dhaka the house always feels a bit empty according to my parents (my sisters love me too much to ever say that to my face). I'm sure she is going to come to sylhet often and visit everyone, but her presence will probably be missed around the house. When I'm at Dhaka the house is basically empty now, only 4 people at home now.\n\nAt the end of the day I love her (as long as she buys me jhalmuri) and is my therapist so I will miss her a bit but I'm sure that she will be extremely happy with towhid vaia (rip towhid vaia) and that's all that matters.\n\nAfnan is going to bully me about this post so much haha"}, "ghl_mocked_encrypted_link": null, "ghl_label_mocked_cta_button": null, "wwwURL": "https://www.facebook.com/abdurrahmantalha.dev/posts/pfbid02qeGraPg1YwAjVQADkF2CakhDPvtAo3593cYxT9m32RTzEhnLWCXvawM972Y5deUzl", "target_group": null, "attached_story": null, "id": "UzpfSTEwMDA4OTM2NjgyOTA2OTo3MTM0MzY4NTQ5Nzg1MzY6NzEzNDM2ODU0OTc4NTM2"}, "__module_operation_CometFeedStoryContentMatchRenderer_story": {"__dr": "CometFeedStoryDefaultContentStrategy_content$normalization.graphql"}, "__module_component_CometFeedStoryContentMatchRenderer_story": {"__dr": "CometFeedStoryDefaultContentStrategy.react"}}, "layout": {"__typename": "CometStoryDefaultLayoutStrategy", "__isICometStorySection": "CometStoryDefaultLayoutStrategy", "is_prod_eligible": true, "__module_operation_CometFeedStoryLayoutMatchRenderer_story": {"__dr": "CometFeedStoryDefaultLayoutStrategy_layout$normalization.graphql"}, "__module_component_CometFeedStoryLayoutMatchRenderer_story": {"__dr": "CometFeedStoryDefaultLayoutStrategy.react"}}, "copyright_violation_header": null, "header": null, "context_layout": {"__typename": "CometFeedStoryDefaultContextLayoutStrategy", "__isICometStorySection": "CometFeedStoryDefaultContextLayoutStrategy", "is_prod_eligible": true, "local_alerts_story_menu_promotion": null, "story": {"id": "UzpfSTEwMDA4OTM2NjgyOTA2OTo3MTM0MzY4NTQ5Nzg1MzY6NzEzNDM2ODU0OTc4NTM2", "debug_info": null, "serialized_frtp_identifiers": null, "can_viewer_see_menu": false, "comet_sections": {"actor_photo": {"__typename": "CometFeedStoryActorPhotoStrategy", "__isICometStorySection": "CometFeedStoryActorPhotoStrategy", "is_prod_eligible": true, "story": {"actors": [{"__typename": "User", "__isActor": "User", "id": "100089366829069", "__isEntity": "User", "url": "https://www.facebook.com/abdurrahmantalha.dev", "work_foreign_entity_info": null, "work_info": null, "story_bucket": {"nodes": [{"should_show_close_friend_badge": false, "id": "104137932575101", "first_story_to_show": null}]}, "live_video_for_comet_live_ring": null, "profile_url": "https://www.facebook.com/abdurrahmantalha.dev", "name": "<PERSON><PERSON>", "profile_picture": {"uri": "https://scontent.fdac145-1.fna.fbcdn.net/v/t39.30808-1/472737163_585277764461113_2559430034335871640_n.jpg?stp=cp0_dst-jpg_s40x40_tt6&_nc_cat=108&ccb=1-7&_nc_sid=1d2534&_nc_ohc=u_KqJyrAl9EQ7kNvwHO6rR-&_nc_oc=AdlrwCu--dwJXhVx1DMmRzd9steZduRwZyiqz7LAlrVjivJiYCCX20S7ngNOwPs2fjE&_nc_zt=24&_nc_ht=scontent.fdac145-1.fna&_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&oh=00_AfTVVIBc9LcIR2D8XXRKaTzIWg7eLIezbD0CoLV1kF27xw&oe=687AF2F5", "width": 40, "height": 40, "scale": 1}, "is_additional_profile_plus": false, "delegate_page": {"is_business_page_active": false, "id": "488294627707388"}}], "comet_sections": {"action_link": null}, "attachments": [{"action_links": []}], "sponsored_data": null, "daspo_sto": null, "id": "UzpfSTEwMDA4OTM2NjgyOTA2OTo3MTM0MzY4NTQ5Nzg1MzY6NzEzNDM2ODU0OTc4NTM2"}, "has_commerce_attachment": false, "__module_operation_CometFeedStoryActorPhotoSectionMatchRenderer_story": {"__dr": "CometFeedStoryActorPhotoStrategy_actorPhoto$normalization.graphql"}, "__module_component_CometFeedStoryActorPhotoSectionMatchRenderer_story": {"__dr": "CometFeedStoryActorPhotoStrategy.react"}}, "metadata": [{"__typename": "CometFeedStoryMinimizedTimestampStrategy", "__isICometStorySection": "CometFeedStoryMinimizedTimestampStrategy", "is_prod_eligible": true, "override_url": null, "video_override_url": null, "story": {"creation_time": 1750463530, "unpublished_content_type": "PUBLISHED", "url": "https://www.facebook.com/abdurrahmantalha.dev/posts/pfbid02qeGraPg1YwAjVQADkF2CakhDPvtAo3593cYxT9m32RTzEhnLWCXvawM972Y5deUzl", "ghl_label": null, "id": "UzpfSTEwMDA4OTM2NjgyOTA2OTo3MTM0MzY4NTQ5Nzg1MzY6NzEzNDM2ODU0OTc4NTM2"}, "__module_operation_CometFeedStoryMetadataSectionMatchRenderer_story": {"__dr": "CometFeedStoryMinimizedTimestampStrategy_timestamp$normalization.graphql"}, "__module_component_CometFeedStoryMetadataSectionMatchRenderer_story": {"__dr": "CometFeedStoryMinimizedTimestampStrategy.react"}}, {"__typename": "CometFeedStoryAudienceStrategy", "__isICometStorySection": "CometFeedStoryAudienceStrategy", "is_prod_eligible": true, "story": {"privacy_scope": {"icon_image": {"name": "everyone"}, "description": "Public"}, "id": "UzpfSTEwMDA4OTM2NjgyOTA2OTo3MTM0MzY4NTQ5Nzg1MzY6NzEzNDM2ODU0OTc4NTM2"}, "__module_operation_CometFeedStoryMetadataSectionMatchRenderer_story": {"__dr": "CometFeedStoryAudienceStrategy_audience$normalization.graphql"}, "__module_component_CometFeedStoryMetadataSectionMatchRenderer_story": {"__dr": "CometFeedStoryAudienceStrategy.react"}}], "title": {"__typename": "CometFeedStoryTitleWithActorStrategy", "__isICometStorySection": "CometFeedStoryTitleWithActorStrategy", "is_prod_eligible": true, "story": {"id": "UzpfSTEwMDA4OTM2NjgyOTA2OTo3MTM0MzY4NTQ5Nzg1MzY6NzEzNDM2ODU0OTc4NTM2", "actors": [{"__typename": "User", "name": "<PERSON><PERSON>", "id": "100089366829069", "__isActor": "User", "__isEntity": "User", "url": "https://www.facebook.com/abdurrahmantalha.dev", "work_foreign_entity_info": null, "work_info": null}], "collaborators": [], "title": null, "comet_sections": {"action_link": null, "follow_button": null, "badge": null}, "encrypted_tracking": "AZWoIo_bFmUjX28O-BoI6vA6DRClVP-zDDb-kg6AAONCFyU7R62OKsMnTFI0k3zUQyAOJapGS0bap0E8QyFsFrXEZcXxqaczkT6_IZzljB0KapMImOQSxbEaW2mqtLCwy2hXwuzEhDwZvdoKjt1ZSrlHNMdfG_GvFmzZeGCSTFzRkiYnWcXLdBJfzd6us011lo2XKWwOhI2JWFhKRO_mTS8VXq5GWZvOTuwEWpi7maSg_0zAnND4NeWtipJqYVUjj4fdFf95PNJ_eBCkDO6n29jNai7Sddpbn4G9KvdTmUTBsThTrbXtKQC37ZYV9prNN60Hz20usxJIIHyLYCMh5zeLqv1wngP_JnpFWQ9qoQikcqQzPJEXP5uZWDQt6rPIIET5MKnNpG227c80WFuqUd60V1-J5tM9WmWSRp01g3vOVP_4Zg0U0lceSfGMoMYa_Li9I9eTsb7adR4_0d1HnRID8Pi7GUQjruT4fsBrcGBA2h8AL42OwdqYM2Tz_PTZe0AvMjWBYgThg2PV8mLrnpd7_yT9KKY_Ijk-uy-OwQTnjDdJohTRH8rcn1H8228aWwf5x1Vr6W7l42IBeJOPoeE9UHSOWAevSNxB9K7acC2ddGzOPcxtOThkuP2YAsdxVjnL-DDKz0LqGJXy0eVNfFKp-KLHYg92bf-3Payz4S-4At9MeOhGuM-Fp0rekfsgRdZpWJKrddM1G4YEgdVrNUstev6HBkJ_2xR9yE3zWwhSP06w1l8Y5U7V1tVbkM-LoHM6n22CNhz_H0qebiPrG75M0uaHXPfgfmH1sQVW-FS2_G3xTKwuzw0lHhJrKRd5dx3fkDdB0aCVRIbLRlmaCUVXimUuy14KMNxQsrKnlF2ZIiXC5H6ki-R31RaUQamshn-FWTXox9c73E7vVUZlFuXs1Wl19DWJ5s9BqJArUEWqzk2dA3vryUMXhRPXQyBevZm4f-Wk_adnJHBvOAu2VpCxUXlZpyn-VBfO1P2tG3-J7ByLVL_pU3zByRFwkN2vI7EFnTaF5FJ7l2nVVh7T4z2qrsXClALE6pQxW4aQ5fCUSntv_Eh5WQ4dqnizz_wQlA1276RQFvhJV7gxKl8u3MoNTTVWVDAWCrsOb2kA2rQzOEZ4W02QQPiMkF0ZZf8IKdzWaDdWOwxC9TLAacwN3nhbNXbIiF84qognrFmhDo6NdJpgbu5_lxwkhwGXnL5QRj6oc7enw-Tt9EuODEAzHIUfgIzuSHjlHU06lnBHX3as2sGsixt5PRc6Iu9_PFwLpFCwk8VdMTiYiSS3yLvrmhUuoPYVUxMd8RI5leP3VkOr_Pm8HYXvXnqNFNIQ6SD2vAGU8ic4wG_u68_IR_npTux4Ot5ZvpRxzU3OaDFW5TrGQLnHn4-Th2T59xv5GK5KBncWjQyv_lZq16GvOjOcj-ovVQLfnH2d_x1eoOHmeb-24xOPrcVZwhqAHb45XS--WholCJAZQBsAW7hJ70pU_iEZWiLG7yBHZJ2PWLqyz4fSlBZlw7KDDy06yAasKEBsSZ31DZYR_NPXlYs0-8jcJ2cFvzJIsmW8JxfH1PpviLJzreIoNP-HRT6vEf4vBVqTk_SDxT9NH9oKymdVIREgvey0-U2ofSAEnqKt3e-hXaSUWjXQVC9I6AQVsGYHTMP1Qs-oFBY62wDIHr-sPiZZq8sne7bgUWo5p-I0m6getOV5m6gcEw_wwYGafZaZjT2VPEEW-JKyawLRilu73reR90S4XJTxzbiyLqX0RY1TdIJrLYPb5rEKiHW0Tk9T-azJowysCnT_wo7Fb0StkfPSG1SJu36A8HMXpAMj9jOziL_lM-gWn4LsojAzSkME5nU9y-ZTryLmyOVnCVtSYJGWOXRWxLDkwaW8gDNuv3SB4lgZLpM0EGnAt9Lt8ftF51zEncE"}, "__module_operation_CometFeedStoryTitleSectionMatchRenderer_story": {"__dr": "CometFeedStoryTitleWithActorStrategy_contextTitle$normalization.graphql"}, "__module_component_CometFeedStoryTitleSectionMatchRenderer_story": {"__dr": "CometFeedStoryTitleWithActorStrategy.react"}}}, "encrypted_tracking": "AZV9fjXJazV8HOfl4e_E1t3ZvtpO84i3NC0M8eqaVFGly27tE-JJ13uVLFwIgnB8Lm7CqJ5ZmXIkmWOWSl4llatNUcURz9Yuvb1dJE8Ieqvuop-BrNkfJgNuLrIkAmhEuZOXm9S0qs6UDJ4WuWKcOs5IPjm7jPkvhYobZ6gtZqqV6nfwEzvZnCAmq1hb2-glD__vww6DI2o7wb0YDMaGHguUHuD9rXny8uxbtT_buNrWcHhx1C9Y2mC2mSxolGYxhabLTr0Z-I9y3lYmHPIpJ9fiRe7e4YbTPeT2c46bJlT39lMOaOhVFGsb1QUrc8koiOp81z826mtJ4gPE9olBpn7J1VTcrg8xY_0TkPtoep1RX1dOOk2UR32q0Ha8Q2DRuT3SLeglElUl-LjToiru47GmbEVb04bOyhoJ6K7E4I9X_5BfFEuNFcYMXfNdL4VhjXODmz9UyogN3aq0vRjZrB7afRWRl2M3eKdKYlk5Ofk-TnsBXC8K4MqXUtPgXOC8WMHnO7A6xVhMw8KfudxiEie7oMICVoDgF_OWbXng74M4WaIDTDf8ojAULzQeEWMjHvKZ7pXQxBw2-Jv4pEg-1WcaEz24UEFRvIIQlPFeUPgVGn03Vm_2R5_lvEvj7qP03Fl-klkZjxOOXmYJNVh9MMRlF7h_sC7XLFLK4VsHGXtoCuFMs9A4oQ-CHyqt4imHRuuGICoC2YFaKuW3Kh4qESqLuSec6p-5O_jgeAhw_S1i6i7iB_idfXiOKdGp9HemrThS-YW9NXGwLmkInY-IOkF1r6cvSnB_WoypLEF8N1UMPay48RFK8mpKzZzhf9H3J7gKcd3Yz9QPGQCpBHalXKmzuBHC6l9Rh-HB0220ifuAcAPaSV2mkYWAooL-MHDo-KjOR16fh20-gnyVdBhouzLZUxc_3rLpIlg4uHFWyVEOJSQSEFH604tbmShF0LKh4ARHggdnUD2RHu5mrJEax_1kndELG6NuL7iSgxdCxZ22U7Oy_GgVTh7rB-C_g1HnpI_jL5T08cpkQO8-ZGxJtkwuaHRALc9r9ce4PxzxanpNXaLXWfrqAUIhydwCBH0xb6-_LXbGeCjFsverXg1mUsy3QaGBNBVpgIASOZAm-JhrIEb-P7dkjqjwLsGRgOPhecCrtDlkLIu4p9w3VHx1ik327ryGyD065orbHKrcTKCZkUKmTc9YvA0R_V_khtS_NMq_fnrjmnAG_-wh7-OE2Aa5TWCRSIuYkoWBv_w-DFI61FglXwQrj1zmF9HOXf90dRcW3rL9FQCgmOVklgF5JGQQ82PCmk2RwIKFpwrK2QArMhRa9YMNkFl_ttOPA0hL1b3GKOWL-pgFOS2LMrJHMVTyRKIzJ8hCOpI9tlCd4yrhaCU8OkQb-i141WwkCJaCRJjF1tGC5VaeePnnecK6qXsQMOvEb8-rZOj6tSDYP6x6uDQPCqxq7iWadwygagdfvVkQF-XkysojTMMKgVop4fWTCUR_45d5Um8EhWrfwP7rQcmI8piKVVrFTF9dItU8x4_fkAQgbjEAokWiuRmQGdvJ3e70NpDcW3aLjyXKyKwbQ5R9eipGPaXrvgZblODVZEiTzqgmo_KTCVPWepqtyghW2XSRfR6IWtkrE1znNVqS5X8EWIN5mx7NgeYxH--9sVdWf6VC-qRu_dehVdNSTz5K6UfKmklOiX8mbykPvBhksmhKwD8FhRD8oJ-zFycIwBcUo_YHYLq897_o8n_Ywb8IvMFQoxK1M6HkccCOHQambqihCJNJzgKqEHd4zkkY7PoT6iwbkz_YlXoeH5e_JhFfMv3ZJqYIkUXUSfw6eaMnIyUQRlvF6jbiZ-Qlxj6kKal5ntHet3Zl3He1FfC9goun7yBiRP3yJc_ASB7baoIm1TupVRQtYSGonOEeLetVoS8", "easy_hide_button_story": null}, "is_regulation_enforced": false, "__module_operation_CometFeedStoryContextSectionMatchRenderer_story": {"__dr": "CometFeedStoryDefaultContextLayoutStrategy_contextLayout$normalization.graphql"}, "__module_component_CometFeedStoryContextSectionMatchRenderer_story": {"__dr": "CometFeedStoryDefaultContextLayoutStrategy.react"}}, "aymt_footer": null, "footer": null, "feedback": {"__typename": "CometStoryFeedbackUFIStrategy", "__isICometStorySection": "CometStoryFeedbackUFIStrategy", "is_prod_eligible": true, "story": {"feedback_context": {"feedback_target_with_context": {"viewer_actor": null, "id": "ZmVlZGJhY2s6NzEzNDM2ODU0OTc4NTM2"}}, "story_ufi_container": {"__typename": "XFBSimplifiedUFIContainer", "story": {"encrypted_tracking": "AZXmbf7B5bW8qw7Vh-jvrCZQPfSDSgUtr7pMpqZmaadhKDZcm3U2V13Ok8S1QK1nOD5Wd9KnFpcJD2mtzs1eQUD8G8X49aOiorX1IYD4xTBfBGJVJ2d8urPyzMaDjCvoUn0NXgXYTwq_Eq9nm_B8u9EyKVrO9MNYlErngi7EkKZAoZ0a6nMARO3I87U7ieyplifPUOneAeJER-4HYJR-Ibzx14zOJuU4Zi2ZndtpkooveF5NgXpNUVJnTOQqzh9q_DfL2DQ-jxA0w0gQ1u8sLQuZlIlywkA84GNtmECi92_Ktz0xMnWT90-CB0lT8B3TX3EX64lwryGLU99FCRMo_ruD39TWa62J4IPb-T_UvFwwOzHsAv6ieO0c3JBS-zHpOUWGsuKLr369x7zXk8KCmaj1DbD2RGrsIZw5eB2c2FlqCv68S8GvfaBSKfqf88qGVq49j09BjOlcG4scpZebZVT48DmMW89xecag3JwwNJGFyfPC2YBmsXHE0trIDtvWv52v2v7SP5KMKaY4LIStC_uNZ024dLCxJ40xwLXRHGnTYYdDZrgZEugjqoBDs9Ixp_xr__zcHuWenLRF2eSPKE-LkgQPY6ag1CYJxKpCIgaMoPmD7P7TskrqEdh1lqS-Zg7fwpwYAicbDNaaOrN9k2kwnpfUci0Ktess4-EIPr9hid588UiDzOYkaD_LHlvokVeQvi6Y04jMWsIP_338ItiGHA9vTuOu16d1mhhbmi1rFUUz1jNZF6yoIO0-Mblp8yYG4sIHSHcEY8WqcsGtfmJwbBBIPLfK5UZSg-mJMQGfiMMjjVYwDseSNBHWZwSw8O-BEitTDKZn2duUjqkLteAAK8cft5H_uKnoqOmj8Y_-LkA59rhls6ouBgLLsJV7zBPW5q2H5_WMcl3OdcYG8QtI5jQWReA4q2K6int2XlTcxuE6Vs3sVVjrkHdpnTxrlzhWFcpeIk5JUuXXSDIIqJumBwdh8Yzc_LKzQR_IN2wczecqgyMTr_KQekN63Vs5gSsY9Uj6qQI5ZdmCt9MXxuLLO4f68-lXs0kjMUG5uRkE9gMYqi09BpJngL9oxvIegFdGowmly70jHI9XTVDoPtA31qpZ-MGXnUuULzznHfdH_fe5UVGbQpSyOmILoF5dyY1Iz3s7ebtNBkdeiG8NLXob6fckR7FlK0ZMPhssHRy1SvgoxkZTBt3rTXEe5sEXr_JI1uAnloIpmkdYgD-em66JrEpCeCj7Z8LuFbAFNbaYevMq8-28lsre7hzx_UnwZHqxTy-8rmmYkFTQqAgjBxTFcDjbHi2M83IYbbo_suGWPvYH5tehstJfnHE1MwhMIvgAD_0FMdBTGCBqvrG7HuDQvzBirqX1R49Vu8ibrksmG7k_H574Gib_3UhKKWdGLlqm0INNLxGIRvknxxZ6DKM0uXRZRyDaVN0bpmMYX8PX3ruLl0TFkCxrMEGaKI8FCdA39JCBaFv82JoVzcNojZNChjBknAyu6sylo6GcJ-vdnuVon9YE-FEJj77cwFMd2gvPentS2ElVEYqaW7HSp-mkXhYbdM6wQWr_YIGSWnVaiSXXfX3jAEDCNg2DBskhpjc7fyNsFVFEzJbdzw832cbbey36FrDcryTF_CtAOsHh9X1uaw_RaOqUSe-SbpFhoxvNIarX8gzKE_N-CqJ4TpZVsgn-3b4jI1BU-EaXdNrToHVz2gCmt6RxLZzbAdnf4vmzBgBhqWLYt0I34wWp8YTaWrKmqtMuss200A2AGpbmQAUb0Vy4g8MO2VJDBrE7uZBAgONIgSi4FzyyLDTXi9qobP7OyMBFeGxtg5bsb_imvdZi1X52ixWt-rPys83xU6VStq_QpOxF5offcG845j0XjR9kdfx_0tqXSxIDeRGYfCklkTJ93qzHDJow_hpBjSA", "is_text_only_story": false, "feedback_context": {"feedback_target_with_context": {"id": "ZmVlZGJhY2s6NzEzNDM2ODU0OTc4NTM2", "owning_profile": {"__typename": "User", "name": "<PERSON><PERSON>", "short_name": "<PERSON><PERSON>", "id": "100089366829069"}, "can_viewer_comment": false, "comet_ufi_summary_and_actions_renderer": {"__typename": "UnauthenticatedUCometUFISummaryAndActionsRenderer", "feedback": {"id": "ZmVlZGJhY2s6NzEzNDM2ODU0OTc4NTM2", "is_similar_cqa_question": false, "subscription_target_id": "713436854978536", "i18n_reaction_count": "317", "important_reactors": {"nodes": []}, "reaction_count": {"count": 317, "is_empty": false}, "top_reactions": {"count": 3, "edges": [{"visible_in_bling_bar": true, "node": {"id": "1678524932434102", "localized_name": "Love"}, "i18n_reaction_count": "213", "reaction_count": 213}, {"visible_in_bling_bar": true, "node": {"id": "****************", "localized_name": "Like"}, "i18n_reaction_count": "65", "reaction_count": 65}, {"visible_in_bling_bar": true, "node": {"id": "613557422527858", "localized_name": "Care"}, "i18n_reaction_count": "39", "reaction_count": 39}]}, "reaction_display_config": {"reaction_display_strategy": "NONE", "reaction_string_with_viewer": null, "reaction_string_without_viewer": null, "__module_operation_CometUFIReactionsCount_feedback": {"__dr": "CometUFIReactionSentence_reactionDisplayConfig$normalization.graphql"}, "__module_component_CometUFIReactionsCount_feedback": {"__dr": "CometUFIReactionSentence.react"}}, "viewer_actor": null, "viewer_feedback_reaction_info": null, "can_show_seen_by": false, "if_viewer_can_see_seen_by_member_list": null, "if_viewer_cannot_see_seen_by_member_list": {"i18n_reaction_count": "317", "reaction_count": {"count": 317}, "reaction_display_config": {"reaction_display_strategy": "NONE"}, "seen_by": {"count": 0, "i18n_seen_by_count": null, "seen_by_everyone": false}, "__module_operation_CometUFISeenByCount_feedback__if_viewer_cannot_see_seen_by_member_list": {"__dr": "CometUFISeenByCountText_feedback$normalization.graphql"}, "__module_component_CometUFISeenByCount_feedback__if_viewer_cannot_see_seen_by_member_list": {"__dr": "CometUFISeenByCountText.react"}, "id": "ZmVlZGJhY2s6NzEzNDM2ODU0OTc4NTM2"}, "i18n_share_count": "4", "share_count": {"count": 4, "is_empty": false}, "comments_count_summary_renderer": {"__typename": "TotalCommentsCountSummaryRenderer", "feedback": {"id": "ZmVlZGJhY2s6NzEzNDM2ODU0OTc4NTM2", "comment_rendering_instance": {"comments": {"total_count": 12}}}, "__module_operation_CometUFISummaryBase_feedback": {"__dr": "CometUFICommentsCountRenderer_data$normalization.graphql"}, "__module_component_CometUFISummaryBase_feedback": {"__dr": "CometUFICommentsCountRenderer.react"}}, "associated_video": null, "comment_rendering_instance": {"comments": {"total_count": 12}}, "page_private_reply": null, "video_view_count": null, "video_view_count_renderer": null}, "__module_operation_CometUFISummaryAndActions_feedback": {"__dr": "CometUnauthenticatedUFISummaryAndActionsRenderer_feedback$normalization.graphql"}, "__module_component_CometUFISummaryAndActions_feedback": {"__dr": "CometUnauthenticatedUFISummaryAndActionsRenderer.react"}}, "is_community_qa_or_qaish_post": false, "threading_config": null, "actor_provider": {"__typename": "<PERSON><PERSON><PERSON>", "current_actor": null, "id": "ZmVlZGJhY2s6NzEzNDM2ODU0OTc4NTM2"}, "comment_rendering_instance": {"comments": {"total_count": 12}}, "viewer_actor": null, "url": "https://www.facebook.com/abdurrahmantalha.dev/posts/pfbid02qeGraPg1YwAjVQADkF2CakhDPvtAo3593cYxT9m32RTzEhnLWCXvawM972Y5deUzl", "__typename": "<PERSON><PERSON><PERSON>", "if_viewer_can_comment_anonymously": null, "plugins": [{"__typename": "CommentComposerMentionsPlugin", "context_id": null, "post_id": "713436854978536", "__module_operation_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerMentionsPlugin_plugin$normalization.graphql"}, "__module_component_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerMentionsPlugin"}}, {"__typename": "CommentComposerHashtagPlugin", "__module_operation_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerHashtagPlugin_plugin$normalization.graphql"}, "__module_component_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerHashtagPlugin"}}, {"__typename": "CommentComposerEmojiPlugin", "emoji_size": 16, "__module_operation_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerEmojiPlugin_plugin$normalization.graphql"}, "__module_component_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerEmojiPlugin"}}, {"__typename": "CommentComposerEmoticonPlugin", "emoji_size": 16, "__module_operation_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerEmoticonPlugin_plugin$normalization.graphql"}, "__module_component_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerEmoticonPlugin"}}, {"__typename": "CommentComposerPrefillMentionPlugin", "__module_operation_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerPrefillMentionPlugin_plugin$normalization.graphql"}, "__module_component_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerPrefillMentionPlugin"}}, {"__typename": "CommentComposerAssociateReplyWithParentPlugin", "__module_operation_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerAssociateReplyWithParentPlugin_plugin$normalization.graphql"}, "__module_component_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerAssociateReplyWithParentPlugin"}}, {"__typename": "CommentComposerSetReplyClickedPlugin", "__module_operation_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerSetReplyClickedPlugin_plugin$normalization.graphql"}, "__module_component_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerSetReplyClickedPlugin"}}, {"__typename": "CommentComposerStateSnapshotPlugin", "__module_operation_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerStateSnapshotPlugin_plugin$normalization.graphql"}, "__module_component_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerStateSnapshotPlugin"}}, {"__typename": "CommentComposerCommentCharacterLimitPlugin", "__module_operation_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerCommentCharacterLimitPlugin_plugin$normalization.graphql"}, "__module_component_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerCommentCharacterLimitPlugin"}}, {"__typename": "CommentComposerWriteToComposerPlugin", "__module_operation_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerWriteToComposerPlugin_plugin$normalization.graphql"}, "__module_component_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerWriteToComposerPlugin"}}], "comment_composer_placeholder": "Write a comment…", "have_comments_been_disabled": false, "default_comment_ordering_mode": "RANKED_THREADED", "inline_composer_visible_by_default": false, "associated_group": null, "work_comment_summaries_from_feedback": null, "are_live_video_comments_disabled": false, "is_viewer_muted": false, "comments_disabled_notice_renderer": {"__typename": "GeneralCommentDisableNotice", "notice_message": {"delight_ranges": [], "image_ranges": [], "inline_style_ranges": [], "aggregated_ranges": [], "ranges": [], "color_ranges": [], "text": "Commenting has been turned off for this post."}, "__module_operation_CometUFICommentDisabledNotice_feedback": {"__dr": "CometGenericCommentDisableNotice_commentDisableNotice$normalization.graphql"}, "__module_component_CometUFICommentDisabledNotice_feedback": {"__dr": "CometGenericCommentDisableNotice.react"}}, "comment_moderation_filter_restriction_notice": null}, "interesting_top_level_comments": [{"comment": {"id": "Y29tbWVudDo3MTM0MzY4NTQ5Nzg1MzZfMjM3MzE0NzQ2MzA3OTk0OA==", "legacy_fbid": "2373147463079948", "depth": 0, "body": {"text": "আ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": []}, "attachments": [], "is_markdown_enabled": false, "community_comment_signal_renderer": null, "comment_menu_tooltip": null, "should_show_comment_menu": false, "author": {"__typename": "User", "id": "100009179329514", "name": "<PERSON>", "__isActor": "User", "profile_picture_depth_0": {"uri": "https://scontent.fdac145-1.fna.fbcdn.net/v/t39.30808-1/489338129_3928364067479532_7660861442062390561_n.jpg?stp=cp0_dst-jpg_s32x32_tt6&_nc_cat=106&ccb=1-7&_nc_sid=1d2534&_nc_ohc=zwyJtnJPelQQ7kNvwEIE2as&_nc_oc=AdnKk0D1JzL4OOP5NbRMzWDnE7-xivIa6sfvmnVO9-0IOdvIHlXxSRgBNs3_Cxb4ojk&_nc_zt=24&_nc_ht=scontent.fdac145-1.fna&_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&oh=00_AfQexmLXHolOoek_tzXjIcQbKubuy5SkNGM-yNRnZQ-X0g&oe=687ADA91"}, "profile_picture_depth_1": {"uri": "https://scontent.fdac145-1.fna.fbcdn.net/v/t39.30808-1/489338129_3928364067479532_7660861442062390561_n.jpg?stp=cp0_dst-jpg_s24x24_tt6&_nc_cat=106&ccb=1-7&_nc_sid=1d2534&_nc_ohc=zwyJtnJPelQQ7kNvwEIE2as&_nc_oc=AdnKk0D1JzL4OOP5NbRMzWDnE7-xivIa6sfvmnVO9-0IOdvIHlXxSRgBNs3_Cxb4ojk&_nc_zt=24&_nc_ht=scontent.fdac145-1.fna&_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&oh=00_AfQk3NtBHRW2yGLlxx-7405xUgJtfOPckxf6fbF5qY4alg&oe=687ADA91"}, "gender": "MALE", "__isEntity": "User", "url": "https://www.facebook.com/ahmleon551", "work_info": null, "is_verified": false, "short_name": "<PERSON>", "subscribe_status": "CANNOT_SUBSCRIBE"}, "is_author_weak_reference": false, "comment_action_links": [{"__typename": "XFBCommentTimeStampActionLink", "comment": {"id": "Y29tbWVudDo3MTM0MzY4NTQ5Nzg1MzZfMjM3MzE0NzQ2MzA3OTk0OA==", "created_time": 1750479716, "url": "https://www.facebook.com/abdurrahmantalha.dev/posts/pfbid02qeGraPg1YwAjVQADkF2CakhDPvtAo3593cYxT9m32RTzEhnLWCXvawM972Y5deUzl?comment_id=2373147463079948"}, "__module_operation_CometUFICommentActionLinks_comment": {"__dr": "CometUFICommentTimeStampActionLink_commentActionLink$normalization.graphql"}, "__module_component_CometUFICommentActionLinks_comment": {"__dr": "CometUFICommentTimeStampActionLink.react"}}], "feedback": {"viewer_feedback_reaction_info": null, "id": "ZmVlZGJhY2s6NzEzNDM2ODU0OTc4NTM2XzIzNzMxNDc0NjMwNzk5NDg=", "top_reactions": {"edges": []}, "reactors": {"count_reduced": "0"}, "total_reply_count": 0, "viewer_actor": null, "actor_provider": {"__typename": "<PERSON><PERSON><PERSON>", "current_actor": null, "id": "ZmVlZGJhY2s6NzEzNDM2ODU0OTc4NTM2XzIzNzMxNDc0NjMwNzk5NDg="}, "url": "https://www.facebook.com/abdurrahmantalha.dev/posts/pfbid02qeGraPg1YwAjVQADkF2CakhDPvtAo3593cYxT9m32RTzEhnLWCXvawM972Y5deUzl?comment_id=2373147463079948", "__typename": "<PERSON><PERSON><PERSON>", "if_viewer_can_comment_anonymously": null, "plugins": [{"__typename": "CommentComposerMentionsPlugin", "context_id": null, "post_id": "713436854978536", "__module_operation_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerMentionsPlugin_plugin$normalization.graphql"}, "__module_component_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerMentionsPlugin"}}, {"__typename": "CommentComposerHashtagPlugin", "__module_operation_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerHashtagPlugin_plugin$normalization.graphql"}, "__module_component_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerHashtagPlugin"}}, {"__typename": "CommentComposerEmojiPlugin", "emoji_size": 16, "__module_operation_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerEmojiPlugin_plugin$normalization.graphql"}, "__module_component_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerEmojiPlugin"}}, {"__typename": "CommentComposerEmoticonPlugin", "emoji_size": 16, "__module_operation_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerEmoticonPlugin_plugin$normalization.graphql"}, "__module_component_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerEmoticonPlugin"}}, {"__typename": "CommentComposerPrefillMentionPlugin", "__module_operation_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerPrefillMentionPlugin_plugin$normalization.graphql"}, "__module_component_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerPrefillMentionPlugin"}}, {"__typename": "CommentComposerAssociateReplyWithParentPlugin", "__module_operation_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerAssociateReplyWithParentPlugin_plugin$normalization.graphql"}, "__module_component_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerAssociateReplyWithParentPlugin"}}, {"__typename": "CommentComposerSetReplyClickedPlugin", "__module_operation_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerSetReplyClickedPlugin_plugin$normalization.graphql"}, "__module_component_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerSetReplyClickedPlugin"}}, {"__typename": "CommentComposerStateSnapshotPlugin", "__module_operation_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerStateSnapshotPlugin_plugin$normalization.graphql"}, "__module_component_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerStateSnapshotPlugin"}}, {"__typename": "CommentComposerCommentCharacterLimitPlugin", "__module_operation_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerCommentCharacterLimitPlugin_plugin$normalization.graphql"}, "__module_component_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerCommentCharacterLimitPlugin"}}, {"__typename": "CommentComposerWriteToComposerPlugin", "__module_operation_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerWriteToComposerPlugin_plugin$normalization.graphql"}, "__module_component_useCometUFIComposerPlugins_feedback": {"__dr": "cometUFIComposerWriteToComposerPlugin"}}], "comment_composer_placeholder": "Reply to <PERSON>…", "can_viewer_comment": false, "have_comments_been_disabled": false, "default_comment_ordering_mode": "TOPLEVEL", "inline_composer_visible_by_default": true, "comment_rendering_instance": null, "associated_group": null}, "preferred_body": {"__typename": "TextWithEntities", "delight_ranges": [], "image_ranges": [], "inline_style_ranges": [], "aggregated_ranges": [], "ranges": [], "color_ranges": [], "text": "আ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "translation_type": "ORIGINAL"}, "body_renderer": {"__typename": "TextWithEntities", "delight_ranges": [], "image_ranges": [], "inline_style_ranges": [], "aggregated_ranges": [], "ranges": [], "color_ranges": [], "text": "আ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__module_operation_CometUFICommentTextBodyRenderer_comment": {"__dr": "CometUFICommentBodyTextWithEntities_textWithEntities$normalization.graphql"}, "__module_component_CometUFICommentTextBodyRenderer_comment": {"__dr": "CometUFICommentBodyTextWithEntities.react"}}, "comment_parent": null, "is_declined_by_group_admin_assistant": false, "is_gaming_video_comment": false, "timestamp_in_video": null, "translatability_for_viewer": {"source_dialect": "bn_IN"}, "written_while_video_was_live": false, "group_comment_info": null, "bizweb_comment_info": null, "has_constituent_badge": false, "can_viewer_see_subsribe_button": false, "can_see_constituent_badge_upsell": false, "legacy_token": "713436854978536_2373147463079948", "parent_feedback": {"id": "ZmVlZGJhY2s6NzEzNDM2ODU0OTc4NTM2", "share_fbid": "713436854978536", "political_figure_data": null, "owning_profile": {"__typename": "User", "name": "<PERSON><PERSON>", "id": "100089366829069"}}, "question_and_answer_type": null, "is_author_original_poster": false, "is_viewer_comment_poster": false, "is_author_bot": false, "is_author_non_coworker": false, "author_user_signals_renderer": null, "author_badge_renderers": [], "identity_badges_web": [], "can_show_multiple_identity_badges": false, "discoverable_identity_badges_web": [], "user": {"name": "<PERSON>", "profile_picture": {"uri": "https://scontent.fdac145-1.fna.fbcdn.net/v/t39.30808-1/489338129_3928364067479532_7660861442062390561_n.jpg?stp=cp0_dst-jpg_s50x50_tt6&_nc_cat=106&ccb=1-7&_nc_sid=1d2534&_nc_ohc=zwyJtnJPelQQ7kNvwEIE2as&_nc_oc=AdnKk0D1JzL4OOP5NbRMzWDnE7-xivIa6sfvmnVO9-0IOdvIHlXxSRgBNs3_Cxb4ojk&_nc_zt=24&_nc_ht=scontent.fdac145-1.fna&_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&oh=00_AfR55HbLb7XGUnigJ4H7-mAeBknJ1a0rPOjshMKmo0JRPw&oe=687ADA91"}, "id": "100009179329514"}, "parent_post_story": {"attachments": [{"media": {"__typename": "Photo", "__isNode": "Photo", "id": "713436824978539"}}], "id": "UzpfSTEwMDA4OTM2NjgyOTA2OTo3MTM0MzY4NTQ5Nzg1MzY6NzEzNDM2ODU0OTc4NTM2"}, "gen_ai_content_transparency_label_renderer": null, "work_ama_answer_status": null, "work_knowledge_inline_annotation_comment_badge_renderer": null, "business_comment_attributes": [], "is_live_video_comment": false, "created_time": 1750479716, "translation_available_for_viewer": false, "inline_survey_config": null, "spam_display_mode": "none", "attached_story": null, "comment_direct_parent": null, "if_viewer_can_see_member_page_tooltip": null, "is_disabled": false, "work_answered_event_comment_renderer": null, "comment_upper_badge_renderer": null, "elevated_comment_data": null, "inline_replies_expander_renderer": null}, "relevant_contextual_replies": {"nodes": []}}]}, "shareable_from_perspective_of_feed_ufi": null, "id": "UzpfSTEwMDA4OTM2NjgyOTA2OTo3MTM0MzY4NTQ5Nzg1MzY6NzEzNDM2ODU0OTc4NTM2", "url": "https://www.facebook.com/abdurrahmantalha.dev/posts/pfbid02qeGraPg1YwAjVQADkF2CakhDPvtAo3593cYxT9m32RTzEhnLWCXvawM972Y5deUzl", "shareable": null, "sponsored_data": null, "inform_treatment_for_messaging": null, "attachments": [{"action_links": []}], "tracking": "{\"qid\":\"-8695920292545515934\",\"mf_story_key\":\"713436854978536\",\"top_level_post_id\":\"713436854978536\",\"tl_objid\":\"713436854978536\",\"content_owner_id_new\":\"100089366829069\",\"throwback_story_fbid\":\"713436854978536\",\"page_id\":\"488294627707388\",\"photo_id\":\"713436824978539\",\"story_location\":4,\"story_attachment_style\":\"photo\",\"sty\":22,\"ent_attachement_type\":\"MediaAttachment\",\"page_insights\":{\"100089366829069\":{\"page_id\":\"100089366829069\",\"page_id_type\":\"page\",\"actor_id\":\"100089366829069\",\"dm\":{\"isShare\":0,\"originalPostOwnerID\":0,\"sharedMediaID\":0,\"sharedMediaOwnerID\":0},\"psn\":\"EntStatusCreationStory\",\"post_context\":{\"object_fbtype\":266,\"publish_time\":1750463530,\"story_name\":\"EntStatusCreationStory\",\"story_fbid\":[\"713436854978536\"]},\"role\":1,\"sl\":4,\"targets\":[{\"actor_id\":\"100089366829069\",\"page_id\":\"100089366829069\",\"post_id\":\"713436854978536\",\"role\":1,\"share_id\":0}]},\"100076214209550\":{\"page_id\":\"100076214209550\",\"page_id_type\":\"page\",\"actor_id\":\"100089366829069\",\"dm\":{\"isShare\":0,\"originalPostOwnerID\":0,\"sharedMediaID\":0,\"sharedMediaOwnerID\":0},\"psn\":\"EntStatusCreationStory\",\"role\":16,\"sl\":4},\"100079809223454\":{\"page_id\":\"100079809223454\",\"page_id_type\":\"page\",\"actor_id\":\"100089366829069\",\"dm\":{\"isShare\":0,\"originalPostOwnerID\":0,\"sharedMediaID\":0,\"sharedMediaOwnerID\":0},\"psn\":\"EntStatusCreationStory\",\"role\":16,\"sl\":4}},\"profile_id\":\"100089366829069\",\"actrs\":\"100089366829069\",\"tds_flgs\":3}", "post_id": "713436854978536", "daspo_sto": null, "target_group": null, "click_tracking_linkshim_cb": "AT2DFvv1lwmZm_rfJ1qLZXHvY7Tti4MEcp0c9gsEvRRpr0RO1wUH6PwM5zyQts0WBy59o0jFBADwGltuUw6SvxO-Y-q_dpXV69CVoCuvEpmAnLLCU7UknZ_d_ni2zmnGQ30lhp4ihJU9MLqwalIicdcukqNTw6IotEMtdA0-nVblSmQupiBbhtnwaQtEHbbEHmIGC6sTbuGcDGpJBRlu0FrLVus", "encrypted_click_tracking": "AZW95_YbciDhZFymXdwHJ_d7pIRCVUx9bFG9kkYrORozMhOAeUBRfIOYp4FBMt-7qOqv2S53kbqKHzh7ItkTekvXZBCmT6UGZ3e-OlNFX2n6BP5usljfWZbpw8Vl4RfpkI1Z_PvQjT3XFkaxAffTxpB2Q05tnoDsU94WmLAUikzsxheQpgG6wLSvClGtyX77vuI", "__module_operation_useCometUFIAdaptivePostActionBar_story": {"__dr": "CometUFIAdaptiveShareActionRenderer_story$normalization.graphql"}, "__module_component_useCometUFIAdaptivePostActionBar_story": {"__dr": "CometUFIAdaptiveShareActionRenderer.react"}, "vote_attachments": [], "feed_backend_data": {"pcomment": 0}}, "__module_operation_CometFeedUFIContainer_story": {"__dr": "CometSimplifiedUFIContainer_renderer$normalization.graphql"}, "__module_component_CometFeedUFIContainer_story": {"__dr": "CometSimplifiedUFIContainer.react"}}, "id": "UzpfSTEwMDA4OTM2NjgyOTA2OTo3MTM0MzY4NTQ5Nzg1MzY6NzEzNDM2ODU0OTc4NTM2"}, "__module_operation_CometFeedStoryFeedbackSection_story": {"__dr": "CometFeedStoryFeedbackUFIStrategy_feedback$normalization.graphql"}, "__module_component_CometFeedStoryFeedbackSection_story": {"__dr": "CometFeedStoryFeedbackUFIStrategy.react"}}, "outer_footer": null, "call_to_action": {"__typename": "CometStoryDefaultCallToActionStrategy", "__isICometStorySection": "CometStoryDefaultCallToActionStrategy", "is_prod_eligible": true, "story": {"bumpers": null, "tracking": "{\"qid\":\"-8695920292545515934\",\"mf_story_key\":\"713436854978536\",\"top_level_post_id\":\"713436854978536\",\"tl_objid\":\"713436854978536\",\"content_owner_id_new\":\"100089366829069\",\"throwback_story_fbid\":\"713436854978536\",\"page_id\":\"488294627707388\",\"photo_id\":\"713436824978539\",\"story_location\":4,\"story_attachment_style\":\"photo\",\"sty\":22,\"ent_attachement_type\":\"MediaAttachment\",\"page_insights\":{\"100089366829069\":{\"page_id\":\"100089366829069\",\"page_id_type\":\"page\",\"actor_id\":\"100089366829069\",\"dm\":{\"isShare\":0,\"originalPostOwnerID\":0,\"sharedMediaID\":0,\"sharedMediaOwnerID\":0},\"psn\":\"EntStatusCreationStory\",\"post_context\":{\"object_fbtype\":266,\"publish_time\":1750463530,\"story_name\":\"EntStatusCreationStory\",\"story_fbid\":[\"713436854978536\"]},\"role\":1,\"sl\":4,\"targets\":[{\"actor_id\":\"100089366829069\",\"page_id\":\"100089366829069\",\"post_id\":\"713436854978536\",\"role\":1,\"share_id\":0}]},\"100076214209550\":{\"page_id\":\"100076214209550\",\"page_id_type\":\"page\",\"actor_id\":\"100089366829069\",\"dm\":{\"isShare\":0,\"originalPostOwnerID\":0,\"sharedMediaID\":0,\"sharedMediaOwnerID\":0},\"psn\":\"EntStatusCreationStory\",\"role\":16,\"sl\":4},\"100079809223454\":{\"page_id\":\"100079809223454\",\"page_id_type\":\"page\",\"actor_id\":\"100089366829069\",\"dm\":{\"isShare\":0,\"originalPostOwnerID\":0,\"sharedMediaID\":0,\"sharedMediaOwnerID\":0},\"psn\":\"EntStatusCreationStory\",\"role\":16,\"sl\":4}},\"profile_id\":\"100089366829069\",\"actrs\":\"100089366829069\",\"tds_flgs\":3}", "id": "UzpfSTEwMDA4OTM2NjgyOTA2OTo3MTM0MzY4NTQ5Nzg1MzY6NzEzNDM2ODU0OTc4NTM2"}, "__module_operation_CometFeedStoryCallToActionSection_story": {"__dr": "CometFeedStoryDefaultCallToActionStrategy_cta$normalization.graphql"}, "__module_component_CometFeedStoryCallToActionSection_story": {"__dr": "CometFeedStoryDefaultCallToActionStrategy.react"}}, "post_inform_treatment": null, "action_link": null, "timestamp": {"__typename": "CometFeedStoryMinimizedTimestampStrategy", "__isICometStorySection": "CometFeedStoryMinimizedTimestampStrategy", "is_prod_eligible": true, "override_url": null, "video_override_url": null, "story": {"creation_time": 1750463530, "unpublished_content_type": "PUBLISHED", "url": "https://www.facebook.com/abdurrahmantalha.dev/posts/pfbid02qeGraPg1YwAjVQADkF2CakhDPvtAo3593cYxT9m32RTzEhnLWCXvawM972Y5deUzl", "ghl_label": null, "id": "UzpfSTEwMDA4OTM2NjgyOTA2OTo3MTM0MzY4NTQ5Nzg1MzY6NzEzNDM2ODU0OTc4NTM2"}, "__module_operation_CometFeedStoryTimestampSection_story": {"__dr": "CometFeedStoryMinimizedTimestampStrategy_timestamp$normalization.graphql"}, "__module_component_CometFeedStoryTimestampSection_story": {"__dr": "CometFeedStoryMinimizedTimestampStrategy.react"}}}, "encrypted_tracking": "AZUIJA_7JVQm3kEYM5o28XtT01dqC5Yc4awFrGRiwx-x_1asFxbm-RNTRLAPZNXoUqVVsklkDVbMO_W33SRyElIjVU-RvPQ6xdTumxYdrQifDgYWN5Lc_RhT_wrCEHPTrGoassYpRxaF4XJgciy7KmPA1NqS6-DdtUgP1A8VBRU_fGLttyReqSZcIQ3nlWV-PMaXMERRSNJ8oLVPhVwfoL4Q1psIRTvY10BQ9h39g3UD66emsKlO2KbS9b3l4weGsHP5LoJhyW9n9_1K7S--qwt1M6oq87UXfBDviN_ThHPlqQZ7BHYW_9HpJeFCi2YG05hnBVXpGgiIfpOom55GnC2h7SkV8lg3sVrpebUxp6Ro73CNfeySAamR9scUcttwhymBsJ07KFo3bFgrH7TnGbFX1o_T4W5TdmPOYl6H7aH7awjgcQoy3y4jsHHzna9avlSrLk3QMLBJGa4arPKPcEXm27UzazT_4LFI6YTcrFETWqeyOt4FQpthfvyjAc_-AfaiG94Ej2C1njtk8pBJRDJtGnQ1XNhklSfYtIEzMduUCoY5TN7w6kXCN1WGURwXn90e-umV0KkQ5vksWoo0lVQJHyncigwlwzbqXSgZo1F2nZZyWqMccAKVs92lxNs4HYdy4WYvtcoBubuYMy1L_WcFFCMCOMgId02r9d7cx0uoxW1fABwtXOc0l4CzMR2tPj9j3N6K2Fg7KErIQybD8GV9s5H-VuoA3Ets_W7xdX85GI8Z1hg0Kq6up6RxxZzzb4fP02bFwTzIFiE62f7lGNbeStWkvwcYckTmL15-cmUjQxAW7HnZCbYAP1_rX5OyBEFBOZ4jI9Sz_Va754SuIcfnSkGuqRWFS58pZbHXnx36NmhlsnTbJMyJ14_Yho6lZSiDayxnNXwR4OeY-D7ynbN9Bp7uHBEcxJFawmyL2zaglF_ulfApdgafc1-yhs-C4xi2D1V3sEGBPzDeuujiAWY_iuEsbIaPUDmmFAvXDiGBn6fO8VPs9QnFidDRh2itejphe9f9gv6tRHgAS1jGj0l3hxau68tN_z4jqBUUYxBZzT9VE_QG9UFx3V-lUaxrmBovpHfwdwjCJRfaDv9pzQraPInxnNdcu4qyUpjqHd4sApxIzu8-U0zINkLxeXywZjJmJxC6dE0j7uHDRl2rclPRVfL9ZLp0WJfEHUbGVxb_q9-i5gUn60HKqYfENw7dWqGHjHnK3nmLcV0Z6zYxA6kpnI5Zif5Gi__xeVKPat71NygV3oFCV7EhEtk7V2zlDFUjOG-5GfZ2mC5pLlz-wngjqOzM8zjFjAgWJRkmRTh-_2MeIZ0aqzniYR5DW99srS6PZwA3L1oC7QsyXeU8P8ITHjjwIa53cm5i1NbRUgEOEAVHzzJWoodHaS82y2dQ4hPMg1_dNhwe6ewUM5LKh4GG9RN7VusOd6uVze6i51lx8ZLTFyKddrMi7kVkK7N2RWLfWjrAeCVq8GLjQvpMHmHmHTl7L4BbVUEnq6t7XK5i2HTGbV9Anqx6i4Pzhl_Q4dGfbLAUglDpUs-hsc6Gmx_i2oiD5UBlzyJE_JdE4c3JWBtCNiH3IACqqXzk62nDZcgQeKkYXWYuEsYUcCiGAva05zD2KUTIQRP8rE4DKzVHx7u_8s_eDRV95ruBXOiP_nb6eiD-oL1WxX-_Nm0wdomRggudnciw0vq_F8tN7m_rpHfN_K2DG0y9X3fAdzYojdAXZM51mv6Sfh_vhb1gjG-qIrcvM20HvCYNaGBPng700PYX1ey0iSp9BW9p6V9b1LdP79YXtTR5uyGAela8gVeyUUsN_ppEnOE8ulv_xaBcJv5dO068599khYS0iJiVsZFGgJuZH__vD93LYS6oz5AZp8hLLbZ_y1AFM4mRBH8wFNikeupm8yPrV69j-hGtclM", "should_host_actor_link_in_watch": false, "whatsapp_ad_context": null, "schema_context": null, "click_tracking_linkshim_cb": "AT14Fi2ADqad9AqnS6x4BEU0vPzGUPnqJb79Sx08ZnnAv9nXf35g3JhXlvq84n_UcZZ0Agdv4--DovZj5G8pjgnKSvIPrTHcp2cHgsibLx7iSnr_i48JxN7k4iF1a6c6oWZ9rqSpdh9TDrsA6UPcrt5eW4_LTVR-Z2ZYyrg9mUahms6iI5ZOsdJsNpi0syaDNskenh59mOROEFgrx7jSeAJ0xKI", "encrypted_click_tracking": "AZVVxep4_7P1iJZRDsaG7fBNud_Dqc6h_ewaQdKk0WDXXp1P_iH05J-HNNPuXbkDme1_VVqAPY2N1TQbfYY2n72CXwbnZ4EBfnJk7Q7yeCIX3h41QP8z4w-P438IiuOKBD7SmRiG2ld6bp2VOAeBhSUJJ9EB5wwIMK1K1v1hpK8BFgfYMfhg8_T6VoxopU_H7wc", "serialized_frtp_identifiers": null, "can_viewer_see_menu": false, "actors": [{"__typename": "User", "name": "<PERSON><PERSON>", "id": "100089366829069", "__isActor": "User", "__isEntity": "User", "url": "https://www.facebook.com/abdurrahmantalha.dev", "work_foreign_entity_info": null, "work_info": null}], "to": null, "work_is_repost": null, "__module_operation_CometFeedUnitContainerSection_feedUnit": {"__dr": "CometFeedUnitStoryStrategy_feedUnit$normalization.graphql"}, "__module_component_CometFeedUnitContainerSection_feedUnit": {"__dr": "CometFeedUnitStoryStrategy.react"}, "__isTrackableFeedUnit": "Story", "trackingdata": {"id": "AZXO30Wmt7mTE1JW2ir3GQ8azQygNo5lgwgy_QgQRwBf4rthUAfbHHvFAMSCBY14h8nNRODrph7hIJiWVKLyjuhHn6rq4_DeW4VVadeQCVMcJRAEKD0YGMzIlL3JKMTcjUMrbIJa_BjPfay6ygOVhSusqZqXQio2Ym9LlpefwV4lGfSdkBBuGu3K4pu5T3zudYDWwQ_SFJ9tDsqeLdku6MUQ2Vm1TcLP_s-NG4oJy6FA3wcRso01H94MC54MVTPxVtF4MUL-x1hbmYw3neHmC9-d1h7luuIun8TJLDLFFc5Yg_Ptc5FR3XkMzaB1E_px1MsZQZqNetABOSwtPl-2ePpQ4lZsPhJo4uX_mrxD7ivy8XJVUKTh6AKzwSRBjQAP9HnvOJqXc9gp8Ci8t_y348udZnAawTSRlDUfs3RMoCImyETKk2szQ0DBCtcYEv3f2CFK1u1AZ0xu62KJ9WSoTEDXc6kJ-UymyTCDfBdATs63QuAS0z4evHMZ5325Fx88miLiBwtcHy__lnfkhPe9DFK36Yttv5lj5dIiWYJ3-wlWBon7YXvgoU71ZlPEQPsN4ctXOwfC3Z3ewLp_sxISR__7vLJWLqhDkKnYYthiq5NcP46fEskVHRTkvwLYzVubwRx2jXabgrvVWAsBYWq_Efg7nWLM_Ag7fzACliwYPI5JAGTX3kIv68iMur_zOTtbuKmUU0iEaj4lY8zCCc4OGHBX7MMxLNNOpUlzGfHQCDi-ChniDAQr9CI1AL07xphz2u1_drh93fJxZieeqcBCwSuUGMDFBrUYReoraD0j9c5G8w6UfC2lVz_SKjpwcS_Gy5IaB1sxKfIuzHowqwG3_vU-FDiuaoZIWfnV1bUItBHwcI8R8PcPTR--hbFYgu4fvXJ9Z05eCavl5rDQtdWHUNSV0s--JHfEC3tDkw4hfsG0qVDWiYeqe5iNPL5Rdt6KUKwsBx8FlpmGMBsEpfGTeyVVWkkGcW34wxICevWMESW2F9nnx9btDPYlThdxDf_PH0-HYPaFUWrpukQqVfY-l56j2Rsm195mblTVns-yq2MEFso5d99sMzcTAwtyqnv8--ZpHFWgSaMsKgkZVMUkz3xq7WrG506HbqY08UpN9d1VO82jiCHZNvLYPHO0wAbOUHz9INQt5EiGW8uA_AiNfmXteb84kv8Mgzyo9u-_Qa1loh2imwhIkEYQPp_vhQt4Bz8ISFjRxyYl3GeIU7jT5TX8OPm_S9sRh-WcAwlH03w14s53vXeDNWGsTrbPkAIQXBJoNLa4N02fbhqoOGXf1T1MrUo0M_UALTmFZjm0VUP9gGQpLUoahFK6zbsIqpBOk4Lzk_iNW8gGr8HKvx9cB5c8dbDOevEU_MR_B-cLsLPzQQiWukw1v9FpGNbvdMvkAHmUde-OPA6CkHEjHQPOHt4CXG450rr3Wsp5KRuJLr4jWx75cXHnmC87ROQ72h6aeGV6unPe_Q-66lOEtQew18wGhNGt9prM89yeWj4hasrWsxLQU-0VQmVHx31ymrYK1bO9rSw1pbs4kXZsxzmIlrmneyxCs5zWxV-mlQPJvw0PLwr-r4kZXY80BqdfgFI9qZLPQLeOKSQtyHOtRx31uAgdwG6ZFs0FUmZY9ehwdbJWxOMPGbrciHNyD7udjIKZuBNMQc8-DydX4rpVW8mXAWCx2e1TtRXk4-RIVWbjzfaAo5NS2nPVsaFiCSKEqMRHe---hB4cimjsiBkVvebc8pCNXhrhiwCS7tYfnestrR2uNf6XqqA31huPBH39Ux_sPvKZwGHJT5XEe9VdXmnnEASUrBGRrF92KHQoV4qzu9d98tlSJfuep-nVxRKf_V9lcUkbIdsmp8tvL_bW9D4hZ5xl0C97zdEpFr52FsOgMww7f68dgO7-BNl0DkQHKOuFb7M"}, "viewability_config": [8, 5], "client_view_config": {"can_delay_log_impression": false, "use_banzai_signal_imp": true, "use_banzai_vital_imp": true}, "__isNode": "Story"}, "cursor": "AQH<PERSON>2cbCjKdkK3Jmy4hj1R2p2hYYUf-A-94XEyCuxdNPdGd1n_MLgldHL1iO6rkwXLGdz-yRaFlCBI2mcjkeO7Tq7DAKZ7_j_-T_vECdKTspWCibzt2X6NWqajBsuIqw1qLUs5FrFpjmNVgBbJyJcx3x0n_L85-FVwsPDVULr6_E7kG15KjlolcMeo_0AAKCkxEBTPv5ZELtc0pFjAOm-ESL5WBMtp5SCAZhmxuzqqIUxPWImddL5WErcCiZujjc3ZV-axU2hTIboM-ALZl-ZOSE_0We8qIQACESYGoYjs70hLh0MOFNRGohLgM06-lAPIYiLa8Ikr-s_MDbAo0yCWHOA"}]}}}, "errors": [{"message": "A server error missing_required_variable_value occured. Check server logs for details.", "severity": "WARNING"}, {"message": "A server error missing_required_variable_value occured. Check server logs for details.", "severity": "WARNING"}, {"message": "A server error missing_required_variable_value occured. Check server logs for details.", "severity": "WARNING"}, {"message": "A server error missing_required_variable_value occured. Check server logs for details.", "severity": "WARNING"}, {"message": "A server error missing_required_variable_value occured. Check server logs for details.", "severity": "WARNING"}, {"message": "A server error missing_required_variable_value occured. Check server logs for details.", "severity": "WARNING"}, {"message": "A server error missing_required_variable_value occured. Check server logs for details.", "severity": "WARNING"}, {"message": "A server error missing_required_variable_value occured. Check server logs for details.", "severity": "WARNING"}, {"message": "A server error missing_required_variable_value occured. Check server logs for details.", "severity": "WARNING"}, {"message": "A server error missing_required_variable_value occured. Check server logs for details.", "severity": "WARNING"}, {"message": "A server error missing_required_variable_value occured. Check server logs for details.", "severity": "WARNING"}, {"message": "A server error missing_required_variable_value occured. Check server logs for details.", "severity": "WARNING"}, {"message": "A server error missing_required_variable_value occured. Check server logs for details.", "severity": "WARNING"}, {"message": "A server error missing_required_variable_value occured. Check server logs for details.", "severity": "WARNING"}], "extensions": {"prefetch_uris": ["https://scontent.fdac145-1.fna.fbcdn.net/v/t39.30808-6/509600711_713436828311872_7457334169256797299_n.jpg?stp=dst-jpg_p526x296_tt6&_nc_cat=105&ccb=1-7&_nc_sid=833d8c&_nc_ohc=EMr3XDCNE34Q7kNvwEJHwin&_nc_oc=AdlEo20DiHFyB-eYTeJAwbdw3LBDEtYtxodcRBCVdK3rb1N4Kb4bM7Leif5iXe6JKHo&_nc_zt=23&_nc_ht=scontent.fdac145-1.fna&_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&oh=00_AfRlIL36ILO6DQfljG0XsV_v-eWpwZXfwOrbW2uzBEdDbA&oe=687AFC26", "https://scontent.fdac145-1.fna.fbcdn.net/v/t39.30808-1/472737163_585277764461113_2559430034335871640_n.jpg?stp=cp0_dst-jpg_s40x40_tt6&_nc_cat=108&ccb=1-7&_nc_sid=1d2534&_nc_ohc=u_KqJyrAl9EQ7kNvwHO6rR-&_nc_oc=AdlrwCu--dwJXhVx1DMmRzd9steZduRwZyiqz7LAlrVjivJiYCCX20S7ngNOwPs2fjE&_nc_zt=24&_nc_ht=scontent.fdac145-1.fna&_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&oh=00_AfTVVIBc9LcIR2D8XXRKaTzIWg7eLIezbD0CoLV1kF27xw&oe=687AF2F5"], "prefetch_uris_v2": [{"uri": "https://scontent.fdac145-1.fna.fbcdn.net/v/t39.30808-6/509600711_713436828311872_7457334169256797299_n.jpg?stp=dst-jpg_p526x296_tt6&_nc_cat=105&ccb=1-7&_nc_sid=833d8c&_nc_ohc=EMr3XDCNE34Q7kNvwEJHwin&_nc_oc=AdlEo20DiHFyB-eYTeJAwbdw3LBDEtYtxodcRBCVdK3rb1N4Kb4bM7Leif5iXe6JKHo&_nc_zt=23&_nc_ht=scontent.fdac145-1.fna&_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&oh=00_AfRlIL36ILO6DQfljG0XsV_v-eWpwZXfwOrbW2uzBEdDbA&oe=687AFC26", "label": null}, {"uri": "https://scontent.fdac145-1.fna.fbcdn.net/v/t39.30808-1/472737163_585277764461113_2559430034335871640_n.jpg?stp=cp0_dst-jpg_s40x40_tt6&_nc_cat=108&ccb=1-7&_nc_sid=1d2534&_nc_ohc=u_KqJyrAl9EQ7kNvwHO6rR-&_nc_oc=AdlrwCu--dwJXhVx1DMmRzd9steZduRwZyiqz7LAlrVjivJiYCCX20S7ngNOwPs2fjE&_nc_zt=24&_nc_ht=scontent.fdac145-1.fna&_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&oh=00_AfTVVIBc9LcIR2D8XXRKaTzIWg7eLIezbD0CoLV1kF27xw&oe=687AF2F5", "label": null}], "is_final": false, "sr_payload": {"ddd": {"hsrp": {"hsdp": {"clpData": {"2": {"r": 1, "s": 1}, "20": {"r": 1, "s": 1}, "90": {"r": 1, "s": 1}, "166": {"r": 100, "s": 1}, "433": {"r": 1}, "445": {"r": 1}, "446": {"r": 1}, "447": {"r": 1}, "604": {"r": 1}, "819": {"r": 1, "s": 1}, "1052": {"r": 1}, "1306": {"r": 1, "s": 1}, "1497": {"r": 1}, "1736": {"r": 1, "s": 1}, "1878": {"r": 1}, "2074": {"r": 1}, "2132": {"r": 1}, "2133": {"r": 1}, "2149": {"r": 1, "s": 1}, "2288": {"r": 1, "s": 1}, "2304": {"r": 1, "s": 1}, "2315": {"r": 1}, "2646": {"r": 1, "s": 1}, "2647": {"r": 1, "s": 1}, "2648": {"r": 1, "s": 1}, "2649": {"r": 1, "s": 1}, "2650": {"r": 1, "s": 1}, "2651": {"r": 1, "s": 1}, "3157": {"r": 1}, "3180": {"r": 1, "s": 1}, "3701": {"r": 1}, "3766": {"r": 1, "s": 1}, "3832": {"r": 1000, "s": 1}, "3980": {"r": 1, "s": 1}, "4171": {"r": 1, "s": 1}, "4274": {"r": 1}, "4652": {"r": 1000, "s": 1}, "4719": {"r": 1, "s": 1}, "5047": {"r": 1, "s": 1}, "6476": {"r": 1000, "s": 1}, "1743356": {"r": 1}, "1743406": {"r": 1}, "1743444": {"r": 1}, "1743449": {"r": 1}, "1743450": {"r": 1}, "1743454": {"r": 1}, "1743455": {"r": 1}, "1743456": {"r": 1}, "1743457": {"r": 1}, "1743458": {"r": 1}, "1743459": {"r": 1}, "1743460": {"r": 1}, "1743461": {"r": 1, "s": 1}, "1743465": {"r": 1}, "1743810": {"r": 1}, "1743922": {"r": 1}, "1743925": {"r": 1}, "1744057": {"r": 500, "s": 1}, "1744058": {"r": 5000, "s": 1}, "1744059": {"r": 10000, "s": 1}, "1744060": {"r": 1000, "s": 1}, "1744178": {"r": 1, "s": 1}, "1744228": {"r": 1}, "1744247": {"r": 1}, "1744249": {"r": 1}, "1755537": {"r": 1}, "1792745": {"r": 1}, "1814852": {"r": 1}, "1828945": {"r": 100, "s": 1}, "1838142": {"r": 1, "s": 1}, "1842512": {"r": 1}, "1848815": {"r": 10000, "s": 1}, "1857112": {"r": 1}, "1914651": {"r": 1}, "1950603": {"r": 1}, "1956659": {"r": 1}, "1962341": {"r": 1, "s": 1}, "1985969": {"r": 10, "s": 1}}, "gkxData": {"24": {"result": true, "hash": null}, "196": {"result": false, "hash": null}, "276": {"result": false, "hash": null}, "517": {"result": false, "hash": null}, "577": {"result": false, "hash": null}, "943": {"result": false, "hash": null}, "1154": {"result": false, "hash": null}, "1174": {"result": false, "hash": null}, "1326": {"result": false, "hash": null}, "1514": {"result": false, "hash": null}, "1624": {"result": false, "hash": null}, "1670": {"result": false, "hash": null}, "1762": {"result": false, "hash": null}, "1872": {"result": false, "hash": null}, "2099": {"result": true, "hash": null}, "2103": {"result": true, "hash": null}, "2160": {"result": false, "hash": null}, "2411": {"result": true, "hash": null}, "2750": {"result": false, "hash": null}, "2815": {"result": false, "hash": null}, "3356": {"result": false, "hash": null}, "3421": {"result": true, "hash": null}, "3739": {"result": false, "hash": null}, "4008": {"result": false, "hash": null}, "4192": {"result": false, "hash": null}, "4229": {"result": false, "hash": null}, "4337": {"result": false, "hash": null}, "4484": {"result": false, "hash": null}, "4811": {"result": false, "hash": null}, "4969": {"result": true, "hash": null}, "5043": {"result": false, "hash": null}, "5163": {"result": false, "hash": null}, "5191": {"result": false, "hash": null}, "5279": {"result": true, "hash": null}, "5303": {"result": true, "hash": null}, "5415": {"result": false, "hash": null}, "5551": {"result": false, "hash": null}, "5607": {"result": false, "hash": null}, "5608": {"result": false, "hash": null}, "5628": {"result": false, "hash": null}, "5679": {"result": false, "hash": null}, "6046": {"result": false, "hash": null}, "6275": {"result": false, "hash": null}, "6375": {"result": true, "hash": null}, "6502": {"result": false, "hash": null}, "6672": {"result": false, "hash": null}, "6934": {"result": false, "hash": null}, "7085": {"result": false, "hash": null}, "7127": {"result": false, "hash": null}, "7137": {"result": false, "hash": null}, "7170": {"result": false, "hash": null}, "7389": {"result": false, "hash": null}, "7396": {"result": false, "hash": null}, "7604": {"result": true, "hash": null}, "7671": {"result": false, "hash": null}, "7742": {"result": false, "hash": null}, "8029": {"result": false, "hash": null}, "8068": {"result": false, "hash": null}, "8287": {"result": false, "hash": null}, "8377": {"result": false, "hash": null}, "8460": {"result": false, "hash": null}, "8610": {"result": false, "hash": null}, "8794": {"result": false, "hash": null}, "8821": {"result": false, "hash": null}, "8869": {"result": false, "hash": null}, "8927": {"result": true, "hash": null}, "9063": {"result": false, "hash": null}, "9104": {"result": false, "hash": null}, "9144": {"result": false, "hash": null}, "9389": {"result": false, "hash": null}, "10182": {"result": false, "hash": null}, "10188": {"result": true, "hash": null}, "10211": {"result": false, "hash": null}, "10233": {"result": false, "hash": null}, "10255": {"result": false, "hash": null}, "10278": {"result": false, "hash": null}, "10625": {"result": false, "hash": null}, "10692": {"result": false, "hash": null}, "10725": {"result": false, "hash": null}, "10726": {"result": false, "hash": null}, "10839": {"result": false, "hash": null}, "10850": {"result": false, "hash": null}, "10863": {"result": false, "hash": null}, "10865": {"result": false, "hash": null}, "10971": {"result": false, "hash": null}, "11243": {"result": false, "hash": null}, "11386": {"result": false, "hash": null}, "11412": {"result": false, "hash": null}, "11471": {"result": false, "hash": null}, "11557": {"result": false, "hash": null}, "11582": {"result": false, "hash": null}, "11610": {"result": false, "hash": null}, "11685": {"result": false, "hash": null}, "11826": {"result": false, "hash": null}, "11869": {"result": false, "hash": null}, "12000": {"result": false, "hash": null}, "12009": {"result": false, "hash": null}, "12092": {"result": false, "hash": null}, "12124": {"result": false, "hash": null}, "12183": {"result": false, "hash": null}, "12184": {"result": false, "hash": null}, "12285": {"result": false, "hash": null}, "12286": {"result": false, "hash": null}, "12288": {"result": false, "hash": null}, "12290": {"result": false, "hash": null}, "12291": {"result": false, "hash": null}, "12292": {"result": false, "hash": null}, "12293": {"result": false, "hash": null}, "12294": {"result": false, "hash": null}, "12295": {"result": false, "hash": null}, "12296": {"result": false, "hash": null}, "12297": {"result": false, "hash": null}, "12298": {"result": false, "hash": null}, "12343": {"result": false, "hash": null}, "12522": {"result": true, "hash": null}, "12787": {"result": false, "hash": null}, "13037": {"result": false, "hash": null}, "13090": {"result": false, "hash": null}, "13292": {"result": false, "hash": null}, "13341": {"result": false, "hash": null}, "13484": {"result": false, "hash": null}, "13962": {"result": false, "hash": null}, "14400": {"result": false, "hash": null}, "14419": {"result": false, "hash": null}, "14421": {"result": false, "hash": null}, "14516": {"result": false, "hash": null}, "14917": {"result": false, "hash": null}, "14925": {"result": false, "hash": null}, "14926": {"result": false, "hash": null}, "14927": {"result": false, "hash": null}, "15016": {"result": false, "hash": null}, "15025": {"result": false, "hash": null}, "15065": {"result": false, "hash": null}, "15092": {"result": false, "hash": null}, "15236": {"result": true, "hash": null}, "15245": {"result": false, "hash": null}, "15340": {"result": false, "hash": null}, "15620": {"result": false, "hash": null}, "15745": {"result": false, "hash": null}, "15778": {"result": false, "hash": null}, "15818": {"result": true, "hash": null}, "15960": {"result": false, "hash": null}, "16062": {"result": false, "hash": null}, "20836": {"result": false, "hash": null}, "20861": {"result": false, "hash": null}, "20863": {"result": false, "hash": null}, "20864": {"result": true, "hash": null}, "20865": {"result": false, "hash": null}, "20869": {"result": false, "hash": null}, "20915": {"result": false, "hash": null}, "20916": {"result": false, "hash": null}, "20918": {"result": false, "hash": null}, "20919": {"result": true, "hash": null}, "20920": {"result": false, "hash": null}, "20921": {"result": true, "hash": null}, "20922": {"result": true, "hash": null}, "20923": {"result": true, "hash": null}, "20924": {"result": true, "hash": null}, "20925": {"result": true, "hash": null}, "20926": {"result": true, "hash": null}, "20929": {"result": true, "hash": null}, "20935": {"result": false, "hash": null}, "20936": {"result": false, "hash": null}, "20942": {"result": false, "hash": null}, "20948": {"result": true, "hash": null}, "20976": {"result": false, "hash": null}, "20977": {"result": false, "hash": null}, "20980": {"result": false, "hash": null}, "20981": {"result": false, "hash": null}, "20982": {"result": false, "hash": null}, "20983": {"result": false, "hash": null}, "20984": {"result": true, "hash": null}, "20988": {"result": true, "hash": null}, "20995": {"result": false, "hash": null}, "21000": {"result": true, "hash": null}, "21003": {"result": false, "hash": null}, "21004": {"result": false, "hash": null}, "21005": {"result": false, "hash": null}, "21037": {"result": false, "hash": null}, "21043": {"result": false, "hash": null}, "21050": {"result": false, "hash": null}, "21051": {"result": false, "hash": null}, "21053": {"result": false, "hash": null}, "21055": {"result": false, "hash": null}, "21056": {"result": false, "hash": null}, "21057": {"result": false, "hash": null}, "21058": {"result": false, "hash": null}, "21063": {"result": false, "hash": null}, "21069": {"result": false, "hash": null}, "21072": {"result": false, "hash": null}, "21089": {"result": false, "hash": null}, "21095": {"result": false, "hash": null}, "21106": {"result": false, "hash": null}, "21107": {"result": false, "hash": null}, "21117": {"result": false, "hash": null}, "21118": {"result": false, "hash": null}, "21119": {"result": false, "hash": null}, "21120": {"result": true, "hash": null}, "21121": {"result": false, "hash": null}, "21122": {"result": false, "hash": null}, "21123": {"result": true, "hash": null}, "21124": {"result": false, "hash": null}, "22681": {"result": false, "hash": null}, "22769": {"result": true, "hash": null}, "22786": {"result": false, "hash": null}, "22792": {"result": false, "hash": null}, "22802": {"result": false, "hash": null}, "22803": {"result": false, "hash": null}, "22809": {"result": true, "hash": null}, "22812": {"result": true, "hash": null}, "22826": {"result": false, "hash": null}, "22874": {"result": false, "hash": null}, "22875": {"result": false, "hash": null}, "22877": {"result": false, "hash": null}, "22883": {"result": false, "hash": null}, "22885": {"result": false, "hash": null}, "22968": {"result": false, "hash": null}, "22979": {"result": false, "hash": null}, "23010": {"result": false, "hash": null}, "23017": {"result": false, "hash": null}, "23018": {"result": false, "hash": null}, "23023": {"result": false, "hash": null}, "23039": {"result": true, "hash": null}, "23044": {"result": false, "hash": null}, "23157": {"result": false, "hash": null}, "23174": {"result": false, "hash": null}, "23176": {"result": false, "hash": null}, "23177": {"result": false, "hash": null}, "23180": {"result": false, "hash": null}, "23181": {"result": false, "hash": null}, "23182": {"result": false, "hash": null}, "23184": {"result": false, "hash": null}, "23185": {"result": false, "hash": null}, "23186": {"result": false, "hash": null}, "23188": {"result": false, "hash": null}, "23189": {"result": false, "hash": null}, "23190": {"result": false, "hash": null}, "23191": {"result": false, "hash": null}, "23192": {"result": false, "hash": null}, "23193": {"result": false, "hash": null}, "23195": {"result": false, "hash": null}, "23196": {"result": false, "hash": null}, "23197": {"result": false, "hash": null}, "23198": {"result": false, "hash": null}, "23199": {"result": false, "hash": null}, "23200": {"result": false, "hash": null}, "23204": {"result": true, "hash": null}, "23209": {"result": false, "hash": null}, "23210": {"result": false, "hash": null}, "23219": {"result": false, "hash": null}, "23229": {"result": false, "hash": null}, "23413": {"result": false, "hash": null}, "23414": {"result": false, "hash": null}, "23874": {"result": false, "hash": null}, "24215": {"result": false, "hash": null}, "24257": {"result": true, "hash": null}, "24281": {"result": false, "hash": null}, "24282": {"result": false, "hash": null}, "24285": {"result": false, "hash": null}, "24286": {"result": false, "hash": null}, "24290": {"result": false, "hash": null}, "24292": {"result": true, "hash": null}, "24293": {"result": false, "hash": null}, "24344": {"result": false, "hash": null}, "24349": {"result": false, "hash": null}, "24351": {"result": false, "hash": null}, "24360": {"result": true, "hash": null}, "24364": {"result": false, "hash": null}, "24365": {"result": false, "hash": null}, "24366": {"result": false, "hash": null}, "24367": {"result": false, "hash": null}, "24377": {"result": false, "hash": null}, "24380": {"result": false, "hash": null}, "24565": {"result": false, "hash": null}, "24695": {"result": false, "hash": null}, "25572": {"result": false, "hash": null}, "26216": {"result": false, "hash": null}, "26332": {"result": false, "hash": null}, "26340": {"result": false, "hash": null}, "26341": {"result": false, "hash": null}, "26342": {"result": false, "hash": null}, "30214": {"result": true, "hash": null}}, "ixData": {"14175": {"sprited": 1, "spriteCssClass": "sx_e5affe", "spriteMapCssClass": "sp_8tFGEkghbyq"}, "20314": {"sprited": 1, "spriteCssClass": "sx_c8329e", "spriteMapCssClass": "sp_tAmNuzekRZp"}, "20323": {"sprited": 1, "spriteCssClass": "sx_b76a6a", "spriteMapCssClass": "sp_tAmNuzekRZp"}, "111404": {"sprited": 1, "spriteCssClass": "sx_06f24d", "spriteMapCssClass": "sp_8tFGEkghbyq"}, "111412": {"sprited": 1, "spriteCssClass": "sx_b515ef", "spriteMapCssClass": "sp_8tFGEkghbyq"}, "111418": {"sprited": 1, "spriteCssClass": "sx_ff23a8", "spriteMapCssClass": "sp_8tFGEkghbyq"}, "111421": {"sprited": 1, "spriteCssClass": "sx_faab84", "spriteMapCssClass": "sp_8tFGEkghbyq"}, "347019": {"sprited": 1, "spriteCssClass": "sx_79dc71", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "347020": {"sprited": 1, "spriteCssClass": "sx_bad4b8", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "347022": {"sprited": 1, "spriteCssClass": "sx_4959fc", "spriteMapCssClass": "sp_8tFGEkghbyq"}, "352555": {"sprited": 1, "spriteCssClass": "sx_10187d", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "352559": {"sprited": 1, "spriteCssClass": "sx_10af00", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "386426": {"sprited": 1, "spriteCssClass": "sx_e7aff5", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "386427": {"sprited": 1, "spriteCssClass": "sx_944c3d", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "386428": {"sprited": 1, "spriteCssClass": "sx_45fa8b", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "386429": {"sprited": 1, "spriteCssClass": "sx_348a5d", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "386436": {"sprited": 1, "spriteCssClass": "sx_d75cb8", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "386438": {"sprited": 1, "spriteCssClass": "sx_227c85", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "386439": {"sprited": 1, "spriteCssClass": "sx_f6cb60", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "386622": {"sprited": 1, "spriteCssClass": "sx_9c4bbc", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "386749": {"sprited": 1, "spriteCssClass": "sx_56d8dd", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "387252": {"sprited": 1, "spriteCssClass": "sx_0833b5", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "387254": {"sprited": 1, "spriteCssClass": "sx_b7d568", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "389933": {"sprited": 1, "spriteCssClass": "sx_6c658c", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "389935": {"sprited": 1, "spriteCssClass": "sx_e04a43", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "389937": {"sprited": 1, "spriteCssClass": "sx_6f1f25", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "389939": {"sprited": 1, "spriteCssClass": "sx_95d40c", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "389941": {"sprited": 1, "spriteCssClass": "sx_f971f5", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "389943": {"sprited": 1, "spriteCssClass": "sx_caacda", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "389945": {"sprited": 1, "spriteCssClass": "sx_dc4d10", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "389947": {"sprited": 1, "spriteCssClass": "sx_cef01a", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "389949": {"sprited": 1, "spriteCssClass": "sx_a2e152", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "389951": {"sprited": 1, "spriteCssClass": "sx_0b5956", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "389953": {"sprited": 1, "spriteCssClass": "sx_21824d", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "389955": {"sprited": 1, "spriteCssClass": "sx_643df5", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "389957": {"sprited": 1, "spriteCssClass": "sx_9b8c9d", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "389959": {"sprited": 1, "spriteCssClass": "sx_3cc608", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "478231": {"sprited": 1, "spriteCssClass": "sx_8fb96b", "spriteMapCssClass": "sp_wsAeMRTz8WF"}, "478232": {"sprited": 1, "spriteCssClass": "sx_a26211", "spriteMapCssClass": "sp_3yB9VfKfiHu"}, "478233": {"sprited": 1, "spriteCssClass": "sx_15a960", "spriteMapCssClass": "sp_3yB9VfKfiHu"}, "478237": {"sprited": 1, "spriteCssClass": "sx_795fe6", "spriteMapCssClass": "sp_3yB9VfKfiHu"}, "481767": {"sprited": 1, "spriteCssClass": "sx_4a8e2c", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "484391": {"sprited": 1, "spriteCssClass": "sx_b5cc8a", "spriteMapCssClass": "sp_b5TSgXwYbw7"}, "484394": {"sprited": 1, "spriteCssClass": "sx_87434e", "spriteMapCssClass": "sp_3yB9VfKfiHu"}, "484398": {"sprited": 1, "spriteCssClass": "sx_379172", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "484399": {"sprited": 1, "spriteCssClass": "sx_d15931", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "484757": {"sprited": 1, "spriteCssClass": "sx_05e6f5", "spriteMapCssClass": "sp_b5TSgXwYbw7"}, "484865": {"sprited": 1, "spriteCssClass": "sx_dcdd2c", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "485103": {"sprited": 1, "spriteCssClass": "sx_a5738b", "spriteMapCssClass": "sp_8tFGEkghbyq"}, "487090": {"sprited": 1, "spriteCssClass": "sx_94a6ef", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "487562": {"sprited": 1, "spriteCssClass": "sx_5dc1b2", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "487626": {"sprited": 1, "spriteCssClass": "sx_979452", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "487657": {"sprited": 1, "spriteCssClass": "sx_527762", "spriteMapCssClass": "sp_tAmNuzekRZp"}, "490501": {"sprited": 1, "spriteCssClass": "sx_5fa6d6", "spriteMapCssClass": "sp_8tFGEkghbyq"}, "491221": {"sprited": 1, "spriteCssClass": "sx_a4861e", "spriteMapCssClass": "sp_3yB9VfKfiHu"}, "491223": {"sprited": 1, "spriteCssClass": "sx_55b39d", "spriteMapCssClass": "sp_3yB9VfKfiHu"}, "491230": {"sprited": 1, "spriteCssClass": "sx_831030", "spriteMapCssClass": "sp_tAmNuzekRZp"}, "492454": {"sprited": 1, "spriteCssClass": "sx_852931", "spriteMapCssClass": "sp_3yB9VfKfiHu"}, "492485": {"sprited": 1, "spriteCssClass": "sx_d1bae8", "spriteMapCssClass": "sp_3yB9VfKfiHu"}, "492488": {"sprited": 1, "spriteCssClass": "sx_af9bb4", "spriteMapCssClass": "sp_8tFGEkghbyq"}, "492518": {"sprited": 1, "spriteCssClass": "sx_7fdeb3", "spriteMapCssClass": "sp_b5TSgXwYbw7"}, "492521": {"sprited": 1, "spriteCssClass": "sx_01a5cb", "spriteMapCssClass": "sp_b5TSgXwYbw7"}, "492533": {"sprited": 1, "spriteCssClass": "sx_a56f49", "spriteMapCssClass": "sp_b5TSgXwYbw7"}, "492536": {"sprited": 1, "spriteCssClass": "sx_0fd15f", "spriteMapCssClass": "sp_8tFGEkghbyq"}, "492572": {"sprited": 1, "spriteCssClass": "sx_20fc67", "spriteMapCssClass": "sp_b5TSgXwYbw7"}, "492575": {"sprited": 1, "spriteCssClass": "sx_4ca8f0", "spriteMapCssClass": "sp_b5TSgXwYbw7"}, "492713": {"sprited": 1, "spriteCssClass": "sx_d3bc47", "spriteMapCssClass": "sp_tAmNuzekRZp"}, "497239": {"sprited": 1, "spriteCssClass": "sx_c68669", "spriteMapCssClass": "sp_tAmNuzekRZp"}, "497241": {"sprited": 1, "spriteCssClass": "sx_4c5246", "spriteMapCssClass": "sp_8tFGEkghbyq"}, "497242": {"sprited": 1, "spriteCssClass": "sx_547124", "spriteMapCssClass": "sp_tAmNuzekRZp"}, "497897": {"sprited": 1, "spriteCssClass": "sx_315632", "spriteMapCssClass": "sp_tAmNuzekRZp"}, "498146": {"sprited": 1, "spriteCssClass": "sx_398ac7", "spriteMapCssClass": "sp_3yB9VfKfiHu"}, "498857": {"sprited": 1, "spriteCssClass": "sx_5ec0f9", "spriteMapCssClass": "sp_8tFGEkghbyq"}, "505565": {"sprited": 1, "spriteCssClass": "sx_7b067f", "spriteMapCssClass": "sp_3yB9VfKfiHu"}, "505620": {"sprited": 1, "spriteCssClass": "sx_40077d", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "506962": {"sprited": 1, "spriteCssClass": "sx_b230d5", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "507176": {"sprited": 1, "spriteCssClass": "sx_406812", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "508557": {"sprited": 1, "spriteCssClass": "sx_05af31", "spriteMapCssClass": "sp_8tFGEkghbyq"}, "508558": {"sprited": 1, "spriteCssClass": "sx_d3cbbd", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "509922": {"sprited": 1, "spriteCssClass": "sx_1e0237", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "509926": {"sprited": 1, "spriteCssClass": "sx_d4f87f", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "512647": {"sprited": 1, "spriteCssClass": "sx_27feb8", "spriteMapCssClass": "sp_3yB9VfKfiHu"}, "514454": {"sprited": 1, "spriteCssClass": "sx_50ec74", "spriteMapCssClass": "sp_3yB9VfKfiHu"}, "545517": {"sprited": 1, "spriteCssClass": "sx_60777b", "spriteMapCssClass": "sp_b5TSgXwYbw7"}, "547774": {"sprited": 1, "spriteCssClass": "sx_9edbc6", "spriteMapCssClass": "sp_3yB9VfKfiHu"}, "547775": {"sprited": 1, "spriteCssClass": "sx_2eae00", "spriteMapCssClass": "sp_3yB9VfKfiHu"}, "559830": {"sprited": 1, "spriteCssClass": "sx_3cc624", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "579030": {"sprited": 1, "spriteCssClass": "sx_b5267d", "spriteMapCssClass": "sp_tAmNuzekRZp"}, "618351": {"sprited": 1, "spriteCssClass": "sx_9bcd82", "spriteMapCssClass": "sp_tAmNuzekRZp"}, "621399": {"sprited": 1, "spriteCssClass": "sx_242083", "spriteMapCssClass": "sp_b5TSgXwYbw7"}, "642536": {"sprited": 1, "spriteCssClass": "sx_37c236", "spriteMapCssClass": "sp_3yB9VfKfiHu"}, "648667": {"sprited": 1, "spriteCssClass": "sx_e00d8b", "spriteMapCssClass": "sp_3yB9VfKfiHu"}, "648669": {"sprited": 1, "spriteCssClass": "sx_b32f6c", "spriteMapCssClass": "sp_3yB9VfKfiHu"}, "659288": {"sprited": 1, "spriteCssClass": "sx_a26519", "spriteMapCssClass": "sp_b5TSgXwYbw7"}, "679141": {"sprited": 1, "spriteCssClass": "sx_b04f01", "spriteMapCssClass": "sp_3yB9VfKfiHu"}, "681770": {"sprited": 1, "spriteCssClass": "sx_1b3db6", "spriteMapCssClass": "sp_tAmNuzekRZp"}, "785426": {"sprited": 1, "spriteCssClass": "sx_ea2030", "spriteMapCssClass": "sp_3yB9VfKfiHu"}, "859827": {"sprited": 1, "spriteCssClass": "sx_2c4dad", "spriteMapCssClass": "sp_tAmNuzekRZp"}, "1478524": {"sprited": 1, "spriteCssClass": "sx_a5b2b3", "spriteMapCssClass": "sp_RgfsuHFJyhD"}, "1664042": {"sprited": 1, "spriteCssClass": "sx_b313bd", "spriteMapCssClass": "sp_8tFGEkghbyq"}, "1876411": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yd/r/3mD7kKai_7W.gif", "width": 12, "height": 12}, "1876412": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/y1/r/mHADa0fT0mI.gif", "width": 16, "height": 16}, "1876413": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yV/r/ZY0eC865SgX.gif", "width": 20, "height": 20}, "1876414": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/M3mvaC7u8oH.gif", "width": 24, "height": 24}, "1876415": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yL/r/hVe2HmwMRpE.gif", "width": 32, "height": 32}, "1876416": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yC/r/yFaaylccZ5L.gif", "width": 48, "height": 48}, "1876418": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yk/r/96GJYGbUDCJ.gif", "width": 72, "height": 72}, "1876419": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yS/r/NiR8M1k4AVU.gif", "width": 12, "height": 12}, "1876420": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/ym/r/FNERtXIk9xp.gif", "width": 16, "height": 16}, "1876421": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yJ/r/l2FWxc8ihQj.gif", "width": 20, "height": 20}, "1876422": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yo/r/Io_N1z4MXYh.gif", "width": 24, "height": 24}, "1876423": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yD/r/-1hifBvDgEQ.gif", "width": 32, "height": 32}, "1876424": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yG/r/RcIiVWWukEr.gif", "width": 48, "height": 48}, "1876426": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yP/r/9ISCYYcy94m.gif", "width": 72, "height": 72}, "1876427": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yU/r/MStXnCtsaSe.gif", "width": 12, "height": 12}, "1876428": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/y6/r/dw2egiKdoVV.gif", "width": 16, "height": 16}, "1876429": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yA/r/1DbfjOftY0d.gif", "width": 20, "height": 20}, "1876430": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yi/r/2uPGz8a6lb6.gif", "width": 24, "height": 24}, "1876431": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yw/r/60r9oPEvxiL.gif", "width": 32, "height": 32}, "1876432": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yM/r/NlAFhiEx3a1.gif", "width": 48, "height": 48}, "1876434": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yx/r/uzrQzxgD_Bg.gif", "width": 72, "height": 72}, "1876435": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yM/r/TtXj9IXnkoK.gif", "width": 12, "height": 12}, "1876436": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yy/r/HNs8yq0QiXE.gif", "width": 16, "height": 16}, "1876437": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yT/r/ay_drQe6StD.gif", "width": 20, "height": 20}, "1876438": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/y4/r/iACDMhAROS_.gif", "width": 24, "height": 24}, "1876439": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yy/r/WEhNL1y2zoZ.gif", "width": 32, "height": 32}, "1876440": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/y-/r/mAeZkO4yhqj.gif", "width": 48, "height": 48}, "1876442": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yl/r/79uB7ciX8vY.gif", "width": 72, "height": 72}, "1876443": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yG/r/dzn6it4Fw3p.gif", "width": 12, "height": 12}, "1876444": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yc/r/wqjQpFb4tea.gif", "width": 16, "height": 16}, "1876445": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yk/r/yy3mR2PXKrn.gif", "width": 20, "height": 20}, "1876446": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yl/r/gTdm7zPKz-c.gif", "width": 24, "height": 24}, "1876447": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/ym/r/kdaft251gQ_.gif", "width": 32, "height": 32}, "1876448": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yG/r/6-FTd4KBtOk.gif", "width": 48, "height": 48}, "1876450": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yq/r/Tks_lRPtYc-.gif", "width": 72, "height": 72}, "1876451": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yK/r/Bys0xcVibDa.gif", "width": 12, "height": 12}, "1876452": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/Wk0dcHGH6EG.gif", "width": 16, "height": 16}, "1876453": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/ys/r/aOTs7vt2hEc.gif", "width": 20, "height": 20}, "1876454": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yh/r/wVjfNbGZ3CH.gif", "width": 24, "height": 24}, "1876455": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yw/r/oT6wM_vuQNQ.gif", "width": 32, "height": 32}, "1876456": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yp/r/ac61i44rSWK.gif", "width": 48, "height": 48}, "1876458": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yg/r/ZH27Vvjc9-u.gif", "width": 72, "height": 72}, "1940508": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/y3/r/ycQ2OPoZwUA.gif", "width": 64, "height": 64}, "1940509": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yg/r/8kyIVWHZW-b.gif", "width": 64, "height": 64}, "1940510": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yK/r/8gPN8wBD9yB.gif", "width": 64, "height": 64}, "1940511": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yG/r/WtK_u51t3nM.gif", "width": 64, "height": 64}, "1940512": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yj/r/JYwEre3ewp7.gif", "width": 64, "height": 64}, "1940513": {"sprited": 0, "uri": "https://static.xx.fbcdn.net/rsrc.php/v4/yu/r/M2HDLLPAUWl.gif", "width": 64, "height": 64}}, "metaconfigData": {"15": {"value": false}, "34": {"value": false}, "57": {"value": 0}, "61": {"value": false}, "70": {"value": false}, "73": {"value": false}, "84": {"value": ""}, "89": {"value": false}, "99": {"value": false}, "100": {"value": 0}, "101": {"value": 0}, "102": {"value": 0}, "103": {"value": false}, "109": {"value": 0}, "110": {"value": 0}, "111": {"value": 0}, "113": {"value": ""}, "119": {"value": 0}, "120": {"value": false}, "121": {"value": 0}, "122": {"value": 0}, "123": {"value": 0}, "124": {"value": 0}, "125": {"value": 0}, "126": {"value": 0}, "127": {"value": 0}, "128": {"value": 0}, "129": {"value": 0}, "130": {"value": ""}, "131": {"value": 0}, "133": {"value": 0}, "134": {"value": 0}, "143": {"value": false}, "154": {"value": false}, "164": {"value": false}, "179": {"value": false}, "191": {"value": false}, "206": {"value": false}}, "qexData": {"104": {"r": null}, "128": {"r": null}, "344": {"r": null}, "388": {"r": null}, "398": {"r": false, "l": "J{\"qeid\":\"0\",\"u\":\"\",\"t\":\"fb_acting_account\",\"gks\":[],\"qe\":null}"}, "488": {"r": null}, "503": {"r": false, "l": "J{\"qeid\":\"0\",\"u\":\"\",\"t\":\"fb_acting_account\",\"gks\":[],\"qe\":null}"}, "564": {"r": null}, "576": {"r": null}, "586": {"r": null}, "617": {"r": null}, "1022": {"r": null}, "1382": {"r": null}, "1463": {"r": null}, "1675": {"r": null}, "1696": {"r": null}, "1777": {"r": null}, "2067": {"r": null}, "3492": {"r": true, "l": "J{\"qeid\":\"0\",\"u\":\"\",\"t\":\"fb_acting_account\",\"gks\":[],\"qe\":null}"}, "3493": {"r": true, "l": "J{\"qeid\":\"0\",\"u\":\"\",\"t\":\"fb_acting_account\",\"gks\":[],\"qe\":null}"}, "3494": {"r": null, "l": "J{\"qeid\":\"0\",\"u\":\"\",\"t\":\"fb_acting_account\",\"gks\":[],\"qe\":null}"}, "3495": {"r": null, "l": "J{\"qeid\":\"0\",\"u\":\"\",\"t\":\"fb_acting_account\",\"gks\":[],\"qe\":null}"}, "3496": {"r": false, "l": "J{\"qeid\":\"0\",\"u\":\"\",\"t\":\"fb_acting_account\",\"gks\":[],\"qe\":null}"}, "4012": {"r": null, "l": "J{\"qeid\":\"0\",\"u\":\"\",\"t\":\"fb_acting_account\",\"gks\":[],\"qe\":null}"}, "4013": {"r": null, "l": "J{\"qeid\":\"0\",\"u\":\"\",\"t\":\"fb_acting_account\",\"gks\":[],\"qe\":null}"}, "4014": {"r": null}, "4494": {"r": null}, "4495": {"r": null}, "4529": {"r": null}, "4613": {"r": false}, "4807": {"r": null}, "4899": {"r": null}, "4923": {"r": null}, "4924": {"r": false, "l": "J{\"qeid\":\"0\",\"u\":\"\",\"t\":\"fb_acting_account\",\"gks\":[],\"qe\":null}"}, "4925": {"r": false, "l": "J{\"qeid\":\"0\",\"u\":\"\",\"t\":\"fb_acting_account\",\"gks\":[],\"qe\":null}"}, "4926": {"r": null}}, "qplData": {"54": {"r": 500}, "93": {}, "106": {}, "232": {"r": 50}, "419": {"r": 1}, "622": {"r": 100}, "702": {"r": 500}, "1099": {"r": 1000}, "1269": {"r": 1000}, "1585": {"r": 1000}, "1811": {"r": 100}, "1891": {"r": 50}, "2242": {"r": 10000}, "2258": {"r": 1000000}, "2267": {"r": 10000}, "2268": {"r": 10000}, "2396": {"r": 10000}, "2408": {"r": 10000}, "2853": {"r": 10000}, "2916": {"r": 1}, "2919": {"r": 100}, "2960": {"r": 1000}, "3037": {"r": 1}, "3289": {"r": 1000}, "3455": {"r": 100}, "4389": {"r": 250}, "4690": {"r": 100}, "4694": {"r": 10000}, "4750": {"r": 1}, "4977": {"r": 4}, "5054": {"r": 1}, "5737": {"r": 100}, "5992": {"r": 50}, "6043": {"r": 1000}, "6153": {"r": 100}, "6177": {"r": 100}, "6204": {"r": 7}, "6752": {"r": 50}, "6895": {"r": 10000}, "7056": {"r": 50}, "7109": {"r": 100}, "7148": {"r": 50}, "7493": {"r": 1000}, "7813": {"r": 1}, "8121": {"r": 1}}, "justknobxData": {"9": {"r": true}, "88": {"r": false}, "144": {"r": true}, "222": {"r": true}, "317": {"r": 16}, "432": {"r": true}, "450": {"r": true}, "494": {"r": true}, "592": {"r": 11}, "593": {"r": 11}, "669": {"r": true}, "684": {"r": true}, "706": {"r": false}, "783": {"r": false}, "794": {"r": false}, "843": {"r": false}, "922": {"r": false}, "1047": {"r": false}, "1051": {"r": false}, "1070": {"r": false}, "1141": {"r": false}, "1175": {"r": false}, "1211": {"r": false}, "1212": {"r": false}, "1417": {"r": true}, "1731": {"r": 2}, "1749": {"r": false}, "1806": {"r": true}, "1831": {"r": 10}, "1832": {"r": 100}, "2217": {"r": true}, "2233": {"r": true}, "2269": {"r": true}, "2382": {"r": true}, "2436": {"r": 10}, "2437": {"r": 50}, "2552": {"r": false}, "2595": {"r": 0}, "2635": {"r": true}, "2644": {"r": true}, "3323": {"r": true}, "3326": {"r": true}, "3332": {"r": false}, "3337": {"r": false}, "3371": {"r": true}, "3409": {"r": true}, "3532": {"r": false}, "3604": {"r": true}, "3609": {"r": true}, "3752": {"r": true}, "3809": {"r": true}, "3926": {"r": 1}, "3935": {"r": false}, "4040": {"r": false}, "4079": {"r": true}, "4109": {"r": true}, "4525": {"r": 1}, "4529": {"r": false}, "4648": {"r": false}, "4694": {"r": false}, "4860": {"r": true}}}, "hblp": {"consistency": {"rev": 1024713728}, "rsrcMap": {"WzW470F": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y9/r/IAqkaiye5bw.js"}, "Sr6Aahz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ym/r/T_fk27AZHrR.js"}, "dEY7RHQ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yL/r/34S4B2-sKti.js"}, "+UR2TQl": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iqgr4/y1/l/en_GB/YibVnePHJ4E.js"}, "UneIqIg": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y3/r/-6ZLh_tX-N3.js"}, "DFsCJbL": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yJ/r/fiJr_Axw0iw.js"}, "fmlBfb1": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yh/r/79WgFFWeOD2.js"}, "F3OAUNE": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y6/r/MqJVc3uQHMD.js"}, "6a0VWiV": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yq/r/ZpsiQ7PjoRj.js"}, "kSXx4CL": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yx/r/Nz55O7_5fhc.js"}, "S3gsgXq": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yN/r/n85FZpxH-E1.js"}, "3bxzL8e": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yi/r/BXzVOm7qxFl.js"}, "iTq47MB": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iM6u4/yA/l/en_GB/0aVCOgnhkKP.js"}, "lAY3K2N": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4imlR4/yo/l/en_GB/a51kzkacXPp.js"}, "zJl4N4d": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/9eXl6XCqXkD.js"}, "m3EFigt": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ys/r/Hmte5f7Pei1.js"}, "7lO0k+R": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i_QM4/yc/l/en_GB/w8ihwfGgIbr.js"}, "lmV4NqA": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i4554/yq/l/en_GB/izevQXOdhzo.js"}, "dhV7BOC": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i1MJ4/y9/l/en_GB/iuXjoahacD-.js"}, "vTyhxMS": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ig0N4/yb/l/en_GB/V44WIAeQSf3.js"}, "fQlL/9u": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4il5S4/y_/l/en_GB/u9TZKU_PD33.js"}, "rpZd0S1": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4icg64/ye/l/en_GB/Rs2tS1wECQF.js"}, "5vmkPLG": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yi/r/NHwIoN3xnxF.js"}, "P4YRYHe": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iDWo4/yM/l/en_GB/2hFmIJggDA2.js"}, "e3t7ot3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/FbpERqJo5_J.js"}, "w6R9/0W": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ij9m4/yi/l/en_GB/GV4gzhTfExz.js"}, "/c8g/m5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yz/r/rjIP4WsNgxY.js"}, "HY7qeqU": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y7/r/LhQDOA1zjcE.js"}, "sw9OsZD": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yl/r/W4DBeXb_f7f.js"}, "Dy29lk1": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yx/r/hMMp4JAX-l6.js"}, "Z48a0we": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yk/r/k41pvEdPaxZ.js"}, "Iak57b2": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yZ/r/TlOezGbcGm4.js"}, "dWqb64m": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ys/r/B_wU69W0Jc1.js"}, "CPtfj1t": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yN/r/amrVOjAPeJv.js"}, "72T5i3p": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yV/r/oxPc5Qj90YA.js"}, "ZA3YWhT": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yO/r/p1_0DjskL7m.js"}, "BRJlUbI": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y9/r/kXpWswBtXcb.js"}, "yk7F21/": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y4/r/5ocGXaHKCWQ.js"}, "o6YiIz7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iLl54/yT/l/en_GB/WMozrbDkqcu.js"}, "ewD+n24": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iMVo4/yf/l/en_GB/FJP1ZkgNkNV.js"}, "WL0kL9K": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yu/r/RmUEzGYIUnv.js"}, "180F4gD": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iZTv4/yZ/l/en_GB/Tl9xlEXEf3N.js"}, "xIwLeog": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yA/l/0,cross/yPTowCrNi2x.css"}, "57AAsPx": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yS/r/GcGohsBN02c.js"}, "tjrJbTN": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yj/r/j87ZhvXe1NU.js"}, "sd317eV": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4itIJ4/yN/l/en_GB/DvxnNCfQpjc.js"}, "14NLUiM": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/SKG0i5r7siu.js"}, "g4pE26R": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yE/r/5qpIZ8Pik2T.js"}, "1W5pzvT": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yt/r/258K246rDQx.js"}, "OX3rQWx": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y4/r/VUFt5BBcFNy.js"}, "4M6DKEv": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iFy04/ye/l/en_GB/ovWknnw1rOf.js"}, "UY5v54z": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yO/r/6VHPIoN7bVn.js"}, "ayU3D2R": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yd/r/V86ZLctu-Qz.js"}, "gLAO/oD": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yf/r/lOie3klmPiF.js"}, "1UJ2zTs": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iUUR4/yU/l/en_GB/VPb_6TVYPbf.js"}, "1QKkLEP": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yg/r/vcmuZaEYeEL.js"}, "sGWXkfX": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iKjI4/yB/l/en_GB/mHuEZ9uGK-8.js"}, "/atJEDq": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yf/r/gyavQe48WU_.js"}, "XMP8fLK": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iGjC4/yN/l/en_GB/pNknbB31HAa.js"}, "SPrY11N": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yw/r/JQZHHDuFO8I.js"}, "DEpFhF6": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/R0yzH-_CCy0.js"}, "NpoviDS": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yY/r/rzpiHM2ijkQ.js"}, "npBeFQ+": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iz-h4/yK/l/en_GB/O1KM0Ci87K0.js"}, "kQeX049": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/y4/l/0,cross/GsjlBxd9h-5.css"}, "0QXB4CG": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y2/r/TdHssoHPta7.js"}, "+qnDr4D": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yN/r/76NZQ8uaYE3.js"}, "8ELCBwH": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ye/r/VRzSVH5iU-V.js"}, "PUI4833": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yP/r/kksryeHnrWZ.js"}, "YgCHL7R": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ym/r/uBxtZE8YKvD.js"}, "b0aAln3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4igOQ4/y8/l/en_GB/qw7tRUrXGPc.js"}, "O9+1txH": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4itf_4/yO/l/en_GB/aqUzrAgXSX4.js"}, "pzNXisi": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yd/r/JFZINK_qza-.js"}, "7iQLlvN": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4igTQ4/yA/l/en_GB/tHQjV4XzVM4.js"}, "UqAQ+gE": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yJ/r/eXtua9vEbT9.js"}, "EQ+D3D1": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yq/r/Yzakz2XfSVl.js"}, "ffSCdTU": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ye/r/RsIK_lMhOLN.js"}, "+h8O0IF": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yH/r/CPHgkbiaByX.js"}, "NyizVr9": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yt/r/ERE-lC8nzLW.js"}, "IMHff1e": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y2/r/n8Iv7rh-DxG.js"}, "5ZcHlAi": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yK/r/E0YmbkQ-BXA.js"}, "qxd9Mr2": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yS/r/hzQOQk-jHYu.js"}, "oesShPU": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yr/r/zHHiXQCtp6j.js"}, "7WTlwQe": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yH/r/yzACpYMBk5i.js"}, "Z+xFe+J": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yK/r/SLy1nUadQCp.js"}, "c1LU2Ag": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4il8E4/yd/l/en_GB/tynfQri3CWt.js"}, "mb53ol3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ihd74/yV/l/en_GB/KFbteUR3erw.js"}, "XEVo1TC": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yR/r/xnr2UQs4qHp.js"}, "GBDhdTg": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yg/r/gQj3fbjkcks.js"}, "16xXSzc": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yA/r/eCq56t0-pC2.js"}, "A7UBH6O": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yb/r/Z3GYsoxNARP.js"}, "WwozWWD": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iOv54/yo/l/en_GB/vJrLKB2YzRa.js"}, "D2KTWwH": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yW/r/nevg33mhmvq.js"}, "lmr4KaY": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ib9v4/y2/l/en_GB/f41_brxftA6.js"}, "Hwr1+zT": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yZ/r/y5ukJVAdpUU.js"}, "e3+9Dwu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iEhe4/yH/l/en_GB/69mzCrEPYJA.js"}, "l9zbJTz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4idke4/yc/l/en_GB/62y1EhdQE9o.js"}, "ITUTFV8": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yH/r/Esq9dU3Uc42.js"}, "hWhBSh/": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iRaT4/yW/l/en_GB/4Q1LhledOf8.js"}, "J8Yb073": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iPxZ4/y-/l/en_GB/gyvX4j9QUyH.js"}, "8zIXEiN": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iKIK4/yO/l/en_GB/eZ8R8w01L1-.js"}, "ID4Gev5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yj/r/PIecVPtnFjg.js"}, "bJ4IPi7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iHox4/yQ/l/en_GB/E7cOG70qLGI.js"}, "kAEXZ2t": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yn/r/9uKSBvVkxkv.js"}, "3Mh28ej": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iKCx4/yD/l/en_GB/yMxnOGVeMUp.js"}, "B5RL2E6": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yM/r/FEXzhorVm27.js"}, "+SRG5gz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yL/r/NurBaqoyB22.js"}, "qPcyJPO": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yR/r/NicoPyq3HVC.js"}, "M1MW+Si": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yp/r/xEzO3VSeqno.js"}, "siBRbiX": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yT/r/D8_28vpYrar.js"}, "XTuHwRI": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y2/r/rJqlTszVYKq.js"}, "OxEZzdV": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yu/r/ZgC7ZkylXwX.js"}, "eejXjh7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4if4B4/yu/l/en_GB/clP6LxltYb_.js"}, "We0Y2Xt": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yf/r/QKY-N0BP5K7.js"}, "0xq3hyW": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yt/r/dVuN8L3bUIe.js"}, "CMtpSOn": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iw_s4/yO/l/en_GB/IABLmmrjRLg.js"}, "fiFBisF": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ye/r/earSC-Vst_U.js"}, "PsaRjY2": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ifmY4/y2/l/en_GB/1VDTXPSUeME.js"}, "HgdKfeq": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4irGn4/yF/l/en_GB/kdZfzz1gMVw.js"}, "i/nQFci": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yv/r/CfevcaQWwl4.js"}, "gRod0+S": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yo/r/1TKzns6OIrA.js"}, "oRWvHq5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iHMp4/yo/l/en_GB/YTpJBCvIgBf.js"}, "CzuOsFB": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ibQ24/yN/l/en_GB/lFOOoo-upRo.js"}, "ATXcRzk": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y3/r/QLzTbFl1bmN.js"}, "wpWTO9l": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4izsM4/yR/l/en_GB/V7aSA7-RHwO.js"}, "iIXSARe": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yk/r/ViwaFKdytYt.js"}, "opltOAS": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y3/r/N_uTzVGfkrA.js"}, "WTATY6l": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yF/r/DgBhaSFwW4F.js"}, "Y0wky9V": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iKeq4/yn/l/en_GB/FFHCbUVT0Jq.js"}, "9UUBvip": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y6/r/3bAE-BkwBDz.js"}, "4TpiAWy": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i95O4/yb/l/en_GB/E_Grpdh2ZOJ.js"}, "0cNVXD5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iF5h4/ys/l/en_GB/KlzqJmVBmis.js"}, "LPvEl4J": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iPbY4/yB/l/en_GB/vQHOZESE-8y.js"}, "r0yfBAv": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iiz44/yd/l/en_GB/rChO3UmbiRd.js"}, "Pwssuq+": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/ym/l/0,cross/Kdxj9-132iS.css"}, "dIVoPFL": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4imLv4/yp/l/en_GB/_rW3C7CSMMK.js"}, "9OKm3xD": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yl/r/8DDjtBSPhNc.js"}, "xAecuAA": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ys/r/qe3syVwqeFi.js"}, "OoD1C8w": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yr/r/1k6s6j6lJzz.js"}, "uPJCx6c": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i-2i4/yA/l/en_GB/z0mN8V3Rktz.js"}, "c81IIcE": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i5r34/y3/l/en_GB/fa2TmwGt1io.js"}, "invUTkA": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iWQU4/yE/l/en_GB/CVNqvY2nF6T.js"}, "AD4aRIi": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/8Kc0ja3GELN.js"}, "vZ5yXE+": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iGOv4/yf/l/en_GB/jDKKNrj8OJH.js"}, "S1ZCgTh": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yJ/r/CMN23XmuaL7.js"}, "abQMyJn": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ilyc4/y7/l/en_GB/p9dN0gD8Ywg.js"}, "ZeahUe3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iua24/yO/l/en_GB/HYj6n6yoW6r.js"}, "bJqu9gO": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ir8C4/yV/l/en_GB/oz1rW3ORhkH.js"}, "TsZtkcX": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yC/r/xfoW0okgHKJ.js"}, "CZNyp6s": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yP/r/CJ3rj_LQxhi.js"}, "SW/Pwbx": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4it3g4/yT/l/en_GB/WTq3l9Zj766.js"}, "Hsjl2uk": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iA-K4/yq/l/en_GB/P26DZunArpH.js"}, "TPiPhxZ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i4wP4/yO/l/en_GB/c56uNf8hHFP.js"}, "/PCpjt7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4izVR4/yf/l/en_GB/_9fpVjkv9ib.js"}, "yPJRMPV": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yl/r/0HPO6kOfTfg.js"}, "bqek3DH": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yw/r/-ySWlTNgjy7.js"}, "ePOrzDE": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yp/r/nMN6iOtpblz.js"}, "gZqXm1L": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y6/r/mmKpBgZUecz.js"}, "mVu9uoT": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i6RN4/yK/l/en_GB/Yk5cuxbazwn.js"}, "XAsVRCe": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yh/r/uHVVwRCKBe9.js"}, "lg+gEkr": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yb/r/FFPuBHSheiu.js"}, "BqErpsc": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ie9o4/y0/l/en_GB/HQyMBwjOAvZ.js"}, "0vtBQlg": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yS/r/RMYXk0XmR5G.js"}, "iOD9YzB": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yx/r/sumLn4hgd9V.js"}, "DfdTYKR": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yk/r/1SgoHfO8AdP.js"}, "A3LbXJ4": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yv/r/nWjVbUXVan9.js"}, "Wka4woV": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y6/r/u4f-qXgSljD.js"}, "OCGi48V": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iqjt4/yh/l/en_GB/Mj7goqYsTgV.js"}, "VbFxeir": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ihfJ4/yh/l/en_GB/fxCQjANTbwL.js"}, "1jjlZqt": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y4/r/FxCwYLze60h.js"}, "6Vx6j82": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ye/r/sMd3v_-EMDS.js"}, "QeWWSDP": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yA/r/5or3xX0qrWc.js"}, "wBUs0s5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ii7j4/y3/l/en_GB/XGVNQepSrmQ.js"}, "S+XsLoT": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yO/r/yl7GrxmhB0z.js"}, "83nXCxW": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yd/r/hwKgpv7lado.js"}, "A1wWm2g": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yn/r/QEZJ4MBJTrX.js"}, "j9ZV4iT": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yg/r/XVsfZcNsj11.js"}, "lMB51eB": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yc/r/4z8a8zrxPK2.js"}, "3u6SGwj": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ys/r/Oxt27P--_RB.js"}, "bbLCiUL": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yL/r/c-Hub190iqa.js"}, "G+uFvTN": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yA/r/EZngvV4xpEH.js"}, "GW5//HY": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/myu0YsAT87R.js"}, "z/N7nw+": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yK/r/QqynDi7aVr_.js"}, "Nrc2fWn": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yS/r/DvaAZuO77i-.js"}, "B4wSUBY": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yu/r/4Gsb5Rx_Xs4.js"}, "igSbXP4": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yx/l/0,cross/Icy4bHuCmtQ.css"}, "HOs3TiS": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iCWj4/yC/l/en_GB/5OGRIcxSMDs.js"}, "d3Fxe/y": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/y4/l/0,cross/ngi6ND1Mg8N.css"}, "6iU1NsB": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yM/r/Zf4bSXIfXVq.js"}, "6ro5LAu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yk/r/3JKepQv_QmY.js"}, "SLiUPR7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yY/r/1pRdT-tpK4z.js"}, "YLJ6WbM": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i_Ou4/y6/l/en_GB/pHKjPQeIo-I.js"}, "yUqVmZ2": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yi/r/03YxQA42w6B.js"}, "vhI7dSU": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ihN64/yr/l/en_GB/_X8CfY_eUwT.js"}, "0F/X7qA": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yt/r/VYM_uu1exNn.js"}, "rJmmbBH": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yB/l/0,cross/tF4W__GXHsR.css"}, "wAmzd0a": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/ya/l/0,cross/7GMiGn23v6r.css"}, "D6p9uYq": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yQ/l/0,cross/FaMlFJODe4K.css", "nonblocking": 1}, "oPVWR39": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i5BE4/ys/l/en_GB/aZFr3_-cK08.js"}, "88qrwWE": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ivuW4/yG/l/en_GB/WVn7byPFWpK.js"}, "iq6n5cY": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yh/r/nb1a_23IJCO.js"}, "SWVFARg": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yH/r/GZal5gObCjz.js"}, "rHjQ135": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iucI4/yw/l/en_GB/yOhSEKRsQVN.js"}, "htFX3lV": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yd/r/AMRUASySH4L.js"}, "IkSdoO7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yT/r/Y4Dbi8zwjqj.js"}, "0OiaLuP": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yg/r/6HV6QNRXRwX.js"}, "LujvAKc": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yK/r/hrXk3OUqYch.js"}, "HtrS4yL": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y_/r/6mBrKj2Oo5-.js"}, "JI94oxz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/bkGLlzinqg0.js"}, "pkj/9PY": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yb/r/0kc8ly060Ii.js"}, "cJoQBHY": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ioSB4/yt/l/en_GB/QRqmwpHHnUV.js"}, "OLHcH4M": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yy/r/2d85sBFLLal.js"}, "b0t9eMa": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y1/r/GVIgywb5WvB.js"}, "jOON9K3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y-/r/jQ0SOAu2_fy.js"}, "p5o4jN8": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yg/r/gT2Tsq6bn7E.js"}, "eDvbndE": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4irPe4/yP/l/en_GB/lBiuLWY00lx.js"}, "JtvHoGB": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yJ/r/D0x9wilMFsA.js"}, "BWxeAad": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iLpP4/yq/l/en_GB/L8jM0QSPnB8.js"}, "UHS/25B": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yW/r/ljBMVHyij03.js"}, "8jW6Z3P": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yf/l/0,cross/_xl5FyGRlcD.css"}, "AM4VhF0": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yi/l/0,cross/UQq-8dYdQWN.css", "nonblocking": 1}, "lr6ew3o": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4isYu4/yI/l/en_GB/vAHxW3kkIV1.js"}, "aLfc+ws": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yp/r/9bKdHN4WGTa.js"}, "JI/ewOb": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y_/r/9gGjGjfD4G4.js"}, "drkow/K": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y5/r/ZjeEirFDSyT.js"}, "fqo+KiO": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y7/r/BtunWxzJi5G.js"}, "NrtiKph": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iJo14/yJ/l/en_GB/Okmqa8r-Y-X.js"}, "EqgZAyz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i--64/yk/l/en_GB/js2VnqWryv3.js"}, "04RdAeA": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yc/r/CEIgI_1aTDI.js"}, "8WrV1qw": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yn/r/yb0SzZ9z4a1.js"}, "tuQh/4O": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/f5sRDrExbRC.js"}, "fzG10qZ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yh/r/IT0cg9x8Ili.js"}, "+zxVnNA": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iNwc4/yA/l/en_GB/ZioBOcbBDJN.js"}, "FUpSXjV": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iirW4/yw/l/en_GB/3ohx7TQrxyw.js"}, "UaqI1yF": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ivsD4/ym/l/en_GB/XD63p_HJNd1.js"}, "J223IwG": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ihXm4/y-/l/en_GB/gS3RyIN1pdg.js"}, "sW0y7ab": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i9Od4/yK/l/en_GB/7biW2iqp2Em.js"}, "tRo7x53": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iSy64/yr/l/en_GB/qGiIIoyrWv_.js"}, "PLFVLtw": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ilp44/y8/l/en_GB/ZYYEV-44Enr.js"}, "HLK1Yuz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i7mS4/y7/l/en_GB/6XOS_HWD92x.js"}, "LHUE5Ub": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4idy94/yN/l/en_GB/Iob7Pl9tgbR.js"}, "hvHkdIC": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yZ/r/8LKGQfW9hND.js"}, "ipSndkH": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/lzbGbjDZv4j.js"}, "mOYAbny": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4inuJ4/yh/l/en_GB/aL6R6FoIkNV.js"}, "OpIFwqF": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y3/r/bFe5QjDqLWg.js"}, "r3BqJsA": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yZ/r/mlNDIaHqX2C.js"}, "dU+KFGJ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yq/r/kA23zod0J_q.js"}, "Jjqfj8u": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iF0F4/yq/l/en_GB/YuRm6QWxzFW.js"}, "aOCC6m8": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yS/r/qbixZ1-TsQC.js"}, "Bj9SjIs": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iXnp4/yI/l/en_GB/sQynn3lYO5A.js"}, "Q55CU+C": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yW/l/0,cross/ZoQdFRpo-fY.css"}, "Vk5fAdB": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yZ/r/IgruAI4X9rd.js"}, "AM+Jr2d": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yQ/r/8-_Ga7RkOzo.js"}, "lD27Xzj": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4idYv4/yX/l/en_GB/dd2Ys6F7MDH.js"}, "mRJ4GEO": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yf/r/iFTJcFU3YXc.js"}, "stdBzsD": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yA/r/-ldXpgmZsUO.js"}, "Dw6m1KW": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yi/r/JHcXxcblF1A.js"}, "W2q32qT": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yE/r/knffZclfnRJ.js"}, "grCpL4r": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yX/r/Of8IDER-EHm.js"}, "TEBB51l": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y6/r/GK85eSIDaIe.js"}, "3MiTeE0": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yw/r/Kuw_swWZs2a.js"}, "yzm5eQH": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/YTJ8KQYfWHl.js"}, "IpbaOAi": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i3om4/yg/l/en_GB/uZH3zgTUd2i.js"}, "T1ub4PD": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i1h34/y7/l/en_GB/VYG11VRl8xx.js"}, "EgFdLES": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yi/l/0,cross/bKVZP875qLR.css"}, "V/2V00p": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iw7W4/yH/l/en_GB/gGIsdFqgMiv.js"}, "3e1KAxr": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iI744/y2/l/en_GB/rg7Jycoczdk.js"}, "GPSC/0w": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yH/r/09CnXcUR1N6.js"}, "wrc5x5+": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yp/r/KQVmrdLoKMm.js"}, "z8CauSQ": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yy/l/0,cross/omKy7Y4Rgv_.css", "nonblocking": 1}, "o4X9gYT": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yl/r/EtBS_CqZRBA.js"}, "E7T5in+": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y-/r/A6AMXRRbe03.js"}, "w8fOUMC": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iFRl4/yi/l/en_GB/0fqtyx_JUf8.js"}, "hthh40l": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yb/r/sE5UBvphjQD.js"}, "U853Zz1": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yx/r/1DFvIpryPO3.js"}, "9YQ3krq": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iEzL4/yz/l/en_GB/uWJb-hfkNU0.js"}, "XSlreDW": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ijKY4/yA/l/en_GB/UCZCLsbJ9FO.js"}, "bj5dUVI": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iEuQ4/yT/l/en_GB/XqKZXCpB6Ax.js"}, "EbAqmk8": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yC/r/uS9g73pD_29.js"}, "6ihXblm": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yH/r/8BWfLfdDsB9.js"}, "j3JrM2X": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yM/r/881Cbnpdl8w.js"}, "hm/ESgP": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yd/r/RTUP_sImoZj.js"}, "j5IOl0D": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y7/r/l_zHp9bmxf3.js"}, "+6BpX6j": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yj/r/Se4G1tnF88u.js"}, "VVDgRKK": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y6/r/7Ov5wiSMyU-.js"}, "x3i+GY/": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yN/r/bhwRxGXXUvf.js"}, "3V2Xot9": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iTI24/yv/l/en_GB/PtavfyV9h6G.js"}, "y8Z1UMs": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/NmSq1nB8lfR.js"}, "iAW5jrp": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ys/r/66l7awMfR50.js"}, "b3XSUfw": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y3/r/z4WIl_AuYFm.js"}, "lhOq/wP": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yo/r/xPdaqdJ-Jw5.js"}, "QKr50F5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y_/r/6cc3BIOmeI_.js"}, "3WIA9mq": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yk/r/pGVPaOopPBp.js"}, "Mzh7qY1": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yJ/r/oCSroGqH1H8.js"}, "k5Eukrh": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yY/r/rRf1TkG7YAC.js"}, "b4oleUw": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yH/r/lmGXz0Z2kjX.js"}, "7IsiFCm": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ya/r/VFlvKNuX_E6.js"}, "1a6uHIy": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yV/r/94ywdVoC3Yh.js"}, "DbZbqd1": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yc/r/bODj-7VClaQ.js"}, "wEZMRUe": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yn/r/QMlXGt2nXdF.js"}, "uEwd8v3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yf/r/8SGs2jb5ied.js"}, "H6EFwGg": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yp/r/quIats86s7j.js"}, "f0rk7UV": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y-/r/s6080aeBbU7.js"}, "T0TuY8T": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yq/r/Zy7IdrPG-OK.js"}, "BlO2f94": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yj/r/frp85quqWOB.js"}, "aiFodw4": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iS0Q4/y0/l/en_GB/VOhtFlQ4MGs.js"}, "3b7F9Fu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yz/r/QLpqsjJXEu7.js"}, "MSn/ro1": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yO/r/E997svCjDwJ.js"}, "w2tLMLh": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yy/r/YV3_uEAPdEG.js"}, "v89pUz/": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ya/r/gDq5wlNoXQz.js"}, "B5UE81g": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4isO54/yH/l/en_GB/LgnJ7HPfZ0V.js"}, "tuF4Wpw": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yg/r/Z3Bq-qeTIAe.js"}, "vdWCh0h": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yy/r/90QGbRcit-q.js"}, "gpyjHy7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yk/r/0tkudgZisKT.js"}, "crXs0Q4": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yD/r/j8ZssOCzR5Z.js"}, "fjtgrsO": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yy/r/UHO5nT3gxef.js"}, "Rk2KMgE": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y5/r/b8Q5tFfURBl.js"}, "AdNCKua": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yG/r/8v0oT0nYNM5.js"}, "E1UtRr5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yB/r/e18jzu7zmU_.js"}, "91drT1d": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y_/r/uuM6RV9U-l6.js"}, "jjcqkJ7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iKY44/y9/l/en_GB/GChocAEnty_.js"}, "+thM/Xl": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yr/r/u8gsrWtTbRm.js"}, "ToGQR9n": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y0/r/Nh9yLpqDUk4.js"}, "9bdSDSi": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iX6H4/yb/l/en_GB/QISejI8odW_.js"}, "w7SNbDz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yu/r/UdrHio9mOlI.js"}, "eXGLDE8": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yy/r/6GcTtEy7iXF.js"}, "AQtFf3d": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yh/r/nuH1kDUfuhI.js"}, "+jcKpiI": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yW/r/_HYU1N__zFb.js"}, "9EWnid5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yH/r/W77AT8EQxMr.js"}, "FyxLQT5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yo/r/5bl09vRHXGb.js"}, "TUoM+2I": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yX/r/p9cEQiHjDna.js"}, "MmrbWXs": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y_/r/OPQ6JzfUOg_.js"}, "rlZI7fU": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yN/r/qM4-b-r6bBx.js"}, "wyLpwS2": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yS/r/yMoSrSiA6k4.js"}, "J04JOWm": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yK/r/H39G_sBrJDU.js"}, "zpd+lZp": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yu/r/xywjIId_gt6.js"}, "9J16fdf": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yE/r/T6pvL7wokUF.js"}, "SgX7DpB": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yW/r/Z96fENEvRmx.js"}, "wJJKbNe": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/YgXqCbBcfHE.js"}, "uhOTCO2": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yN/r/4xsLRSXr9F8.js"}, "F6N4Tl0": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yV/r/2pALpeS_A89.js"}, "ZWs49iU": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yC/r/P7XcG6KtgpG.js"}, "5rgV2S5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yH/r/AsNKkevkkHZ.js"}, "tUwh8B3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yu/r/hAb7qHyhE8f.js"}, "oeDbDS2": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ya/r/tFDNBzoMonI.js"}, "czC3MSn": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yf/r/DveAU5Vcn15.js"}, "ad9dOy7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yt/r/_UJA2DQWSFu.js"}, "Kumtycu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yE/r/6vubCgUDC38.js"}, "hknTMg3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yi/r/wBhWNEhLagF.js"}, "BzzM/0G": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y_/r/rGf2F7z49GO.js"}, "pKc3SfL": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iaNq4/yv/l/en_GB/koE-kH0Ldtk.js"}, "teoK0BU": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yv/r/OpCSGaNPBai.js"}, "nXvbb9G": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ye/r/bkCZm5Y5mRF.js"}, "IiCdgPi": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yo/r/vRdWg_IVsv4.js"}, "XU7mIlf": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yU/r/QB7wuKgHgUd.js"}, "bscwShE": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yv/r/DGM23rsKlph.js"}, "NGKxIVe": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iLAQ4/yv/l/en_GB/flb_3H4qsf7.js"}, "52gvWl+": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yl/r/KWBhWE5fECJ.js"}, "KbhcDqz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yC/r/x8zVKPZ6s1G.js"}, "cTG7mXF": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y0/r/vXkS9F4JReB.js"}, "Lo6LeaT": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ye/r/_PO-F7B-Zrw.js"}, "YP/NSCj": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i5Un4/yF/l/en_GB/ryH83CQpj4X.js"}, "Qf/pxyz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yz/r/XFAmHLHFZ9-.js"}, "ou8vrnV": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yD/r/doyx3k5XY9j.js"}, "fJEdBdk": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yw/r/aebnsszUuen.js"}, "kChrIuq": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yJ/r/zRU-i59rNwV.js"}, "kW3qAGC": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yY/r/tQpkE4U62_n.js"}, "dbTcVEe": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ye/r/f92TGCesDFn.js"}, "3SQTX1Z": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yy/r/cdhg7-HRHuf.js"}, "Ah82WxJ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y_/r/1LWS7WOlLUn.js"}, "mMrwbxd": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iSi94/yc/l/en_GB/2fEA8sfYmbf.js"}, "0Y0KFas": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yy/r/LwQRYMM9Agd.js"}, "/ABZdos": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yL/l/0,cross/csbGj66wDjc.css"}, "FtI3fMk": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yU/r/3Tbj2xE7htR.js"}, "UmtttOw": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yg/r/Bmax7JjZ4Vq.js"}, "sZy8rgw": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yK/r/TOUZkg0qUTZ.js"}, "MV8u0Us": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yl/r/k1-a75D-zI8.js"}, "gXI9hSg": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yh/r/4cmP5x2wxyw.js"}, "cn+oFmt": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yR/r/NMh7dKmhQG3.js"}, "WRi+XjB": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yb/r/SZmLcNcr0rb.js"}, "j92QuVO": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yo/r/VY0UvoDbBk3.js"}, "KaeAqwN": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yg/r/78a7CCLwzWc.js"}, "Okt9G7P": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y0/r/atwXbJrm9UR.js"}, "beAhtR3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yq/r/QPbpK4WVpyB.js"}, "D0YY98H": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iDR_4/yT/l/en_GB/hAWN9Xp-xGG.js"}, "F1kHwNx": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ya/r/CWu12lmthZv.js"}, "8LsYrQu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yC/r/qepo5mVqfKP.js"}, "aOri+ZE": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yb/r/AonRYfi4IYz.js"}, "uvPpGOx": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iLWZ4/yF/l/en_GB/837zGza_CHs.js"}, "wnQtOGx": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yT/r/sT5MJzLyVri.js"}, "aTAtdou": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yh/r/VhF8s3BE0_8.js"}, "7aa1Fdl": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yG/r/8V2aqSR7VbZ.js"}, "3o470sW": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y_/r/-gTSHEIW4JI.js"}, "Jkg1gUa": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yw/r/5LniY7AhmgQ.js"}, "ymyJvvz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/MBIL7wLVj1I.js"}, "0bJo5d7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yl/r/qCt6jNcGv3k.js"}, "JBWWYvz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iCdY4/yz/l/en_GB/Ir4hRu7U3-J.js"}, "8DeZfan": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ye/r/COB6Br8wDwa.js"}, "T/exXR2": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ys/r/jMqFe4ljf5B.js"}, "4l/kqi1": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yQ/r/7nymqJmvAi1.js"}, "crlHctM": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yK/r/uVkFHSFtM2A.js"}, "a+P5NjD": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yx/r/I-nuFC0PKJQ.js"}, "irTQXQJ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yn/r/dk7RwWjIaFQ.js"}, "eQbdGRF": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yF/r/drZjBvK6DUi.js"}, "OvavXX8": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y1/r/26SJ0xqHMoa.js"}, "H1pwYZ5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yL/r/jn9_vKFCfUU.js"}, "Isuitf7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y-/r/vDas-WTJ_xD.js"}, "EHj7FXT": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y6/r/Eo_gKgxFW-u.js"}, "ZsccH25": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yn/r/gOh8lhpnaCJ.js"}, "uuVJiAO": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y7/r/5PM9b7H3VU2.js"}, "dzcb3sK": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/z4oKB2uWCsV.js"}, "gKlND5D": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iyhr4/ya/l/en_GB/i-W9zNxmkw6.js"}, "ZeYteZM": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y4/r/7Y99s2vvq8a.js"}, "a1zAOjG": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yo/r/q5omdOEOncy.js"}, "HJEX75Z": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yD/r/gMTjAskewtY.js"}, "pPCIXUq": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yg/r/zsY9zqv68ap.js"}, "G4o+GSn": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/PPzeX-Es1so.js"}, "1ncthyc": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y1/r/uS3xaopR_l8.js"}, "w/oNOc7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y0/r/oa8Ij5j1Lu7.js"}, "2Bh8vWz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yv/r/n5DpZme90M1.js"}, "P7T+uth": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4izOo4/yB/l/en_GB/DeKZpJwZKhe.js"}, "6FTJTfz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yR/r/4mIKC6yg7mz.js"}, "yZTdcQl": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yw/r/ur7dI6yHlWU.js"}, "pZG73rv": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yd/r/a1DJkOYVSNA.js"}, "cXn2xZ2": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y-/r/ufe9Q8-fR3B.js"}, "LWimt5f": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ym/r/-THaqYJCcJ8.js"}, "FlCXYyu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ym/r/GpOxEAGsfc5.js"}, "J0/PDGU": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y_/r/haTOd2ugURk.js"}, "EK4A83Z": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yk/r/J5kMJaGpC4C.js"}, "You46xR": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iJPH4/yw/l/en_GB/KX-7eDs4LJ6.js"}, "ynimsRk": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yE/r/EJBdBUjWW_Y.js"}, "mmPABcg": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yU/r/7S-hAzS2-vc.js"}, "NY64HKi": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ye/r/jc2p92RLoPD.js"}, "tzvC6uI": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y5/r/LMywV5goGb2.js"}, "HwjNP73": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yV/r/o0P438KMPVe.js"}, "Wfb7Uxg": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/Y-uhjHECJLE.js"}, "Jr982T0": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yL/r/OGQqgAY8hRC.js"}, "CbIAYxX": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ym/r/ryU0Il8DpXH.js"}, "Al+X9aL": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yo/r/e7VMjsrjpr1.js"}, "Y3JOSsz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yE/r/Wbi2fQja1FR.js"}, "/bxf4Bs": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yP/r/GtRUdAOwSW8.js"}, "wpOING9": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iG114/yE/l/en_GB/7tW3ys31I6f.js"}, "XJzdbGM": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iEIh4/yz/l/en_GB/O0nP-CI12sK.js"}, "1LX32Cd": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yQ/r/qH9nSA1uvPx.js"}, "TdsFOZs": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yr/r/LXvqbYopg9_.js"}, "U2LU8tl": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yc/r/2OE1DR42zRy.js"}, "8g1v/5N": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yz/r/wwi_RQMu8sx.js"}, "4225gpN": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yv/r/ilsYt2uGndQ.js"}, "OSYB+Sw": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yd/r/p1K1kdDPMKE.js"}, "Rr/uhbz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yi/r/fY2lWhekv4O.js"}, "Xti7cDw": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i6FS4/yV/l/en_GB/LoWKj26zEY0.js"}, "b5d0Exw": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yo/r/AGJkmdPCjqt.js"}, "I9lIRpJ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i5Un4/yK/l/en_GB/HF-bvk-boxj.js"}, "0nLWnMp": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yV/r/oPOdSvEGtBy.js"}, "HjuKI8Y": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/U-oK9hRnM3a.js"}, "rucx1xA": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yY/r/1sknJpdsVRn.js"}, "NPTupMd": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yw/r/8QVfXv8yTIw.js"}, "HSJ2m+o": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i2g14/yZ/l/en_GB/wW8drsnDwK5.js"}, "ZiDtKCK": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yi/r/znpFryczi5T.js"}, "C9n16BW": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ist-4/yJ/l/en_GB/K2sdbg42c1Q.js"}, "H/vFQCj": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iP6d4/yq/l/en_GB/MOgithNShcv.js"}, "EU14oMb": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yS/r/TJyT5lVygva.js"}, "yHaO1MS": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yJ/r/lJXBVgMBtj0.js"}, "t/9M/qj": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yl/r/_pYKkgbhct4.js"}, "2QvhSD9": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ye/r/c6c92KbiO0_.js"}, "fy+TE/Z": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yw/r/vjCr5sEyn9b.js"}, "jnmmy2C": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y1/r/peT8sZxx7lr.js"}, "n+VYnE7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yp/r/suQJtLJZU4_.js"}, "hz8/3dw": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y-/r/tc7wcipUSRB.js"}, "ThFx62w": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yy/r/CiTRe7DkmH7.js"}, "lDAQ46j": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yn/r/cJtwm-Gh0_L.js"}, "MJkWs/f": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yX/r/oZmUl-Hy7HL.js"}, "llFC02g": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i2Dr4/yz/l/en_GB/DmMhg6AXKez.js"}, "07iuC/n": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y7/r/OtZ_15Cdut5.js"}, "0+ePjAs": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y_/r/QWiu7Iv1Ols.js"}, "pLG1x/s": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yS/r/1POe7gstGDQ.js"}, "BHuOWHb": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yR/r/X6EkbYPYAhN.js"}, "ej4cOKg": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yQ/r/Cm5NVPjQwwT.js"}, "Cbd/faM": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yu/r/pN8aFexgOE3.js"}, "dB+yA7N": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yT/r/-Yvdm78ipM9.js"}, "HMu7jnh": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yG/r/YzvFAdvZgxs.js"}, "JSIuWWq": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yn/r/zNCNcl3ICjh.js"}, "4NsC1nN": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yF/r/5O4B6F09aoC.js"}, "Z/hfX+e": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iJOY4/y8/l/en_GB/GGvGaOBEq4m.js"}, "Oa2MvhD": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iPbY4/ym/l/en_GB/hK-jj5p4iEk.js"}, "sdwuU+q": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y5/r/QvQd_a-RWyM.js"}, "1mB+7R9": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iqNu4/yA/l/en_GB/PUPa7xcI8Zk.js"}, "CLT3HW+": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yO/r/ReC6fbKEJ9C.js"}, "QwATVC8": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yy/r/Ugw44SZ9SP9.js"}, "HK71Xlq": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yQ/r/6CkibHzgE-9.js"}, "ak7ajHN": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i65G4/yq/l/en_GB/nY2OuCU738N.js"}, "w5VDOKj": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y2/r/JWXfwXHzSHz.js"}, "flaGBH0": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yk/r/SXFxYb4BKSN.js"}, "Bo2moEN": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4irtN4/yw/l/en_GB/w1HPJa8CNxT.js"}, "eIyodOJ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i85n4/y7/l/en_GB/__m6JVKstpR.js"}, "ogtmnDZ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yj/r/RKhBpVCcHoP.js"}, "rzZCc5i": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yc/r/Qlgn3vbz8-l.js"}, "rvCjzhQ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yO/r/KoNFGMrBDqE.js"}, "2q3LQ0V": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y9/r/RF6VD6yjj3d.js"}, "/pXVZGK": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iRSk4/y3/l/en_GB/HvelN9ZwcCA.js"}, "PmgpMnw": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ys/r/b8DzDz7P07V.js"}, "OIFp0O5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yH/r/4oFMeLyMCZp.js"}, "gKLxRZF": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y2/r/JhWw3h9sXdF.js"}, "Vo5JV7+": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iHXY4/y2/l/en_GB/pNIXAzYh0ZD.js"}, "r5knPo9": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yc/r/6i8XgN6r4fo.js"}, "sBkVxJb": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yX/r/ZO3Brr_P9UZ.js"}, "mP27mRt": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yM/r/HacfMBzsNkk.js"}, "+pq9h4P": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y-/r/t12Ipfu0-HA.js"}, "pk/6RTI": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yu/r/i69zR_12-Cs.js"}, "rUOdupL": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yZ/r/voQ470ouBDK.js"}, "x3dkzIR": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yD/r/id-SVX8os3j.js"}, "UI4blME": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ihS-4/yM/l/en_GB/8sP4GnMH5lq.js"}, "HfF/1+h": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yu/r/XY8Kjm5NaPY.js"}, "UYx2o/B": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y4/r/NjAK7BWAOgP.js"}, "CIIih5i": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yM/r/xV4iRKiW8-7.js"}, "Xcsq+ML": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ikoF4/y7/l/en_GB/xyNlLdE5dKm.js"}, "wSUYGA4": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y0/r/5_h_ytRGhxa.js"}, "pm+eZOd": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yv/r/5XDkZx1UB-c.js"}, "5kcPWn6": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yA/r/ST0PhaacV9T.js"}, "pu90sT5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yN/r/XGmA9oLOZUz.js"}, "JRALkqh": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yY/r/6OX3BAmN86F.js"}, "Pj5WtcB": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i2PV4/yM/l/en_GB/IuK_WQRq4Ep.js"}, "3bUPF2u": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y6/r/Rhc9d2qNy9F.js"}, "tWb0de7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y3/r/a_mpSQDmLM2.js"}, "Qqt6z0n": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yA/r/kcp8DoVkgd_.js"}, "YEUqtaP": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/9YErtPEr2MU.js"}, "yPjOCCT": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i7Te4/y8/l/en_GB/Koy7f1evEyw.js"}, "TpRJy9k": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yM/r/-zAx-VwuJyl.js"}, "MBYHbGD": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y3/r/Oa5MIZB6Fjp.js"}, "O26H5ty": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yK/r/EWczn1t3XP1.js"}, "X1Wm31c": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iMNV4/yX/l/en_GB/EOg68PYQp_p.js"}, "+YBYEis": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ihAw4/y4/l/en_GB/7ZyE1UOcJcL.js"}, "5XRhPxa": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i6_D4/yb/l/en_GB/SV0PIdfvTyO.js"}, "KtatUH1": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yO/l/0,cross/E8R35iLqOAL.css"}, "uK1//Sc": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yW/l/0,cross/DNOtz_DNVcR.css"}, "aPGHTvj": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yj/r/Ni_ZAs7I0fF.js"}, "rHg1jVv": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yj/l/0,cross/6aPc6hwVhLx.css"}, "/TOwoYy": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yI/l/0,cross/BvmCzMLJvTG.css"}, "AEgCBxe": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yO/r/UYh12FewifI.js"}, "Zb52TyN": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yp/r/dwg5_0zScsO.js"}, "l43YXO1": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yp/r/8mstJxK7ww9.js"}, "6XOaqlh": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yB/l/0,cross/t6msme_0vsE.css"}, "wSA190q": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yM/r/xp2N9Pxwzsu.js"}, "80mLdXp": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yE/l/0,cross/GYtSJKrO32H.css", "nonblocking": 1}, "f04weBo": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y9/r/OWVy0Vs_vLr.js"}, "jy2K3/U": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yx/r/z6tITVMCbP8.js"}, "qmdIyIg": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yT/r/IazxQdFmNp3.js"}, "CljYTWJ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yA/r/PzAABQcY8qA.js"}, "+3NZ9s9": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ys/r/x97edTzBme7.js"}, "75qNOzp": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yg/r/twQst2uKaEC.js"}, "wX3bFTF": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yX/r/jTiD4_6O9ym.js"}, "hIp9SUj": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yX/r/hAFnnErk4d6.js"}, "fGCsB99": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iixH4/yF/l/en_GB/FRo4u665Qkq.js"}, "96dXq+T": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y5/r/UUwhWbnvntq.js"}, "noluY+u": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yR/r/JT8wqUPSWgE.js"}, "b5WCEnv": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ye/r/Nr0Y6Q-4KQl.js"}, "zM8vC1x": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/NXE_R2naJ__.js"}, "ERarnfS": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ys/r/h1_NGpT7COT.js"}, "hR/UgiK": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yT/r/rGv4sO4rPdG.js"}, "eyUgJID": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4icE74/y6/l/en_GB/Xk0k_UkH-fw.js"}, "5eYaAeW": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yT/r/G-seZBFandN.js"}, "aGNjC2c": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yU/r/BmiV0CZwK1Y.js"}, "BA2H2yQ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yA/r/aYIvqcHGyRa.js"}, "9KQCjPh": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ikp14/yI/l/en_GB/Jwzo53aygBw.js"}, "ponOpY3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y3/r/ZiwrqLy6Ve4.js"}, "wGf0JcO": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ipEN4/y6/l/en_GB/8Nc0y2-F2sO.js"}, "H6f46ct": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iLEr4/yT/l/en_GB/3TyGu6sgMAa.js"}, "xLbNMjG": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iOmK4/yf/l/en_GB/ffjo0zCOLJZ.js"}, "mZqdoer": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yX/r/d1IX7R0rbMY.js"}, "bUBPFT3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yV/r/MDFrwqM5I2e.js"}, "YaaDqxu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y6/r/0W1d7L21-K0.js"}, "Yfxy6UD": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yO/r/BRPD3u8TkBA.js"}, "3u2o3pg": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ye/r/WHDCN0FM-qg.js"}, "9u5eYU+": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yM/r/piwTEdmscQj.js"}, "COKidXF": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4idBq4/y4/l/en_GB/gKyIOt6tkNd.js"}, "4I6cCSh": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y9/r/kbEFtYezzYl.js"}, "7EdE7i/": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ib9A4/yQ/l/en_GB/oLOcnyUAPqf.js"}, "9iMfnWQ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y6/r/M3hstqhdh_n.js"}, "TNitpBq": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i1Yb4/yM/l/en_GB/akTZSB3E1WH.js"}, "8MX6YxQ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yf/r/_vI4wq55hM_.js"}, "AoLnH66": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iJDP4/y0/l/en_GB/q0UxtDGnqiv.js"}, "86Use7c": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yD/r/kgXlrPYz-oL.js"}, "gnMDDkv": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yO/r/g_wOpwPMC6Y.js"}, "pmJ826k": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yd/r/yxJw1Q51jk5.js"}, "KQQ5AN3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yY/r/N0lfPGhlpkW.js"}, "CKZDJwj": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yz/r/wEjnApc67W6.js"}, "EgcMz3j": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iiOw4/yh/l/en_GB/qYRH4MHMBRx.js"}, "MYtQmWa": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yD/r/tGZ94ZMgoY9.js"}, "V0WZAQ2": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i9SY4/yI/l/en_GB/cDIe7ygGXfz.js"}, "NZrfaO0": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iLIA4/yC/l/en_GB/h6vtATLwF6O.js"}, "fYVQRSa": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ym/r/UP01ESXGYuJ.js"}, "mhgGz12": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yy/r/ioDXeQslVCt.js"}, "xb5RJlG": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yw/r/opHX7suucYq.js"}, "wRLYtDS": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iU0n4/yI/l/en_GB/ksUsg86KVFT.js"}, "yko+HF4": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yi/r/EqWg-06CtDG.js"}, "tnwf3TS": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ys/r/7QFWFZuJgkQ.js"}, "E+0TNBB": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yl/r/_wywMDiI0-s.js"}, "nwTZAZv": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yz/r/aUS5qrqaoSC.js"}, "agvWKTN": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yv/r/71rt6VXjorc.js"}, "8vZh7Cu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yH/r/aLO6jfmeHeN.js"}, "gSqpwIH": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yD/r/2BkLDheH3_B.js"}, "Xbh+qlw": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yb/r/3a8H9BKXQO4.js"}, "fOQuQJd": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y1/r/cVhqydAydwX.js"}, "FuidHPD": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y5/r/t13DrDSGrxL.js"}, "bfgBg1O": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y4/r/ApAV27X3n1E.js"}, "XtzIGcc": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yr/r/lXqU5MkCzfT.js"}, "AalEfJ7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yD/r/iw5OYSJrUET.js"}, "ocvWTxp": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iMs_4/y6/l/en_GB/9CTwTc_DykO.js"}, "Bvwt3eX": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y4/r/_ZJzG8AEUNt.js"}, "b3lULUy": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yn/r/MPRCzKCWYMK.js"}, "CASxwHv": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yy/r/fJPzSga3lAT.js"}, "U6KTmcQ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/4tESygw-mNK.js"}, "S19GZl5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iUa-4/y5/l/en_GB/Z4vsIynSnaw.js"}, "OQMOFGw": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yr/r/_UObVKnIxFI.js"}, "nGJyh3S": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yu/r/cgCObudlVQ_.js"}, "AuZKygx": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yE/r/FlqtqIcTAB9.js"}, "rM665fY": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y-/r/Y_mD3RporN0.js"}, "cBIPZq+": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yM/r/A7PBItNEigh.js"}, "DxDwZTK": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yz/r/Pt-wU5wVzJ6.js"}, "6DhPJkb": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yN/l/0,cross/Xa2PToznt6A.css"}, "1ta0i3h": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yL/l/0,cross/Z8VoovZ0y6O.css"}, "RaV5hgW": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yC/r/zHi76zjeq7h.js"}, "9Bt5Bod": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y2/r/ESaIyB7fV7q.js"}, "M74u61C": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y7/r/EeZnOFQ_mjt.js"}, "au+2QzJ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yV/r/oAiU5nntggn.js"}, "ww9ML/p": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yT/r/dG0EF9Oc4MR.js"}, "YkSpej0": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iiPB4/yY/l/en_GB/pNMvfcNQA1z.js"}, "IIze1le": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yw/r/BEn6-XLMove.js"}, "eol9mwB": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yB/r/DCh-jtMA7oX.js"}, "Nsj8Xol": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yx/r/zzt08pRbqpb.js"}, "EuKj2tK": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yN/r/C9hV5MkS51U.js"}, "tu1pVhJ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iYv44/yH/l/en_GB/ZlkIcsViIwS.js"}, "kxTZ8ay": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i6bh4/yS/l/en_GB/LSeZul_-mvq.js"}, "aH0zWUi": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yv/r/Suli6Ek8sGB.js"}, "iyq788x": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yl/r/kdYwHH8UxP7.js"}, "qu+Lt13": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yC/r/IDdrlXvzvvH.js"}, "OdKN4cb": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ibF24/yo/l/en_GB/voF7ZKQIg_p.js"}, "xkmdJGO": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yd/r/2mq0ift2zQl.js"}, "l3afPwC": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yU/r/hghX-80dF4m.js"}, "D7bQI2o": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iM2K4/yd/l/en_GB/PpMIOyeuXlL.js"}, "NDdfdQF": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ir0W4/y3/l/en_GB/T1ovI6c2Caf.js"}, "eaBodOx": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iMGv4/y0/l/en_GB/hS9Xov1V9GJ.js"}, "2hF/qKA": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yU/r/Nq-pfQDf6n_.js"}, "Vj0YiwX": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yo/r/7CHgj5_LJ9K.js"}, "JqLPex5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i1l24/y0/l/en_GB/p-Vz_evYJtc.js"}, "+QpNj0b": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yO/r/CQZNmrpf0Gi.js"}, "Vl7c9Ia": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yb/r/dFTAP_8xcB4.js"}, "G6mVVjy": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iFzB4/yQ/l/en_GB/DANw9z81ckT.js"}, "zmABFNd": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yK/r/Kff6ZpVDSjE.js"}, "EczF9r2": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yx/r/Kx3LpJ-T3UQ.js"}, "TcsEvK7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yR/r/OlEeLK_LASD.js"}, "qQ/FMOE": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yY/r/XlCL93Qxjg8.js"}, "NDT8ZcT": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iTKr4/y6/l/en_GB/zEYI7BL-o9L.js"}, "SBqDt8y": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iR624/yi/l/en_GB/qOHHqWiF3jc.js"}, "2v/Vn84": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ya/r/GV2RerHmd_n.js"}, "Ecd0NUA": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iBKZ4/yZ/l/en_GB/W6CRXdlWktp.js"}, "rK/fqgT": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y9/r/PT8TrYDFYEf.js"}, "utpnpN3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iKm94/yI/l/en_GB/ULXL2uVeEtj.js"}, "HJ8G9XW": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y3/r/3xgMTuzBIVA.js"}, "H5hJ6Qz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yY/r/CByNssFxjqf.js"}, "G2Xz7er": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/MUBA0fBpQCs.js"}, "ZFvQeGA": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yl/r/r2mCKqxBiR2.js"}, "oaV8GKr": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iNQY4/yQ/l/en_GB/LR0cTuVgKHu.js"}, "Hp2WimU": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yN/r/Zg0TjVqq-_N.js"}, "aoijp3N": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y_/r/W4mxpbt4qMt.js"}, "HLyjvhV": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yM/r/o9r9xnEWrx5.js"}, "KQ+loWk": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yC/r/BwjU4B_qfpp.js"}, "pu2guZ5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yA/r/5zHP4nRC4ug.js"}, "iZGA44l": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i6UQ4/yw/l/en_GB/zQUNHzGPl_y.js"}, "BwjHiXU": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yo/r/f3NM7YW6Hkw.js"}, "NvOtkBA": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yk/r/vtCazN0_VQ4.js"}, "8znbvuk": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yJ/r/Uqc7ea3U8ae.js"}, "7qtAdPf": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yK/r/TWBJmEFvCqR.js"}, "bQ0v4Y3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yY/r/e8weugAWKq8.js"}, "em9Nh4G": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yp/r/onVxOeBYgZv.js"}, "ILZCDtk": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yi/r/v9dvdlGCWxQ.js"}, "i+11kK5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ijPB4/yU/l/en_GB/SEVr_5TJn-R.js"}, "N8LDyNo": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4itUF4/y9/l/en_GB/fk066X1GXPd.js"}, "mwYaqHf": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iQeE4/yA/l/en_GB/5y7Op6n3wCL.js"}, "xZytz85": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ym/r/2KHiaTj9H2O.js"}, "97nkpix": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yj/r/BhRgSco_Cqp.js"}, "cFNH8a0": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ibw54/y6/l/en_GB/CFNNXXIU8uQ.js"}, "PGlOZx0": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yB/r/trjIyCsTslJ.js"}, "Tiio7T9": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yi/r/Xxh_VzFUgEq.js"}, "wtsx95p": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yo/r/-s0pEql6iN8.js"}, "2GwOy4F": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iDWu4/y4/l/en_GB/swsBSzfsnVP.js"}, "+/pKfsC": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yy/r/JB07BFTd113.js"}, "8ItDKbY": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y4/r/iECnqc3fXgY.js"}, "i7vOr1P": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yP/r/2Bi7stz9Btv.js"}, "Zs4nYW5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yd/r/ZF5cXvvxP_V.js"}, "uODXzad": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yt/r/6xJUFRSIqKX.js"}, "tm5Vteg": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y1/r/TksaVrww973.js"}, "PIrr5al": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4il0z4/yJ/l/en_GB/JVgxEZURwHr.js"}, "bG4jsj6": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yl/r/WIA9W50n4xQ.js"}, "isks176": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y1/r/u7wBiIMoQdc.js"}, "ZVbmy7U": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y7/r/LDHPrOp_4E2.js"}, "QgwPgYx": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ya/r/G8R7_j5F__-.js"}, "MJquEik": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y9/r/L3irZvGXM-n.js"}, "Y4EwF/i": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yr/r/2-LBgXvy4Hi.js"}, "1y7zDUg": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yZ/r/yb6-Nxaf3TM.js"}, "q7yzyu9": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y7/r/C3sSU5xXQJ2.js"}, "ocPpFXj": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/8eh6nIs_G_k.js"}, "+4LqdMl": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4itDX4/yp/l/en_GB/OjOv_NbqpAR.js"}, "gmB/WTO": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yC/r/lUlMETLuSmx.js"}, "oQBvcPb": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iMk-4/y-/l/en_GB/dKRfGH50Fug.js"}, "rwpPGnF": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ya/r/UFnW1bo2IbL.js"}, "icvPYVd": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iHFa4/yZ/l/en_GB/6zzCr-UBcx1.js"}, "BRE+hDI": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yB/r/SF9Nexnk7Va.js"}, "JmFGL59": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yk/r/dqrx69t50Y2.js"}, "ycTIAW1": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ye/r/effq_z8Why7.js"}, "Vy3Bbiy": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yU/r/bhZmow8I-cq.js"}, "ojITMY3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yk/r/zjPKj90CHb5.js"}, "QNXKONW": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y5/r/6ZXNEhKB8sn.js"}, "3g1t0NZ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yo/r/5SotwIXH4D0.js"}, "tVIZV0Z": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yC/r/2_H76rzj6dQ.js"}, "6VGcqRS": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ym/r/CMgXx276Oqo.js"}, "4Ma1iMI": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yl/r/P8XCj8c-7oI.js"}, "Xrd3WGG": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ieVH4/y6/l/en_GB/RXp7JCo54ji.js"}, "DtDUL5M": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yD/r/4bhTnuiKL4K.js"}, "oWsQbS6": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iIWs4/yo/l/en_GB/1M6IRXdJCME.js"}, "masqUdi": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yU/r/W3TzQXgS2K8.js"}, "toJd0rA": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yb/r/Uss22GStMZ-.js"}, "eeFeY+E": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y7/r/Fybr3CTWc1S.js"}, "aM98A9L": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yD/r/FAQj2x7C0Jl.js"}, "cUz2PyF": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yP/r/CiTf5QwWGAF.js"}, "MvDd1/g": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yE/r/7SkUKLzXtw-.js"}, "HnAkhYd": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iXLx4/yX/l/en_GB/U0IpMnZ9x63.js"}, "PScPYIr": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yk/r/tUIkmKVdy2B.js"}, "YTYt1rp": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yG/r/Cm7C-S_AYD5.js"}, "SGfmCy4": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yZ/r/0GMc5IwQi37.js"}, "NWX9MlZ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yr/r/fDrmuBiYPMx.js"}, "DdbKnc9": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iNh04/yv/l/en_GB/xBtsrQBvSwQ.js"}, "+uep2j1": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yY/r/RWwUuM_Cjw5.js"}, "kU+Wjsc": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iSfI4/y8/l/en_GB/eBK4grGZbHC.js"}, "cEZMQvV": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yw/r/GaftDX-w9a8.js"}, "vX5djkY": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y5/r/nwLKMBeVEp_.js"}, "aXdCNxD": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yd/r/rbTvbzX8Qch.js"}, "dXoghUN": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yX/r/kT2SkLF6re_.js"}, "ffjgdoB": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y-/r/mBvtzwsLbq0.js"}, "rag9nX5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yr/r/kum35Y5ikQV.js"}, "vZvSvXW": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yC/r/FrzRYYriUA0.js"}, "loplRt/": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iYQC4/yk/l/en_GB/cRq2Z6srmcL.js"}, "YcahXsH": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iMob4/y4/l/en_GB/gNOCs7cqUgp.js"}, "PI8rDi/": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iyFK4/yS/l/en_GB/OVsStocXNqx.js"}, "xgWX83u": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ye/r/mUwk03tcS-5.js"}, "p7OHbpn": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ixlj4/yb/l/en_GB/H13GqUD4aoJ.js"}, "F9vwxQo": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iF1c4/yc/l/en_GB/lcObEikYl8S.js"}, "H1xSsqu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yB/r/KnChDZtK_6P.js"}, "9ljYqAX": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yz/r/Dl2q4-YVS4-.js"}, "1NyP4fn": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ya/r/xRue2qIfli_.js"}, "hQTZGJ9": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/pRmPBEBalBN.js"}, "aUsV+ZM": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yS/r/28-d-Oh2uzj.js"}, "Uuvjz2E": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ihbf4/yJ/l/en_GB/gAm_Bhuw6M7.js"}, "QRP7M/5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y-/r/ezxjBBiRzRn.js"}, "cYUftsH": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yi/r/RBd7no1616c.js"}, "fspCOAH": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/XGSFhQMvKi1.js"}, "86Oiqp3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ilkt4/yw/l/en_GB/HlnqqNwP17g.js"}, "1ar2nQ0": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y5/r/sSSqRF1TgG8.js"}, "EZk1aEK": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i7NI4/ye/l/en_GB/YPUjfP_uYe8.js"}, "fBY288l": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i7xd4/y_/l/en_GB/k-_kRPP5Yk8.js"}, "lkkDAwT": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4isYE4/yX/l/en_GB/NOqEPjcbxFT.js"}, "sJiZVd7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4itzt4/yg/l/en_GB/fUJxavlQYbX.js"}, "jOqQQE9": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yX/r/KUVEL5HS0Jk.js"}, "oC0IBvL": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yx/r/JinHsDFfjVH.js"}, "d2Q3HEX": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yv/r/Exu3_DlgeBD.js"}, "8yosRK4": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iG0K4/y5/l/en_GB/O-_wlWdARei.js"}, "kArBjcw": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yC/r/KQuAMh-cqek.js"}, "YlsjK3T": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yL/r/NbQ4oS_CrTy.js"}, "JWhXVzk": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yQ/r/rQaehoJ5_Yb.js"}, "vYAJdqN": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yh/r/y5gPSBVdsZg.js"}, "pmqBWiS": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yv/r/lpVpe3LAsAo.js"}, "uG84D/Y": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yB/r/WB7BYhK90tr.js"}, "FZnP6It": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yD/r/bUhT4WI8P_w.js"}, "/JYRIgK": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yX/r/GdB21UhWLBu.js"}, "ghU/efn": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yk/r/HGyr3laXju9.js"}, "t/ZsChu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yK/r/880yFbNCeN8.js"}, "qRZH+bN": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y-/r/6gOl1DgUf7K.js"}, "VlsG0Qa": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iWM84/yT/l/en_GB/Wfdb17Z-Vsx.js"}, "rcI/l57": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yy/r/Y2zymQU0kpt.js"}, "7z3ZuCc": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ic6Z4/ye/l/en_GB/Y86J3suWYqZ.js"}, "T8sz4wU": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i-Np4/y2/l/en_GB/UlzeTMDpb4E.js"}, "HGVWAwO": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y2/r/zUv28bnENcO.js"}, "d60fGUl": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yB/r/HvBW-KYermS.js"}, "Qny7GZl": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/aZr_7KbDJfW.js"}, "RmfsxWX": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yZ/r/gX7NkbihEKh.js"}, "87ouXLV": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yM/r/3PH2_KSTxkM.js"}, "0ZM2JNa": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yU/r/2HVBsyEiQKN.js"}, "nu9Op5R": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yT/r/CFtB5qFIv2r.js"}, "MLQYIaG": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yf/r/RUPWa7kivpa.js"}, "HsC1Xx4": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yU/r/D8dgtb7kaJa.js"}, "8SJxzQh": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y-/r/nBPg_VgpDkM.js"}, "ibNaSu6": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yu/r/QJ5XD_UYc8q.js"}, "WO/2RdL": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yH/r/tQVTi6hkO-N.js"}, "ogYRfIi": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yh/r/-UKN6hxpfDF.js"}, "OHHSBJL": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yE/r/0rQslZH_Kyz.js"}, "G9pj27s": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ym/r/hGTvHFdJC-Z.js"}, "vpbcu9X": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yS/r/FZMDESP7Tzq.js"}, "/VWhfKx": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yZ/r/SIAtl2gVodR.js"}, "LUWQZ8u": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yv/l/0,cross/iKX1jxk0CvN.css", "nonblocking": 1}, "d1lq6jp": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yy/l/0,cross/ZprOglqRvZM.css", "nonblocking": 1}, "q/7xany": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yx/r/q4ffcYsUXm-.js"}, "ga2vRTt": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ihXN4/yX/l/en_GB/gvObA09_LNF.js"}, "KPxcHWu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yU/r/hrI-XM2uWIK.js"}, "/RQop9e": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ys/r/4uSjANr5cWl.js"}, "azfOg0F": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ys/r/bF8sW4OeuP4.js"}, "GxmKLih": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yK/r/SO55yuSGR5n.js"}, "6RoZJk2": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iV7w4/yx/l/en_GB/lLobkuAiBWG.js"}, "51Zu8du": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iHi34/yJ/l/en_GB/kmirZMPs2Rd.js"}, "rqAmTFz": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yo/l/0,cross/kSlacaPq5nh.css"}, "fBPefy0": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ya/r/EwrRsBAZWxr.js"}, "UhlDJwB": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yH/r/P7A53CGJMM_.js"}, "ZD+S2f8": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y9/r/ie38mp0O07P.js"}, "HFlmHy3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yH/r/19XAuV6h24i.js"}, "lvLtOwk": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yF/r/WQwXgVwctUP.js"}, "YJaIoCW": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yE/r/_W_yIiSguvW.js"}, "ew5l1G+": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yb/r/Kn7l8y_nfHZ.js"}, "rSCWjJs": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i_J04/yZ/l/en_GB/Ksy5VmmeqkC.js"}, "tXSoCdA": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yE/r/d5P8zcko41n.js"}, "9GYFIOR": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iui44/yF/l/en_GB/DpvIQs5hnUU.js"}, "rCasuzG": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yA/r/OzWmCcYw0wO.js"}, "HhOLZv6": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y6/r/BVvNfDKL-9Z.js"}, "Pz4N/UZ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yv/r/3DOb7TTxu9p.js"}, "AnILcqd": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yD/r/460rnmHKrS4.js"}, "kSBEAJR": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y9/r/kw5O4N-0Zwh.js"}, "vunQ0c5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y3/r/RP10jAcEtDH.js"}, "wnm0qxx": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yf/r/8uq5k4Bv9pU.js"}, "OotE7mX": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yd/r/XbTK_KNdmkh.js"}, "7cDw5lL": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ihqI4/y2/l/en_GB/CxAWcYJaJM5.js"}, "oD/CqYu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y0/r/hiUawX_IN-h.js"}, "M3f7g0U": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ia6Y4/yb/l/en_GB/erVDhpJKi-b.js"}, "FlX0L67": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yJ/r/SdRffLYCrn6.js"}, "ZQvJHOc": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/hWlvJwjRJQ2.js"}, "V74TdxN": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4irBn4/y9/l/en_GB/SnnMKCb-NNW.js"}, "9o5QL6R": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yJ/r/zivpPs0zyHM.js"}, "dRzsvGe": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4icsJ4/y5/l/en_GB/AqCr_7VCu3X.js"}, "Pcsk72i": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y9/r/bWUeFetnQA-.js"}, "HWNocUu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yr/r/InRxDeNhbns.js"}, "MnVo2aO": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y1/r/75NC_3Qj8Lf.js"}, "0v1O4gw": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i0U54/yA/l/en_GB/aHRGl2jGDdn.js"}, "6kBWdiH": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y_/r/Q2m26S8WTrP.js"}, "8ug66M5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y_/r/5rOoyY5InWb.js"}, "a88Ad18": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iLKN4/yf/l/en_GB/rEh98wqNmAK.js"}, "oEdkrad": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/TSFtofDqwC0.js"}, "v+uDnVu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4icpj4/y8/l/en_GB/jjLW_qfzA-R.js"}, "rDXZndH": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yQ/r/y9-kI188cMj.js"}, "KpxglbU": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yF/r/Qx4GMDk8l6m.js"}, "e7tQB1X": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iRKq4/yF/l/en_GB/0hJ5rLiDeVz.js"}, "slHrMON": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yR/r/q43lUhYWGki.js"}, "ncPSbsy": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i7cx4/yC/l/en_GB/pQJPr0HoOQ9.js"}, "itp/xXY": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yx/r/a0nrxTvsDj_.js"}, "R3FKt/X": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yK/r/M8wHrpcxhXp.js"}, "1XdChB+": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ye/r/VBU3z9VkbBF.js"}, "ItL5+o1": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iQQa4/yd/l/en_GB/KZz_-7A_OLM.js"}, "7UYuNic": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yt/r/xL8xmCyDX3R.js"}, "Cs+cukw": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i2od4/yZ/l/en_GB/jYYeal5wd9l.js"}, "SL3Uz4K": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yr/r/szIYpZxy1pB.js"}, "jKEyzV1": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yR/r/IGyCRvEGcRU.js"}, "NOcJNLa": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yG/r/m2v8Cx5-8bq.js"}, "xMFqckF": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/ezpMhaXaqeW.js"}, "wmmlFbB": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yc/r/qvREDKSB-o8.js"}, "LuHuJ91": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yw/r/QTZ9mUJSz8d.js"}, "ayyrnWQ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yH/r/a7yOIxTnW88.js"}, "k57/HUr": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yT/r/6C6boFblDOx.js"}, "7+4+/Y/": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yL/r/C9yx0Fy9e_d.js"}, "KHq4KhJ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iAbP4/yx/l/en_GB/EK1JSFPgfkz.js"}, "4EHtWLU": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yX/r/YgpEmwig8G4.js"}, "PK+F/MP": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y7/r/1cy7VSrlCIR.js"}, "jgPqmY3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yT/r/GyqfBx4i5eF.js"}, "5VgrE1i": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yj/r/Dwb5y5UMgeZ.js"}, "HUzfpWY": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yb/r/m4AIpbJ1Un9.js"}, "UxdVV57": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iR2r4/yC/l/en_GB/JnBDWued5Qc.js"}, "ggGfIQq": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yq/r/u3fVYbrm2-w.js"}, "H1jy+x/": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yJ/r/R4q1nLDnada.js"}, "hB3HmPI": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yi/r/8B1ju3mwn1k.js"}, "TrwjuC0": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yD/r/DKngkSkcOC6.js"}, "H7eyX/h": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yu/r/RYpZmX47kqd.js"}, "hIkUVdb": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yg/r/mBhi0MzNvFA.js"}, "TvzPlDV": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yD/r/WPC_7nw8tKB.js"}, "BpsYMpu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yF/r/KieBATEQvYD.js"}, "cWiXD6v": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yH/r/hDX577LJthh.js"}, "8LbgiWz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yG/r/QhlAGF0m23K.js"}, "i4CrvYu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yk/r/tl67iAg0POM.js"}, "EzP7bX1": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yE/r/0sj5e9y5eET.js"}, "GR6Cq5p": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i-zp4/yY/l/en_GB/I0GxueUfRZ2.js"}, "jiM4LmN": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yh/r/IHn_jf3LnvT.js"}, "lJv37mu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y1/r/FRhZninGp7i.js"}, "9mSO72G": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y_/r/z_lOapWRGx1.js"}, "Lq3HpVY": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yu/r/EyaDBm4XaTY.js"}, "pUGpOvY": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yk/r/JVHaA_7XfoC.js"}, "qfVcy12": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iytg4/yt/l/en_GB/6cDtWHaDOtz.js"}, "3FqyLS2": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yr/r/EaJk8-M8p7c.js"}, "tYlWRD4": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iMNV4/yC/l/en_GB/wVOxIImB_7x.js"}, "T8qboaB": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yn/r/YMpJ3aVZ7Qk.js"}, "nFsTNjJ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i2cz4/ym/l/en_GB/tAs0jjTA5nl.js"}, "6hSk48V": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y5/r/-A7epT4lUn-.js"}, "BNU0jo9": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/XhMrzxEaoGt.js"}, "I+xSE/e": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4imrl4/yo/l/en_GB/MIdq4hRS_T2.js"}, "7VTYpw7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yc/r/9FEgsdUBlVD.js"}, "LHI+p96": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y6/r/jEUBx-R5BKn.js"}, "SUImyBw": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ivQ74/yo/l/en_GB/4CoVnBPZYmd.js"}, "GmM9mRy": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yT/r/HDH4B5pMci0.js"}, "Nc4MiT5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i06x4/yn/l/en_GB/7AcIpfPyTCN.js"}, "8yF50Ai": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yv/r/qoS5mGtGX1-.js"}, "QcEn6y5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i0KO4/ym/l/en_GB/gSiQ769g6l_.js"}, "rHmh3sl": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iMae4/ye/l/en_GB/FwXuMO94sD3.js"}, "Jn0P8Y+": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yT/l/0,cross/JRUHixShCF1.css"}, "aT8eM7o": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yh/r/CC910Q3H6Ny.js"}, "XA3A8XO": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yR/r/4deS9mkWoaS.js"}, "bFENf+A": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yq/r/AqpEGqI9TwG.js"}, "H3kq1tg": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yv/r/8Af2orYmZig.js"}, "vg+fw9x": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yn/r/xKNkjLy7c50.js"}, "e56YkiO": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y4/r/8XDYR7Ys4cW.js"}, "bIlbDQH": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y_/r/bk6IID1N-qp.js"}, "F7ML0+a": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4izkP4/yT/l/en_GB/I1Vftibnacb.js"}, "JCbeqG9": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yn/r/8GQajNzsvUP.js"}, "Sa5NeBO": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yO/r/ZMvA6-bS8V8.js"}, "tadXoV3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yh/r/IXeaLEmm-mQ.js"}, "AM2M7Pp": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yh/r/Hx5UZDwvsTH.js"}, "g6apr04": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yc/r/dbLlCod85wP.js"}, "YzRDiEz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yp/r/Xe6o3gV8KZ-.js"}, "iLCTNyP": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yy/r/C2fZXh9riPg.js"}, "WktD9Cw": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yf/r/Nf53SX7EE8G.js"}, "/VeHYb4": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yp/r/fA4BypMWPbA.js"}, "f+gJDyQ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yx/r/JZCUi2yKpxn.js"}, "b2dYbyi": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4igHW4/yt/l/en_GB/Zii27V_xIoK.js"}, "G67HnM6": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yw/r/qpIddmhzsGw.js"}, "+dn85S9": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yJ/r/GRYBEcASrKZ.js"}, "/kkOgx3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yO/r/WowNMlNEKwO.js"}, "Ld2p5v3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yR/r/CY9FLYHsLsP.js"}, "4p+JRWR": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i4mA4/y6/l/en_GB/9Yj_LVkvBP0.js"}, "8Gk0DMA": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yM/r/VCk-TsCE4z6.js"}, "ePU6Tbw": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yJ/r/_Zk78rW2KSM.js"}, "+312sWv": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y0/r/vI_c6F3Cohs.js"}, "jBonzwd": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yQ/r/fM6kWCw1Syi.js"}, "+UL8Tpy": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yJ/r/fKjakhWvxuz.js"}, "jFMbfaR": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/BbVxbE589aH.js"}, "nP5XLJr": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yn/r/9il6JC8xJYM.js"}, "1d8Uoy6": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yV/r/tJ6uahYBmqB.js"}, "KENSO9Y": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ye/r/4pqMTK3E0jj.js"}, "oF6Plie": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y1/r/3w6LeUjlX9K.js"}, "yfr8tE+": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yY/r/V3ElApW-W_e.js"}, "LSHcyDl": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yo/r/DmAHhIJYv-o.js"}, "PxyDiSj": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ye/r/Bh8rMEGFRTO.js"}, "TgS/vhe": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yV/r/aSdThnTKtRT.js"}, "qPB2M6L": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yw/r/KAnZEwuCjmr.js"}, "7hdxfqa": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yu/r/0POrcqS8IX1.js"}, "TBYeUEy": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iceb4/yc/l/en_GB/bRNxaufUlG6.js"}, "lxmHHZK": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yD/r/FNbY4cn-u-z.js"}, "k03KUxU": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iESm4/yZ/l/en_GB/f5EeNFqgNIO.js"}, "HF0Twol": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yO/r/7KO3sHaTqjW.js"}, "N57hjl4": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4itxM4/yK/l/en_GB/6RvbFDqauuW.js"}, "gr2FW4/": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yG/r/JVxGx8lbRHY.js"}, "w8X7Q7u": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i06x4/yZ/l/en_GB/2C3K_MWd7MC.js"}, "+ISoPLA": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ijUC4/yW/l/en_GB/8JDRdm4Mx5N.js"}, "ADwB+G0": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yy/r/CLCw7hbbg3Z.js"}, "NH8SsM9": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yJ/r/7tJx0ksY6vh.js"}, "PMZLJ80": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yH/r/HToQRKAru-X.js"}, "m1hOrsu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yM/r/KU9vm6-FHlk.js"}, "su0h8DZ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yJ/r/KdVubwhiNjl.js"}, "0YaIyZb": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y_/r/MJmEbVJgECP.js"}, "HsiO4c5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yK/r/YTvgRdLU4k1.js"}, "gWl5LCD": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ib2X4/yT/l/en_GB/WJkjUkiktNL.js"}, "SpYVXeD": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yG/r/E0ar8bjeUDr.js"}, "PO5eZU3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/MBx74db0uYm.js"}, "96ks/Xo": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/yRFM5V9ptOf.js"}, "+bGK9ad": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yW/r/o-9ymCPLbkT.js"}, "kzhLPe8": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yD/r/eQPdnsJlbi-.js"}, "JoPh5yD": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yy/r/dMn2wHYYgmf.js"}, "pe/RT9B": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yq/r/yY_s6aoTBP7.js"}, "74zNR2g": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yf/r/6Lbywf-pDWH.js"}, "nQbgdUp": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/CfealGJloea.js"}, "psQDGpx": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y3/r/rpn1Pn3-UkW.js"}, "OYjPhNm": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yv/r/I_moSLtYXx-.js"}, "VUeIUkc": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ya/r/2tA086sl87y.js"}, "xp4ImVI": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yr/r/qNPAmzBn5AD.js"}, "UYmI+GK": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iw6p4/yU/l/en_GB/UAFJe1dAvVe.js"}, "gdMQTbM": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yc/r/hH1mQvTplNV.js"}, "unqbfcp": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yz/r/HB8o_k3E3v5.js"}, "b/eTD2J": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yr/r/NPyYl7Wkshs.js"}, "B+5KHZ+": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yO/r/_EXMh_j91IX.js"}, "aINM4Q8": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yA/r/QPfqND9CwdE.js"}, "K47Ckms": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/yWPxBPwPmqY.js"}, "n6tfCUD": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yd/r/Le9muOvFq4_.js"}, "TuYREcN": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yq/r/aUJtETtes3D.js"}, "llwAFtq": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iaNq4/y_/l/en_GB/-KHsbni7NUM.js"}, "uIX25y7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y6/r/gXR3N-u7wzY.js"}, "0+heN/J": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iQbg4/yE/l/en_GB/usZCSQZoxPH.js"}, "N/vRBVJ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ibni4/yL/l/en_GB/HEdD_kkK7bS.js"}, "15itoyT": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yU/r/sPRvUUbFTkK.js"}, "3YRytZr": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yN/r/H8T6OoCa4x0.js"}, "A5W4nZ3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yh/r/h46Vah8NbpM.js"}, "Xu3tDev": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/r-xcr8CosLE.js"}, "FMqtL1o": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yJ/r/t2DwqHghYhJ.js"}, "JgGlnLc": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yt/r/hc4p15t2zaV.js"}, "sCjm+Yb": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yG/r/Ey1xD2n3skw.js"}, "MgKiaJn": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y-/r/lRTI-zIdQ3I.js"}, "02d+gRA": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yE/r/kYIoTlWQcd5.js"}, "HwWnpJj": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yx/r/sHC0cfrPx97.js"}, "Oi4NxdN": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yr/r/IfKMkCiGSlW.js"}, "sgl2pMi": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y-/r/TJVBok7M4Hh.js"}, "bQhI7GY": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yw/r/Ma25Wh1ll9g.js"}, "N+sotdu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yk/r/79ZkWbzb67P.js"}, "FPEeweo": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i3gB4/y2/l/en_GB/YlXcpasNniz.js"}, "0vIfjJM": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yU/r/beBquf6isPH.js"}, "IKFZtXQ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yr/r/UiRIlOfb89M.js"}, "gUATRzI": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yu/r/Ww--bBDFCJf.js"}, "MLjC8UI": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yq/r/LUel1NMUnlB.js"}, "JRZjPe5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yG/r/znBsP1seLcN.js"}, "LsPbQ/L": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4izji4/yR/l/en_GB/FMK7m_ChaWP.js"}, "QCvjme6": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yT/r/m4DymjVtTPp.js"}, "8N0YBCE": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yC/r/QVc1H0BaSrn.js"}, "boeTD6t": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ym/r/_-RPQt-CcjK.js"}, "ZfWmFZy": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yS/r/bAc5QozAKjt.js"}, "K2dQxft": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yd/r/599WEXWYtTQ.js"}, "oRaPilD": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iaKv4/y8/l/en_GB/2Kb237V22zO.js"}, "19JPONl": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yP/r/FlIDFUcjCZV.js"}, "0frBl3D": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4icR34/ya/l/en_GB/jwdq8YtCD60.js"}, "Wtqx6Y7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yQ/r/nkbGXi-VHxU.js"}, "WNwptxP": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iqHw4/yU/l/en_GB/K6ERvGvvjqa.js"}, "gDCAJKu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yg/r/Xjpwde3wM2h.js"}, "E2rEch4": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yg/r/52yTJUOQUd_.js"}, "bRdUIoS": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y6/r/ezij662hEiJ.js"}, "5DRFeKx": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iNHD4/yF/l/en_GB/T-WIRbqxNg8.js"}, "xvBEKM8": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yP/r/xrDZq9ZbptV.js"}, "Ns1vBfY": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yb/r/CWfviRPDpLy.js"}, "Z6VCeUW": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yh/r/S3UdhIO9l_Y.js"}, "pJzF+q6": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yL/r/ddxLMMf7ftk.js"}, "ntCdn6n": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y_/r/JOszhbYzeBb.js"}, "TbGs6/p": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yt/r/o8CjzNQY8vc.js"}, "T9ChB+0": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4injC4/yQ/l/en_GB/Fw63ql6-LeB.js"}, "MduUQdJ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yO/r/B8xplzzZ88m.js"}, "AH4m43u": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yT/r/16u_d9heBr8.js"}, "X+oBYJE": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yA/r/XvNTvqQDVrr.js"}, "UbL4CQm": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yO/r/vQHu_T0gVuv.js"}, "bErntoJ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y7/r/kW5gekYulVA.js"}, "kDKn14A": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iPWH4/yi/l/en_GB/lsxutfwVOFH.js"}, "4zi/AFk": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y5/r/VToweN25AbY.js"}, "5FTrDGG": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yq/r/V4dnRbLqgaM.js"}, "3OGVOyL": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iJ724/yI/l/en_GB/aVcT6oitPYg.js"}, "xcBxTPy": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yv/r/mTRCPeDxPDX.js"}, "3F8z+dY": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iFK24/y7/l/en_GB/ROHDiMuo4EK.js"}, "nookUis": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yS/r/m_HnpqL66Aq.js"}, "1Cu1JIP": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4isHg4/yR/l/en_GB/bixxCr75buN.js"}, "a/Z+BCQ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yd/r/9rWUywtodxp.js"}, "UJFYPMZ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4isId4/yY/l/en_GB/BpAXtRlMN4I.js"}, "s1jhi8t": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yh/r/--xTXXObecz.js"}, "dSR0j0H": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ye/r/n_jTdC_q4rc.js"}, "rIVwBcu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yr/r/5KzPEbBFVda.js"}, "UtQq7+g": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iYCP4/yY/l/en_GB/SYda3EY-1vv.js"}, "5twxztF": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i7XP4/yV/l/en_GB/Z_Yk20sI34T.js"}, "TeK1XKp": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yu/r/w9Xkx2lEC4o.js"}, "700+/6W": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yj/r/D4ER-X2QmfZ.js"}, "IPU4NQd": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yS/r/cNWZoSbuYCm.js"}, "ze83gae": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iXHW4/yy/l/en_GB/wkZ64OPTEv4.js"}, "KeRf35c": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4izc74/yA/l/en_GB/RgkoE3Ztxmo.js"}, "C7D+vEj": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4is5y4/yD/l/en_GB/7zPI_mblkAQ.js"}, "wCrtyOq": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yJ/r/cqkAiX_CosR.js"}, "bXI03Wh": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yc/r/yF58bKEA4fZ.js"}, "WI59aUS": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iaKv4/ya/l/en_GB/icPDm6vRbDh.js"}, "ClDCH53": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ym/r/u9Cx0OR5SlO.js"}, "Mgwp148": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yv/r/ekBPrIK1Mtw.js"}, "KLzg2CD": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iVdO4/yr/l/en_GB/9QuNlGj5IPt.js"}, "HeYtjgz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iTyU4/yE/l/en_GB/T5sPwj3tIxW.js"}, "99pUYEy": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iSxm4/yM/l/en_GB/RfPB57Ed5Rc.js"}, "Kv6q2cC": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yM/r/jF7D9_e26pT.js"}, "J0P76gH": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ivkV4/ye/l/en_GB/K_PgLlE3Sfo.js"}, "A0ACyj7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y5/r/dub81vJUHN_.js"}, "SL4XDv7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yL/r/s-TMsov-fkj.js"}, "h11k3h5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yj/r/qAVxsn41mza.js"}, "QkX67Ft": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yq/r/dGhG9BS5Ww9.js"}, "9x4c2GH": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yM/r/ujZu5jybPUg.js"}, "+iUProi": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yy/r/bNhlnF9KcY-.js"}, "2ibftsI": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y5/r/DGbyeqtaJsj.js"}, "yH7vKwO": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ilcs4/yQ/l/en_GB/VjukbEEK8uE.js"}, "vfknPFm": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yR/r/8ZDl_cO1JTc.js"}, "Tj16T2y": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y7/r/8Rwfbyoivf_.js"}, "4wcAGvi": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yF/r/wooMy9xRLxX.js"}, "6679oPv": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/ikX22gpUqRD.js"}, "VBYg64A": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iBwz4/ye/l/en_GB/e2VFscSut6Y.js"}, "RuQeYF1": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yg/r/u8YogLQFlv1.js"}, "mb2FyEe": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yb/r/SoQ9f3wjQVi.js"}, "65AtEbt": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yR/r/6QZl__qKCRx.js"}, "SziyUR7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iCdF4/y4/l/en_GB/EulBsgn4p4b.js"}, "ugZ5ced": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ikF_4/y1/l/en_GB/DYltBo4W0_c.js"}, "KNaOiUn": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y9/r/nOgo-UdTwCk.js"}, "gY2ZD6c": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yy/r/m8FrE40hylf.js"}, "tqz4pss": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/BMGY2vM0YBf.js"}, "saIt3ym": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yn/r/0wb9kaE0HYj.js"}, "pMC4hWu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y6/r/WNgDAuP8VhM.js"}, "Ok10Exp": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yR/r/SQb2x7Fc2KE.js"}, "mUjCKLX": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yj/r/xCSqe3Kz3y5.js"}, "cngQrUl": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yn/r/KoWGXfBN824.js"}, "IdfGV10": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yL/r/j0ryfRM9fAy.js"}, "jWWddi1": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y4/r/taUCZuRMCoD.js"}, "Mw+GpS/": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/RXRU6Vv5iAd.js"}, "D6XDYIc": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4idLQ4/yI/l/en_GB/XR6Ol8N7hQ5.js"}, "eyIHSOv": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yk/r/4gL3X1zqhqx.js"}, "JI7UIHz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yR/r/z0byB-iHLOw.js"}, "fKBhU/u": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y5/r/PXaZoT4trkR.js"}, "ug1uvNU": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y6/r/8IUmreEoN9F.js"}, "A2vW0y/": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y3/r/LjCQrEiMQhH.js"}, "S+INbLD": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yP/r/kXp8nM7v8ZO.js"}, "PyCcSfn": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4icxe4/yO/l/en_GB/l_489I-Fode.js"}, "iCcGRVy": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y6/r/gpGM1UGFM2s.js"}, "mhD3JYD": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yo/r/Ze1RnSN4bA9.js"}, "llbVdIJ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yY/r/AYtadYz1jnz.js"}, "Catu5KG": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y2/r/3E1PVuPz-lo.js"}, "rgwfod6": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yj/r/wcywe1meQga.js"}, "28Nm4jp": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iHLd4/y1/l/en_GB/HWJRmPwQ5s6.js"}, "scKq6Vs": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yk/r/o51633fGUt1.js"}, "BS9vhvV": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yF/r/gwQ2DaErHpW.js"}, "OBmSBCp": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4igKL4/yH/l/en_GB/H0EKsKvlmTd.js"}, "QFiH7vy": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4idBr4/yh/l/en_GB/LqxKvlmOc6e.js"}, "sYXuZU/": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y9/r/lyPqR5uTGHl.js"}, "PqHe6/3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iNZ44/y3/l/en_GB/5rqqvyDfUkU.js"}, "lqDeKzt": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iG7j4/yz/l/en_GB/DCcObZVTAkw.js"}, "bq0rwvh": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yE/r/4X3Cgh8LorK.js"}, "2pNZSSg": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yA/r/TK0Rwb0ouLD.js"}, "6SVXUI4": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yo/r/XJt5vlLU8D0.js"}, "QbB9hXs": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i0lO4/yn/l/en_GB/21N90CyDxC7.js"}, "h4brfME": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yC/r/ezfUBHn99-Z.js"}, "n6bBGlv": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yL/r/ZtMWRL_qiQX.js"}, "7oGtCMe": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y8/r/IgAANRkm8TT.js"}, "IMdCkYz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yS/r/UnXMj76DgGq.js"}, "2WJ17sH": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ivUY4/yC/l/en_GB/IyHTTWMTtY_.js"}, "PyXKfsc": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ya/r/yYYCR5QTZem.js"}, "Z+ZhA+U": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i6v64/yx/l/en_GB/6NYQ9oe7Lxb.js"}, "PR/QqP4": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yH/r/mcyewTOcZSY.js"}, "Sl2mOF/": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y1/r/BKEEevNdpQB.js"}, "PpZ/prg": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y7/r/zwHym0nHzhr.js"}, "pBaKGZW": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yY/r/8QXnhxgc71k.js"}, "Ws4kX8p": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yK/r/bcsBmywHPgS.js"}, "0mrha7k": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yB/r/jKTy1FE52Iv.js"}, "wwbaauH": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ye/r/KaIGvLqajHG.js"}, "1Vyr9Qr": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yP/r/46weYhgvnrd.js"}, "lHOyYgN": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iUOJ4/ya/l/en_GB/MHjpI5BEHbV.js"}, "10sGnCR": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iegj4/ya/l/en_GB/Sm86N7zgcu0.js"}, "CkFhcbo": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y4/r/SLtJhs3hNRB.js"}, "L0bJ3QZ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iwJu4/yO/l/en_GB/mPcc5d-Pw2r.js"}, "wUzAehd": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y3/r/fWVKFkG2f8l.js"}, "8TrOtV3": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yB/r/mzODiq1tTgW.js"}, "9MjDiQ4": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y6/r/OjmPjGWtwey.js"}, "UC5UTmP": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yG/r/lkbfCYJ2-BK.js"}, "sn8Wapu": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yZ/r/s7vnHTzg7GQ.js"}, "0vn/yFz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/h91AoL6d2yZ.js"}, "/9XLdaK": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yf/r/11hGSV_qGPs.js"}, "FcNell1": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yv/r/UBz2wvwnEaG.js"}, "JWFDcmK": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yL/r/Chx6eDtgK1y.js"}, "2EJL/If": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y7/r/gGyV-d6f_38.js"}, "oubeIYT": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yz/r/9cLeTdyVvYc.js"}, "VBlHWsL": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yv/r/OdznlVvq56I.js"}, "BwdkyfY": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yt/r/CO6D62vUYAx.js"}, "1Gl3Z2Z": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yt/r/DatmfhTsd7I.js"}, "kVC0/wZ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yl/r/OGTz7FGDwFt.js"}, "DDSlFiM": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ys/r/hSr0D_xjH7r.js"}, "zopYFkM": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yD/r/7KF7fKkvZDR.js"}, "KViJ+xR": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yt/r/J0aKfgq-BZg.js"}, "Y7KrVSA": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yx/r/WywvgSdrv-d.js"}, "hp7MII7": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yi/r/9foXAxU_g8h.js"}, "kIlybN8": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yX/r/H4Mtu4C-ztC.js"}, "Z137xwt": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/c3n5e7eM8IE.js"}, "5MdZ3xo": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yZ/r/hvlpWpsuUbO.js"}, "khW4oBp": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yn/r/dUbFYjUPHbA.js"}, "5RjwtZm": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yx/r/CGR0go3nbsq.js"}, "82enPUW": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yq/r/4JUx_DYKHl9.js"}, "4zEfisS": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y0/r/2_KZxpsynIc.js"}, "39wkuVA": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/uJoZa4qAFVK.js"}, "IIBk8nD": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yx/r/nVf5fXcpyRX.js"}, "Y/5CFWr": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y3/r/A5hfJDLmNq-.js"}, "wlYtfRY": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y1/r/8PPm-azzAGp.js"}, "puF/Jv0": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iGw04/yO/l/en_GB/vtxkilSiDS9.js"}, "8EKiu92": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yX/r/siiB6Kai909.js"}, "IxmgSpq": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yC/r/l29zXBe523x.js"}, "CGPhg4D": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y0/r/eNAP-muomVl.js"}, "LsiHKqb": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yC/r/0JmF_EsNS0Y.js"}, "lnfihh+": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yo/r/qHASLCP4DDn.js"}, "9xukryL": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yA/r/Qtv_UYJ-t3x.js"}, "jTvsNNJ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yM/r/B1fUcBoUH6E.js"}, "XRVr06B": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y-/r/vIIHeeJXFaH.js"}, "o3UHCOB": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iuJ-4/yf/l/en_GB/EW8BdaYAefe.js"}, "8O+i0Uv": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yc/r/avb4m8UAQdT.js"}, "sGaAkoX": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ij_54/yM/l/en_GB/D1qbiur37XO.js"}, "8uRk9jl": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yp/r/GpS1XhmYBOI.js"}, "Q7wos0g": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4icyc4/yf/l/en_GB/JMcqSx_CJyz.js"}, "3Z8XRPr": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yO/r/B1oQ2cfZzBB.js"}, "QxTlvuf": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yF/r/bUeCvner7U0.js"}, "yOm2EYG": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/ye/r/DEJhe1tUW3c.js"}, "3JwW/sv": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4ibt-4/yP/l/en_GB/ZAuCiymvXCA.js"}, "/0RruWF": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yY/r/sbBA1bvOw-M.js"}, "PNVU5rx": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yv/r/TX3-nVL-bka.js"}, "02nrRf5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y_/r/cNq4DAI5ve_.js"}, "PSodRI+": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yW/r/0xGrZ8rAPTN.js"}, "QaNXVuO": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4id8H4/yW/l/en_GB/PmDdcdWyL6g.js"}, "yx6GLga": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yz/r/wAmJTQtsqlQ.js"}, "5fRW/ex": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yf/r/Kb0OdvsLRv8.js"}, "RdpCget": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iF8A4/yt/l/en_GB/ZVQ654hwURy.js"}, "A+OlLU8": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yT/r/JoUXsMyAn0q.js"}, "Ausgm2K": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y9/r/U4aV8UGwHY6.js"}, "+1z1MmN": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i1l14/yk/l/en_GB/rKM5jY0tFP0.js"}, "MXfmqY5": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4irVd4/yA/l/en_GB/FkUrtyZoqQG.js"}, "HzbNPua": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yN/r/3HnhCmVR0xL.js"}, "CNyGFUs": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y9/r/NfxsgaSDYSh.js"}, "Z47tVyT": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yG/r/QQb05LIx_ib.js"}, "+Vxr4+Q": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y1/r/GGAC47jMZAr.js"}, "dQvu7CT": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yf/r/bdtwc95-1JP.js"}, "V/UX1Wg": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y5/r/DhZzbjpwj1g.js"}, "/r9v1Kb": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yX/r/HDd97o81FlE.js"}, "1yqwozP": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yK/r/wWCsqErxByh.js"}, "M+waYRw": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yl/r/13lUgglkwxN.js"}, "WMm0L8D": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yR/r/mNUWnEYSV7l.js"}, "Nm4S7Tg": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yb/r/eD1236o-4B8.js"}, "mFkoxEV": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yT/r/MuVJ3kziB_s.js"}, "E9ERssr": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yN/r/viHWjbul8fA.js"}, "l4PzlRm": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yB/r/F9Fcsmriq--.js"}, "3UpiF8K": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yM/r/svEOVgK4JiM.js"}, "88Pbcfe": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y3/r/OMPjVMM0yPB.js"}, "r4Fh30Y": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y0/r/ImfHRN5NRgN.js"}, "cumX1+0": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y-/r/78-1k23KuSd.js"}, "aztdKki": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iVdO4/yZ/l/en_GB/6lPKvGSHdGO.js"}, "1xo+hDv": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yT/r/o38ZDX4YWlI.js"}, "691Bs6K": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yp/r/8fBvHi2hFnL.js"}, "0fX8tIP": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i07-4/yZ/l/en_GB/Dvx7OLrar2b.js"}, "LepQ8Dg": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y2/r/3l0ZTbVjAu8.js"}, "VAKtv0Q": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yo/r/EKM2QZDKj4c.js"}, "am21hQJ": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yL/r/qxp3eQXUdU1.js"}, "wO4Dw2o": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/y-/r/-Ks91GssYmE.js"}, "dLciGzt": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yu/r/Y8LHPu0kKVH.js"}, "9NiATAn": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yv/r/yRuFCzueB7p.js"}, "DrMjGyn": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/-mS5Q8Tv_IW.js"}, "dC77nhW": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yZ/l/0,cross/sXdu-exQzSm.css"}, "gQMM0GX": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yU/l/0,cross/E8lMTtVWxK-.css"}, "MeQok+1": {"type": "css", "src": "https://static.xx.fbcdn.net/rsrc.php/v5/yK/l/0,cross/_wMxSWSwTId.css"}, "1NIJ61Z": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i5Z04/yD/l/en_GB/Optc1VzhoRV.js"}, "ZoNh5oc": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iKw34/yY/l/en_GB/SMwaxoOTgzg.js"}, "rzjumBy": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iPol4/yk/l/en_GB/WEmkhTnYMWJ.js"}, "98Y3qDH": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iWtR4/yz/l/en_GB/bvb2bj7iH2a.js"}, "Md/Ftky": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i4724/y4/l/en_GB/HxRRFhZNoH0.js"}, "QjUdE2H": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yN/r/TBRZj8GhpWc.js"}, "sjzhGIG": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yr/r/hiQTkRUJBH5.js"}, "I+GHswV": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yS/r/ui2DkP-wt_7.js"}, "3OQxuIz": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yc/r/SuuS8xeVI9Y.js"}, "x8yY7UR": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4izNk4/ya/l/en_GB/Brh9CXryVda.js"}, "R6tXgFd": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4i_N74/yj/l/en_GB/WT9jEkVMn3P.js"}, "bv1jtSc": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4iAY94/y_/l/en_GB/VK8Lm3ODroK.js"}, "dfIe6wS": {"type": "js", "src": "https://static.xx.fbcdn.net/rsrc.php/v4/yS/r/ajgepxRLxbx.js"}, "cl/gDMu": {"type": "js", "src": "data:application/x-javascript; charset=utf-8;base64,Oy8qRkJfUEtHX0RFTElNKi8KCl9fZCgiQ0FBV2ViQ2xpZW50TG9nZ2luZ0V2ZW50U291cmNlIixbIiRJbnRlcm5hbEVudW0iXSwoZnVuY3Rpb24oYSxiLGMsZCxlLGYpeyJ1c2Ugc3RyaWN0IjthPWIoIiRJbnRlcm5hbEVudW0iKSh7Q0FBOiJjYWEiLENPUk9OQVZJUlVTX0hVQjoiY29yb25hdmlydXNfaHViIixESVNDT1ZFUllfSFVCOiJkaXNjb3ZlcnlfaHViIixFVkVOVFM6ImV2ZW50cyIsRVhQTE9SRToiZXhwbG9yZSIsRVhURVJOQUxfVVJMOiJleHRlcm5hbF91cmwiLEZFRUQ6ImZlZWQiLEdST1VQUzoiZ3JvdXBzIixHQU1JTkc6ImdhbWluZyIsSEFTSFRBRzoiaGFzaHRhZyIsSFRUUF9FUlJPUjoiaHR0cF9lcnJvciIsSU5TVEFOVF9HQU1FU19IVUI6Imluc3RhbnRfZ2FtZXNfaHViIixKT0JTOiJqb2JzIixMT0NBVElPTjoibG9jYXRpb24iLExPR0lOOiJsb2dpbiIsTUlOSV9TSE9QOiJtaW5pX3Nob3AiLE1BUktFVFBMQUNFOiJtYXJrZXRwbGFjZSIsTUVOVEFMX0hFQUxUSF9IVUI6Im1lbnRhbF9oZWFsdGhfaHViIixQQUdFOiJwYWdlIixQTEFDRVM6InBsYWNlcyIsUE9TVDoicG9zdCIsTE9HTzoibG9nbyIsUFJPRklMRToicHJvZmlsZSIsUkVFTFM6InJlZWxzIixTRU86InNlbyIsU0hPUFM6InNob3BzIixTVVBQT1JUX1BPUlRBTFM6InN1cHBvcnRfcG9ydGFscyIsVUZJOiJ1ZmkiLFVOS05PV046InVua25vd24iLFZJREVPOiJ2aWRlbyIsV0FUQ0g6IndhdGNoIixCVVNJTkVTUzoiYnVzaW5lc3MiLFNUT1JZOiJzdG9yeSJ9KTtmLkNBQVdlYkNsaWVudExvZ2dpbmdEaWFsb2dTb3VyY2U9YX0pLDY2KTsKLy8jIHNvdXJjZVVSTD1odHRwczovL3N0YXRpYy54eC5mYmNkbi5uZXQvcnNyYy5waHAvdjQveUwvci9NRmRCUllRSGFGay5qcwo=", "d": 1, "nc": 0, "nonce": "MQDPJX8z"}, "BSUsW4j": {"type": "js", "src": "data:application/x-javascript; charset=utf-8;base64,Oy8qRkJfUEtHX0RFTElNKi8KCl9fZCgidXNlQ0FBTG9nZ2VkT3V0RGlhbG9nV3JhcHBlciIsWyJjcjo1MzciXSwoZnVuY3Rpb24oYSxiLGMsZCxlLGYsZyl7InVzZSBzdHJpY3QiO2Z1bmN0aW9uIGEoYSl7cmV0dXJuIGIoImNyOjUzNyIpKGEpfWdbImRlZmF1bHQiXT1hfSksOTgpOwovLyMgc291cmNlVVJMPWh0dHBzOi8vc3RhdGljLnh4LmZiY2RuLm5ldC9yc3JjLnBocC92NC95Ty9yLzd1LUxOblhHR3pULmpzCg==", "d": 1, "nc": 0, "nonce": "MQDPJX8z"}, "TcDdWQV": {"type": "js", "src": "data:application/x-javascript; charset=utf-8;base64,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", "d": 1, "nc": 0, "nonce": "MQDPJX8z"}}, "compMap": {"CometFeedStoryOptimisticMediaAttachmentSection.react": {"r": ["WzW470F", "Sr6Aahz", "dEY7RHQ", "+UR2TQl", "UneIqIg", "DFsCJbL", "fmlBfb1", "F3OAUNE", "6a0VWiV", "kSXx4CL", "S3gsgXq", "3bxzL8e"], "rds": {"m": ["CometFeedStoryOptimisticProgressBar.react", "ComposerCometMediaAttachmentCollage.react", "ContextualConfig", "BladeRunnerClient", "CometToast.react", "bumpVultureJSHash", "FbtLogging", "DGWRequestStreamClient", "ODS", "IntlQtEventFalcoEvent", "cometComposerQPLLogger", "ConsumptionFocusPointVpvFalcoEvent", "CometSuspenseFalcoEvent", "FDSTooltipDeferredImpl.react", "MqttLongPollingRunner", "Animation", "PageTransitions"], "r": ["iTq47MB", "lAY3K2N", "zJl4N4d", "m3EFigt", "7lO0k+R", "lmV4NqA", "dhV7BOC", "vTyhxMS", "fQlL/9u", "rpZd0S1", "5vmkPLG", "P4YRYHe", "e3t7ot3", "w6R9/0W", "/c8g/m5", "HY7qeqU", "sw9OsZD", "Dy29lk1", "Z48a0we", "Iak57b2", "dWqb64m", "CPtfj1t", "72T5i3p", "ZA3YWhT", "BRJlUbI", "yk7F21/", "o6YiIz7", "ewD+n24", "WL0kL9K", "180F4gD", "xIwLeog", "57AAsPx", "tjrJbTN", "sd317eV", "14NLUiM", "g4pE26R", "1W5pzvT", "OX3rQWx", "4M6DKEv", "UY5v54z", "ayU3D2R", "gLAO/oD", "1UJ2zTs", "1QKkLEP", "sGWXkfX", "/atJEDq", "XMP8fLK", "SPrY11N", "DEpFhF6", "NpoviDS", "npBeFQ+", "kQeX049", "0QXB4CG", "+qnDr4D", "8ELCBwH", "PUI4833", "YgCHL7R", "b0aAln3", "O9+1txH", "pzNXisi", "7iQLlvN", "UqAQ+gE", "EQ+D3D1", "ffSCdTU", "+h8O0IF", "NyizVr9", "IMHff1e", "5ZcHlAi", "qxd9Mr2", "oesShPU", "7WTlwQe", "Z+xFe+J", "c1LU2Ag", "mb53ol3", "XEVo1TC", "GBDhdTg", "16xXSzc", "A7UBH6O", "WwozWWD", "D2KTWwH", "lmr4KaY", "Hwr1+zT", "e3+9Dwu", "l9zbJTz", "ITUTFV8", "hWhBSh/", "J8Yb073", "8zIXEiN", "ID4Gev5", "bJ4IPi7", "kAEXZ2t", "3Mh28ej", "B5RL2E6", "+SRG5gz", "qPcyJPO", "M1MW+Si", "siBRbiX", "XTuHwRI", "OxEZzdV", "eejXjh7", "We0Y2Xt", "0xq3hyW", "CMtpSOn", "fiFBisF", "PsaRjY2", "HgdKfeq", "i/nQFci", "gRod0+S", "oRWvHq5", "CzuOsFB", "ATXcRzk", "wpWTO9l", "iIXSARe", "opltOAS", "WTATY6l", "Y0wky9V", "9UUBvip", "4TpiAWy", "0cNVXD5", "LPvEl4J", "r0yfBAv", "Pwssuq+", "dIVoPFL", "9OKm3xD", "xAecuAA", "OoD1C8w", "uPJCx6c", "c81IIcE", "invUTkA", "AD4aRIi", "vZ5yXE+", "S1ZCgTh", "abQMyJn", "ZeahUe3", "bJqu9gO", "TsZtkcX", "CZNyp6s", "SW/Pwbx", "Hsjl2uk", "TPiPhxZ", "/PCpjt7", "yPJRMPV", "bqek3DH", "ePOrzDE", "gZqXm1L", "mVu9uoT", "XAsVRCe", "lg+gEkr", "BqErpsc", "0vtBQlg", "iOD9YzB", "DfdTYKR", "A3LbXJ4", "Wka4woV", "OCGi48V", "VbFxeir", "1jjlZqt", "6Vx6j82", "QeWWSDP", "wBUs0s5", "S+XsLoT", "83nXCxW", "A1wWm2g", "j9ZV4iT", "lMB51eB", "3u6SGwj", "bbLCiUL", "G+uFvTN", "GW5//HY", "z/N7nw+", "Nrc2fWn", "B4wSUBY", "igSbXP4", "HOs3TiS", "d3Fxe/y", "6iU1NsB", "6ro5LAu", "SLiUPR7", "YLJ6WbM", "yUqVmZ2", "vhI7dSU", "0F/X7qA", "rJmmbBH", "wAmzd0a", "D6p9uYq"]}, "be": 1}, "CometFeedStoryMenu.react": {"r": ["oPVWR39", "88qrwWE", "zJl4N4d", "iq6n5cY", "7lO0k+R", "WzW470F", "fQlL/9u", "Sr6Aahz", "eejXjh7", "SWVFARg", "rHjQ135", "dEY7RHQ", "htFX3lV", "IkSdoO7", "CPtfj1t", "0OiaLuP", "xIwLeog", "LujvAKc", "+UR2TQl", "UneIqIg", "HtrS4yL", "mVu9uoT", "JI94oxz", "kQeX049", "BqErpsc", "fmlBfb1", "F3OAUNE", "b0aAln3", "pkj/9PY", "6a0VWiV", "cJoQBHY", "S3gsgXq", "3bxzL8e", "igSbXP4"], "rdfds": {"m": ["FDSTooltipDeferredImpl.react"]}, "rds": {"m": ["FDSAlertDialogImpl.react", "FeedChevronFalcoEvent", "CometFeedStoryMenuActionFalcoEvent", "FbtLogging", "CometSuspenseFalcoEvent", "IntlQtEventFalcoEvent"], "r": ["OLHcH4M", "b0t9eMa", "w6R9/0W"]}, "be": 1}, "VultureJSSampleRatesLoader": {"r": ["jOON9K3"], "be": 1}, "FDSTooltip.react": {"r": ["zJl4N4d", "WzW470F", "fQlL/9u", "dEY7RHQ", "xIwLeog", "+UR2TQl", "kQeX049", "fmlBfb1", "6a0VWiV", "3bxzL8e", "BqErpsc", "igSbXP4"], "rdfds": {"m": ["FDSTooltipDeferredImpl.react"], "r": ["Sr6Aahz", "UneIqIg", "S3gsgXq"]}, "rds": {"m": ["CometSuspenseFalcoEvent", "FbtLogging", "IntlQtEventFalcoEvent"], "r": ["w6R9/0W"]}, "be": 1}, "CometProfileVerificationBadgePopover.react": {"r": ["c1LU2Ag", "p5o4jN8", "WzW470F", "fQlL/9u", "Sr6Aahz", "eDvbndE", "dEY7RHQ", "xIwLeog", "+UR2TQl", "UneIqIg", "JtvHoGB", "kQeX049", "BqErpsc", "fmlBfb1", "F3OAUNE", "b0aAln3", "6a0VWiV", "S3gsgXq", "3bxzL8e", "igSbXP4"], "rds": {"m": ["FbtLogging", "IntlQtEventFalcoEvent", "CometSuspenseFalcoEvent"], "r": ["w6R9/0W"]}, "be": 1}, "CometStoryAggregatedUsersTitleDialog.react": {"r": ["BWxeAad", "UHS/25B", "5ZcHlAi", "8jW6Z3P", "AM4VhF0", "c1LU2Ag", "zJl4N4d", "lr6ew3o", "7lO0k+R", "aLfc+ws", "dhV7BOC", "WzW470F", "fQlL/9u", "JI/ewOb", "drkow/K", "yUqVmZ2", "fqo+KiO", "NrtiKph", "EqgZAyz", "04RdAeA", "Sr6Aahz", "eejXjh7", "dEY7RHQ", "8WrV1qw", "tuQh/4O", "fzG10qZ", "+zxVnNA", "FUpSXjV", "UaqI1yF", "J223IwG", "sW0y7ab", "tRo7x53", "PLFVLtw", "xIwLeog", "HLK1Yuz", "LHUE5Ub", "r0yfBAv", "hvHkdIC", "OoD1C8w", "ipSndkH", "+UR2TQl", "UneIqIg", "mOYAbny", "OpIFwqF", "r3BqJsA", "dU+KFGJ", "kQeX049", "BqErpsc", "+qnDr4D", "fmlBfb1", "F3OAUNE", "b0aAln3", "Jjqfj8u", "aOCC6m8", "6a0VWiV", "Bj9SjIs", "S3gsgXq", "3bxzL8e", "B4wSUBY", "Q55CU+C", "XMP8fLK", "igSbXP4"], "rdfds": {"m": ["FDSTooltipDeferredImpl.react", "GetLsDatabase"], "r": ["Vk5fAdB", "AM+Jr2d"]}, "rds": {"m": ["MWChatTabSharedQPLAnnotations", "MWChatOpenSecureTabForUserCallback", "MWLSPreloadThreadForContact", "ContextualConfig", "BladeRunnerClient", "MAWVerifyThreadCutover", "CometToast.react", "MWChatLogOpenChatTab", "FriendingCometFriendRequestCancelMutation", "FriendingCometFriendRequestConfirmMutation", "FriendingCometFriendRequestDeleteMutation", "FriendingCometFriendRequestSendMutation", "MWChatTabOpenThreadCapabilitiesLoggingGated", "CometRelayEF", "FbtLogging", "DGWRequestStreamClient", "CometSuspenseFalcoEvent", "IntlQtEventFalcoEvent", "MAWThreadCutover", "MqttLongPollingRunner", "CometExceptionDialog.react", "hasDefaultThreadOneToOneCapability", "MAWMainThreadLogger", "MAWWebWorkerSingleton", "CometRelayEnvironment", "EncryptedBackupUploadFailureFalcoEvent", "EncryptedBackupUploadSuccessFalcoEvent", "sendToSentQPLLogger", "LSVoprfWasm", "MAWBridgeObserver", "EBIsEbEnabledSubscriber", "EBWriteMinosMailboxKeysToCache", "EBMinosVerifySingleEpoch", "PersistedQueue"], "r": ["lD27Xzj", "mRJ4GEO", "stdBzsD", "SLiUPR7", "YLJ6WbM", "w6R9/0W", "Dw6m1KW", "Dy29lk1", "W2q32qT", "grCpL4r", "TEBB51l", "3MiTeE0", "yzm5eQH"]}, "be": 1}, "GroupsCometGroupRuleEntityDialog.react": {"r": ["BWxeAad", "8jW6Z3P", "IpbaOAi", "c1LU2Ag", "T1ub4PD", "zJl4N4d", "7lO0k+R", "EgFdLES", "WzW470F", "fQlL/9u", "V/2V00p", "3e1KAxr", "GPSC/0w", "04RdAeA", "Sr6Aahz", "wrc5x5+", "z8CauSQ", "dEY7RHQ", "o4X9gYT", "8WrV1qw", "E7T5in+", "w8fOUMC", "+zxVnNA", "hthh40l", "tRo7x53", "xIwLeog", "HLK1Yuz", "OoD1C8w", "U853Zz1", "+UR2TQl", "vZ5yXE+", "UneIqIg", "9YQ3krq", "kQeX049", "BqErpsc", "XSlreDW", "+qnDr4D", "fmlBfb1", "F3OAUNE", "bj5dUVI", "b0aAln3", "EbAqmk8", "aOCC6m8", "6a0VWiV", "S3gsgXq", "3bxzL8e", "B4wSUBY", "igSbXP4"], "rdfds": {"m": ["FDSTooltipDeferredImpl.react"]}, "rds": {"m": ["ContextualConfig", "BladeRunnerClient", "CometRelayEF", "FbtLogging", "GroupsViewedRulesFalcoEvent", "DGWRequestStreamClient", "CometSuspenseFalcoEvent", "IntlQtEventFalcoEvent", "MqttLongPollingRunner", "CometExceptionDialog.react"], "r": ["SLiUPR7", "YLJ6WbM", "yUqVmZ2", "w6R9/0W"]}, "be": 1}, "CometUFIConversationGuideContainer.react": {"r": ["6ihXblm", "WzW470F", "fQlL/9u", "3e1KAxr", "Sr6Aahz", "dEY7RHQ", "xIwLeog", "+UR2TQl", "UneIqIg", "j3JrM2X", "XMP8fLK", "kQeX049", "fmlBfb1", "F3OAUNE", "6a0VWiV", "S3gsgXq", "3bxzL8e"], "rdfds": {"m": ["CometUFIThreadedComponentDecorator.react"]}, "rds": {"m": ["CometSuspenseFalcoEvent"]}, "be": 1}, "FBGComponentRootImpl.react": {"r": ["hm/ESgP", "j5IOl0D", "+6BpX6j", "VVDgRKK", "x3i+GY/", "3V2Xot9", "y8Z1UMs", "iAW5jrp", "b3XSUfw", "lhOq/wP", "BWxeAad", "QKr50F5", "3WIA9mq", "Mzh7qY1", "k5Eukrh", "b4oleUw", "7IsiFCm", "1a6uHIy", "DbZbqd1", "wEZMRUe", "uEwd8v3", "H6EFwGg", "f0rk7UV", "8jW6Z3P", "T0TuY8T", "BlO2f94", "aiFodw4", "3b7F9Fu", "MSn/ro1", "c1LU2Ag", "w2tLMLh", "zJl4N4d", "v89pUz/", "B5UE81g", "tuF4Wpw", "vdWCh0h", "gpyjHy7", "crXs0Q4", "fjtgrsO", "Rk2KMgE", "lr6ew3o", "AdNCKua", "E1UtRr5", "7lO0k+R", "91drT1d", "jjcqkJ7", "+thM/Xl", "ToGQR9n", "9bdSDSi", "w7SNbDz", "eXGLDE8", "AQtFf3d", "+jcKpiI", "dhV7BOC", "9EWnid5", "FyxLQT5", "TUoM+2I", "WzW470F", "MmrbWXs", "rlZI7fU", "wyLpwS2", "J04JOWm", "zpd+lZp", "9J16fdf", "SgX7DpB", "wJJKbNe", "fQlL/9u", "uhOTCO2", "F6N4Tl0", "ZWs49iU", "5rgV2S5", "tUwh8B3", "oeDbDS2", "czC3MSn", "rpZd0S1", "ad9dOy7", "3e1KAxr", "<PERSON><PERSON><PERSON><PERSON>", "hknTMg3", "BzzM/0G", "pKc3SfL", "teoK0BU", "nXvbb9G", "IiCdgPi", "XU7mIlf", "bscwShE", "NGKxIVe", "52gvWl+", "04RdAeA", "Sr6Aahz", "KbhcDqz", "cTG7mXF", "Lo6LeaT", "YP/NSCj", "Qf/pxyz", "ou8vrnV", "fJEdBdk", "kChrIuq", "kW3qAGC", "dbTcVEe", "3SQTX1Z", "Ah82WxJ", "mMrwbxd", "0Y0KFas", "/ABZdos", "FtI3fMk", "UmtttOw", "sZy8rgw", "MV8u0Us", "gXI9hSg", "cn+oFmt", "WRi+XjB", "j92QuVO", "KaeAqwN", "Okt9G7P", "beAhtR3", "D0YY98H", "F1kHwNx", "8LsYrQu", "aOri+ZE", "uvPpGOx", "wnQtOGx", "a<PERSON><PERSON><PERSON><PERSON>", "7aa1Fdl", "3o470sW", "Jkg1gUa", "ymyJvvz", "0bJo5d7", "JBWWYvz", "8DeZfan", "T/exXR2", "4l/kqi1", "crlHctM", "a+P5NjD", "dEY7RHQ", "irTQXQJ", "8WrV1qw", "eQbdGRF", "OvavXX8", "H1pwYZ5", "Isuitf7", "+zxVnNA", "EHj7FXT", "ZsccH25", "uuVJiAO", "dzcb3sK", "gKlND5D", "Q55CU+C", "ZeYteZM", "a1zAOjG", "HJEX75Z", "pPCIXUq", "G4o+GSn", "1ncthyc", "w/oNOc7", "wpWTO9l", "2Bh8vWz", "P7T+uth", "6FTJTfz", "yZTdcQl", "pZG73rv", "cXn2xZ2", "LWimt5f", "FlCXYyu", "J0/PDGU", "o6YiIz7", "EK4A83Z", "You46xR", "ynimsRk", "tRo7x53", "mmPABcg", "NY64HKi", "tzvC6uI", "HwjNP73", "Wfb7Uxg", "Jr982T0", "xIwLeog", "HLK1Yuz", "CbIAYxX", "Al+X9aL", "Y3JOSsz", "/bxf4Bs", "wpOING9", "XJzdbGM", "1LX32Cd", "TdsFOZs", "U2LU8tl", "8g1v/5N", "4225gpN", "OSYB+Sw", "Rr/uhbz", "Xti7cDw", "b5d0Exw", "OoD1C8w", "I9lIRpJ", "0nLWnMp", "HjuKI8Y", "+UR2TQl", "rucx1xA", "NPTupMd", "HSJ2m+o", "ZiDtKCK", "C9n16BW", "UneIqIg", "H/vFQCj", "EU14oMb", "yHaO1MS", "mOYAbny", "abQMyJn", "t/9M/qj", "2QvhSD9", "fy+TE/Z", "jnmmy2C", "n+VYnE7", "hz8/3dw", "ThFx62w", "lDAQ46j", "MJkWs/f", "llFC02g", "07iuC/n", "0+ePjAs", "pLG1x/s", "BHuOWHb", "ej4cOKg", "Cbd/faM", "dB+yA7N", "HMu7jnh", "JSIuWWq", "4NsC1nN", "Z/hfX+e", "XMP8fLK", "Oa2MvhD", "sdwuU+q", "1mB+7R9", "CLT3HW+", "QwATVC8", "HK71Xlq", "ak7ajHN", "w5VDOKj", "flaGBH0", "kQeX049", "Bo2moEN", "BqErpsc", "eIyodOJ", "ogtmnDZ", "rzZCc5i", "rvCjzhQ", "+qnDr4D", "2q3LQ0V", "/pXVZGK", "PmgpMnw", "OIFp0O5", "gKLxRZF", "Vo5JV7+", "r5knPo9", "sBkVxJb", "fmlBfb1", "mP27mRt", "+pq9h4P", "pk/6RTI", "rUOdupL", "x3dkzIR", "UI4blME", "HfF/1+h", "UYx2o/B", "CIIih5i", "Xcsq+ML", "wSUYGA4", "pm+eZOd", "5kcPWn6", "F3OAUNE", "pu90sT5", "JRALkqh", "Pj5WtcB", "3bUPF2u", "b0aAln3", "tWb0de7", "aOCC6m8", "Qqt6z0n", "YEUqtaP", "yPjOCCT", "TpRJy9k", "bbLCiUL", "MBYHbGD", "O26H5ty", "6a0VWiV", "X1Wm31c", "+YBYEis", "cJoQBHY", "5XRhPxa", "S3gsgXq", "3bxzL8e", "B4wSUBY", "KtatUH1", "uK1//Sc", "aPGHTvj", "rHg1jVv", "r0yfBAv", "igSbXP4", "/TOwoYy", "EgFdLES", "AEgCBxe", "rHjQ135", "Zb52TyN", "l43YXO1", "6XOaqlh", "wSA190q", "VbFxeir", "80mLdXp", "f04weBo", "jy2K3/U", "qmdIyIg", "D6p9uYq", "CljYTWJ", "+3NZ9s9", "75qNOzp", "wX3bFTF"], "rdfds": {"m": ["FDSTooltipDeferredImpl.react"]}, "rds": {"m": ["ContextualConfig", "BladeRunnerClient", "CometRelayEF", "bumpVultureJSHash", "FbtLogging", "DGWRequestStreamClient", "CometSuspenseFalcoEvent", "ODS", "IntlQtEventFalcoEvent", "MqttLongPollingRunner", "CometExceptionDialog.react"], "r": ["SLiUPR7", "YLJ6WbM", "yUqVmZ2", "w6R9/0W"]}, "be": 1}, "CometProfileSwitcherDialogEntrypointWrapper.react": {"r": ["hIp9SUj", "fGCsB99", "WzW470F", "3e1KAxr", "Sr6Aahz", "dEY7RHQ", "+UR2TQl", "UneIqIg", "96dXq+T", "fmlBfb1", "F3OAUNE", "6a0VWiV", "S3gsgXq", "3bxzL8e", "fQlL/9u", "xIwLeog", "kQeX049", "igSbXP4"], "rds": {"m": ["FbtLogging", "IntlQtEventFalcoEvent"], "r": ["w6R9/0W"]}, "be": 1}, "CometUFICodedErrorDialog.react": {"r": ["WzW470F", "SgX7DpB", "fQlL/9u", "Sr6Aahz", "dEY7RHQ", "xIwLeog", "+UR2TQl", "noluY+u", "UneIqIg", "kQeX049", "fmlBfb1", "b0aAln3", "6a0VWiV", "3bxzL8e", "S3gsgXq", "zJl4N4d", "igSbXP4"], "rds": {"m": ["FbtLogging", "IntlQtEventFalcoEvent", "CometSuspenseFalcoEvent"], "r": ["w6R9/0W"]}, "be": 1}, "CometUFICommentMenu.react": {"r": ["zJl4N4d", "7lO0k+R", "WzW470F", "b5WCEnv", "fQlL/9u", "3e1KAxr", "Sr6Aahz", "dEY7RHQ", "CPtfj1t", "xIwLeog", "+UR2TQl", "UneIqIg", "kQeX049", "BqErpsc", "fmlBfb1", "F3OAUNE", "b0aAln3", "6a0VWiV", "cJoQBHY", "S3gsgXq", "3bxzL8e", "igSbXP4"], "rdfds": {"m": ["FDSTooltipDeferredImpl.react"]}, "rds": {"m": ["FbtLogging", "CometSuspenseFalcoEvent", "IntlQtEventFalcoEvent"], "r": ["w6R9/0W"]}, "be": 1}, "GroupsCometUFIAnonActorSwitcher.react": {"r": ["zM8vC1x", "BWxeAad", "c1LU2Ag", "zJl4N4d", "ERarnfS", "dhV7BOC", "WzW470F", "fQlL/9u", "3e1KAxr", "Sr6Aahz", "hR/UgiK", "eyUgJID", "dEY7RHQ", "8WrV1qw", "+zxVnNA", "5eYaAeW", "xIwLeog", "Xti7cDw", "+UR2TQl", "vZ5yXE+", "UneIqIg", "llFC02g", "kQeX049", "BqErpsc", "eIyodOJ", "fmlBfb1", "aGNjC2c", "F3OAUNE", "b0aAln3", "BA2H2yQ", "9KQCjPh", "6a0VWiV", "+YBYEis", "ponOpY3", "wGf0JcO", "S3gsgXq", "3bxzL8e", "B4wSUBY", "igSbXP4"], "rds": {"m": ["ContextualConfig", "GroupAnonymousPostUserEventsFalcoEvent", "BladeRunnerClient", "CometToast.react", "CometRelayEF", "FbtLogging", "DGWRequestStreamClient", "CometSuspenseFalcoEvent", "IntlQtEventFalcoEvent", "MqttLongPollingRunner", "CometExceptionDialog.react", "FDSTooltipDeferredImpl.react"], "r": ["SLiUPR7", "YLJ6WbM", "H6f46ct", "yUqVmZ2", "w6R9/0W"]}, "be": 1}, "CometUFIReactionsDialog.react": {"r": ["BWxeAad", "xLbNMjG", "mZqdoer", "UHS/25B", "bUBPFT3", "5ZcHlAi", "YaaDqxu", "Yfxy6UD", "3u2o3pg", "8jW6Z3P", "9u5eYU+", "COKidXF", "AM4VhF0", "c1LU2Ag", "zJl4N4d", "lr6ew3o", "4I6cCSh", "7EdE7i/", "7lO0k+R", "aLfc+ws", "jjcqkJ7", "dhV7BOC", "WzW470F", "9iMfnWQ", "fQlL/9u", "V/2V00p", "yUqVmZ2", "fqo+KiO", "NrtiKph", "TNitpBq", "EqgZAyz", "8MX6YxQ", "AoLnH66", "04RdAeA", "Sr6Aahz", "86Use7c", "M1MW+Si", "gnMDDkv", "pmJ826k", "KQQ5AN3", "CKZDJwj", "EgcMz3j", "w6R9/0W", "MYtQmWa", "V0WZAQ2", "rHjQ135", "NZrfaO0", "dEY7RHQ", "fYVQRSa", "8WrV1qw", "tuQh/4O", "fzG10qZ", "+zxVnNA", "mhgGz12", "FUpSXjV", "UaqI1yF", "sW0y7ab", "xb5RJlG", "wRLYtDS", "yko+HF4", "tnwf3TS", "tRo7x53", "E+0TNBB", "PLFVLtw", "xIwLeog", "HLK1Yuz", "LHUE5Ub", "r0yfBAv", "nwTZAZv", "OoD1C8w", "ipSndkH", "+UR2TQl", "vZ5yXE+", "UneIqIg", "agvWKTN", "mOYAbny", "8vZh7Cu", "gSqpwIH", "OpIFwqF", "Xbh+qlw", "fOQuQJd", "FuidHPD", "XMP8fLK", "bfgBg1O", "XtzIGcc", "r3BqJsA", "dU+KFGJ", "kQeX049", "BqErpsc", "AalEfJ7", "+qnDr4D", "ocvWTxp", "Bvwt3eX", "b3lULUy", "CASxwHv", "fmlBfb1", "U6KTmcQ", "S19GZl5", "OQMOFGw", "nGJyh3S", "F3OAUNE", "b0aAln3", "Jjqfj8u", "aOCC6m8", "yPjOCCT", "AuZKygx", "6a0VWiV", "Bj9SjIs", "S3gsgXq", "3bxzL8e", "rM665fY", "cBIPZq+", "DxDwZTK", "B4wSUBY", "6DhPJkb", "1ta0i3h", "Q55CU+C", "/ABZdos", "igSbXP4"], "rdfds": {"m": ["FDSTooltipDeferredImpl.react", "GetLsDatabase"], "r": ["Vk5fAdB", "AM+Jr2d"]}, "rds": {"m": ["MWChatTabSharedQPLAnnotations", "MWChatOpenSecureTabForUserCallback", "MWLSPreloadThreadForContact", "ContextualConfig", "BladeRunnerClient", "MAWVerifyThreadCutover", "CometToast.react", "MWChatLogOpenChatTab", "FriendingCometFriendRequestCancelMutation", "FriendingCometFriendRequestConfirmMutation", "FriendingCometFriendRequestDeleteMutation", "FriendingCometFriendRequestSendMutation", "FDSTabMenu.react", "MWChatTabOpenThreadCapabilitiesLoggingGated", "CometRelayEF", "FbtLogging", "DGWRequestStreamClient", "CometSuspenseFalcoEvent", "IntlQtEventFalcoEvent", "MAWThreadCutover", "MqttLongPollingRunner", "CometExceptionDialog.react", "hasDefaultThreadOneToOneCapability", "MAWMainThreadLogger", "MAWWebWorkerSingleton", "CometRelayEnvironment", "EncryptedBackupUploadFailureFalcoEvent", "EncryptedBackupUploadSuccessFalcoEvent", "sendToSentQPLLogger", "LSVoprfWasm", "MAWBridgeObserver", "EBIsEbEnabledSubscriber", "EBWriteMinosMailboxKeysToCache", "EBMinosVerifySingleEpoch", "PersistedQueue"], "r": ["lD27Xzj", "mRJ4GEO", "stdBzsD", "SLiUPR7", "YLJ6WbM", "ERarnfS", "cJoQBHY", "Dw6m1KW", "Dy29lk1", "W2q32qT", "grCpL4r", "TEBB51l", "3MiTeE0", "yzm5eQH"]}, "be": 1}, "CometUFICommentReactionIconTooltipContent.react": {"r": ["RaV5hgW", "WzW470F", "fQlL/9u", "9Bt5Bod", "Sr6Aahz", "M74u61C", "dEY7RHQ", "8WrV1qw", "xIwLeog", "+UR2TQl", "au+2QzJ", "UneIqIg", "XMP8fLK", "ww9ML/p", "kQeX049", "YkSpej0", "fmlBfb1", "F3OAUNE", "yPjOCCT", "6a0VWiV", "IIze1le", "S3gsgXq", "3bxzL8e", "igSbXP4"], "rds": {"m": ["FbtLogging", "IntlQtEventFalcoEvent", "CometSuspenseFalcoEvent"], "r": ["w6R9/0W"]}, "be": 1}, "CometUFICommentEditor.react": {"r": ["BWxeAad", "eol9mwB", "c1LU2Ag", "zJl4N4d", "Nsj8Xol", "9bdSDSi", "WzW470F", "fQlL/9u", "EuKj2tK", "rpZd0S1", "tu1pVhJ", "3e1KAxr", "04RdAeA", "Sr6Aahz", "rHjQ135", "kxTZ8ay", "aH0zWUi", "dEY7RHQ", "8WrV1qw", "iyq788x", "+zxVnNA", "UaqI1yF", "CPtfj1t", "xIwLeog", "r0yfBAv", "OoD1C8w", "+UR2TQl", "qu+Lt13", "OdKN4cb", "UneIqIg", "xkmdJGO", "l3afPwC", "XMP8fLK", "kQeX049", "BqErpsc", "D7bQI2o", "+qnDr4D", "NDdfdQF", "eaBodOx", "ocvWTxp", "fmlBfb1", "F3OAUNE", "b0aAln3", "6a0VWiV", "2hF/qKA", "S3gsgXq", "3bxzL8e", "B4wSUBY", "igSbXP4", "/ABZdos"], "rdfds": {"m": ["CometUFIThreadedComponentDecorator.react", "CometTooltipDeferredImpl.react", "FDSTooltipDeferredImpl.react"]}, "rds": {"m": ["CometUFIFunnelLogger", "CometGroupsPathingFunnelLogger", "ContextualConfig", "MLCInstrumentationPlugin__INTERNAL.react", "FDSConfirmationDialogImpl.react", "GroupAnonymousPostUserEventsFalcoEvent", "BladeRunnerClient", "CometRelayEF", "bumpVultureJSHash", "FbtLogging", "DGWRequestStreamClient", "CometSuspenseFalcoEvent", "IntlQtEventFalcoEvent", "MqttLongPollingRunner", "CometExceptionDialog.react"], "r": ["HLK1Yuz", "vhI7dSU", "SLiUPR7", "YLJ6WbM", "H6f46ct", "yUqVmZ2", "w6R9/0W"]}, "be": 1}, "CometIdentityBadgeInformationDialog.react": {"r": ["BWxeAad", "Mzh7qY1", "Vj0YiwX", "c1LU2Ag", "zJl4N4d", "lr6ew3o", "7lO0k+R", "WzW470F", "fQlL/9u", "V/2V00p", "JqLPex5", "3e1KAxr", "NrtiKph", "+QpNj0b", "04RdAeA", "Sr6Aahz", "Vl7c9Ia", "G6mVVjy", "EgcMz3j", "zmABFNd", "EczF9r2", "dEY7RHQ", "8WrV1qw", "+zxVnNA", "TcsEvK7", "qQ/FMOE", "NDT8ZcT", "o6YiIz7", "xIwLeog", "r0yfBAv", "OoD1C8w", "+UR2TQl", "UneIqIg", "SBqDt8y", "2v/Vn84", "XMP8fLK", "kQeX049", "BqErpsc", "+qnDr4D", "fmlBfb1", "F3OAUNE", "b0aAln3", "yPjOCCT", "Ecd0NUA", "rK/fqgT", "6a0VWiV", "S3gsgXq", "3bxzL8e", "B4wSUBY", "igSbXP4"], "rdfds": {"m": ["FDSTooltipDeferredImpl.react"]}, "rds": {"m": ["ContextualConfig", "BladeRunnerClient", "FanFundingClientEventsFalcoEvent", "StarsViewerFunnelEventFalcoEvent", "QPLUserFlow", "CometRelayEF", "FbtLogging", "DGWRequestStreamClient", "CometSuspenseFalcoEvent", "IntlQtEventFalcoEvent", "MqttLongPollingRunner", "CometExceptionDialog.react"], "r": ["SLiUPR7", "YLJ6WbM", "yUqVmZ2", "w6R9/0W", "utpnpN3", "HJ8G9XW"]}, "be": 1}, "FDSProfileVideoSection.react": {"r": ["WzW470F", "Sr6Aahz", "dEY7RHQ", "H5hJ6Qz", "G2Xz7er", "UneIqIg", "fmlBfb1", "F3OAUNE", "6a0VWiV", "S3gsgXq", "3bxzL8e"], "be": 1}, "GroupsCometUFIAnonymousActorIndicatorPopover.react": {"r": ["c1LU2Ag", "WzW470F", "fQlL/9u", "Sr6Aahz", "dEY7RHQ", "ZFvQeGA", "xIwLeog", "+UR2TQl", "UneIqIg", "oaV8GKr", "kQeX049", "BqErpsc", "fmlBfb1", "F3OAUNE", "b0aAln3", "6a0VWiV", "S3gsgXq", "3bxzL8e", "igSbXP4"], "rds": {"m": ["FbtLogging", "IntlQtEventFalcoEvent", "CometSuspenseFalcoEvent"], "r": ["w6R9/0W"]}, "be": 1}, "CometTextDelightAnimation.react": {"r": ["zJl4N4d", "WzW470F", "fQlL/9u", "tu1pVhJ", "Sr6Aahz", "dEY7RHQ", "CPtfj1t", "xIwLeog", "+UR2TQl", "UneIqIg", "Hp2WimU", "kQeX049", "+qnDr4D", "eaBodOx", "aoijp3N", "fmlBfb1", "F3OAUNE", "b0aAln3", "yPjOCCT", "6a0VWiV", "S3gsgXq", "3bxzL8e", "igSbXP4"], "rds": {"m": ["KeyframesRenderer", "FBKeyframesLoggedSession", "KeyframesAssetDecoder"], "r": ["HLyjvhV", "1W5pzvT", "9bdSDSi", "SLiUPR7", "YLJ6WbM", "KQ+loWk"]}, "be": 1}, "CometUFICommentsCountTooltipContent.react": {"r": ["WzW470F", "fQlL/9u", "Sr6Aahz", "pu2guZ5", "dEY7RHQ", "iZGA44l", "8WrV1qw", "xIwLeog", "BwjHiXU", "+UR2TQl", "au+2QzJ", "UneIqIg", "kQeX049", "YkSpej0", "fmlBfb1", "F3OAUNE", "yPjOCCT", "6a0VWiV", "S3gsgXq", "3bxzL8e", "igSbXP4"], "rds": {"m": ["FbtLogging", "IntlQtEventFalcoEvent"], "r": ["w6R9/0W"]}, "be": 1}, "CometUFIShareActionLinkMenu.react": {"r": ["BSUsW4j", "cl/gDMu", "c1LU2Ag", "zJl4N4d", "NvOtkBA", "WzW470F", "fQlL/9u", "8znbvuk", "3e1KAxr", "04RdAeA", "Sr6Aahz", "7qtAdPf", "bQ0v4Y3", "dEY7RHQ", "xIwLeog", "em9Nh4G", "r0yfBAv", "+UR2TQl", "UneIqIg", "ILZCDtk", "i+11kK5", "N8LDyNo", "SBqDt8y", "kQeX049", "BqErpsc", "fmlBfb1", "F3OAUNE", "b0aAln3", "6a0VWiV", "S3gsgXq", "3bxzL8e", "TcDdWQV", "rpZd0S1", "igSbXP4"], "rdfds": {"m": ["FDSTooltipDeferredImpl.react"]}, "rds": {"m": ["FbSharingEventFalcoEvent", "FbSharingSendFalcoEvent", "FbtLogging", "CometSuspenseFalcoEvent", "IntlQtEventFalcoEvent"], "r": ["vhI7dSU", "ERarnfS", "w6R9/0W"]}, "be": 1}, "CometResharesDialog.react": {"r": ["c1LU2Ag", "zJl4N4d", "WzW470F", "fQlL/9u", "8znbvuk", "rpZd0S1", "mwYaqHf", "xZytz85", "97nkpix", "Sr6Aahz", "cFNH8a0", "M1MW+Si", "PGlOZx0", "eejXjh7", "Tiio7T9", "rHjQ135", "wtsx95p", "dEY7RHQ", "8WrV1qw", "+zxVnNA", "CPtfj1t", "2GwOy4F", "xIwLeog", "0cNVXD5", "Al+X9aL", "r0yfBAv", "3bxzL8e", "+UR2TQl", "+/pKfsC", "UneIqIg", "H/vFQCj", "8ItDKbY", "XMP8fLK", "i7vOr1P", "kQeX049", "BqErpsc", "+qnDr4D", "eaBodOx", "ocvWTxp", "fmlBfb1", "F3OAUNE", "Zs4nYW5", "uODXzad", "b0aAln3", "bbLCiUL", "6a0VWiV", "S3gsgXq", "igSbXP4"], "rds": {"m": ["CometFeedStoriesStrategyErrorsTypedLoggerLite", "FbtLogging", "groupsCometFetchStory", "CometSuspenseFalcoEvent", "IntlQtEventFalcoEvent"], "r": ["vhI7dSU", "w6R9/0W"]}, "be": 1}, "CometUFISharesCountTooltipContent.react": {"r": ["tm5Vteg", "PIrr5al", "WzW470F", "fQlL/9u", "Sr6Aahz", "dEY7RHQ", "8WrV1qw", "xIwLeog", "+UR2TQl", "au+2QzJ", "UneIqIg", "bG4jsj6", "kQeX049", "YkSpej0", "fmlBfb1", "F3OAUNE", "yPjOCCT", "6a0VWiV", "S3gsgXq", "3bxzL8e", "igSbXP4"], "rds": {"m": ["FbtLogging", "IntlQtEventFalcoEvent"], "r": ["w6R9/0W"]}, "be": 1}, "CometUFIReactionsCountTooltipContent.react": {"r": ["isks176", "WzW470F", "fQlL/9u", "Sr6Aahz", "dEY7RHQ", "8WrV1qw", "xIwLeog", "ZVbmy7U", "+UR2TQl", "au+2QzJ", "UneIqIg", "kQeX049", "YkSpej0", "fmlBfb1", "F3OAUNE", "QgwPgYx", "yPjOCCT", "6a0VWiV", "S3gsgXq", "3bxzL8e", "igSbXP4"], "rds": {"m": ["FbtLogging", "IntlQtEventFalcoEvent"], "r": ["w6R9/0W"]}, "be": 1}, "CometUFIReactionIconTooltipContent.react": {"r": ["WzW470F", "fQlL/9u", "Sr6Aahz", "dEY7RHQ", "MJquEik", "8WrV1qw", "xIwLeog", "+UR2TQl", "au+2QzJ", "UneIqIg", "kQeX049", "YkSpej0", "Y4EwF/i", "fmlBfb1", "F3OAUNE", "yPjOCCT", "6a0VWiV", "1y7zDUg", "S3gsgXq", "3bxzL8e", "igSbXP4"], "rds": {"m": ["FbtLogging", "IntlQtEventFalcoEvent"], "r": ["w6R9/0W"]}, "be": 1}, "CometUFIEmojiPickerPopoverForLexical.react": {"r": ["KtatUH1", "q7yzyu9", "c1LU2Ag", "zJl4N4d", "lr6ew3o", "7lO0k+R", "WzW470F", "fQlL/9u", "ocPpFXj", "+4LqdMl", "NGKxIVe", "04RdAeA", "Sr6Aahz", "rHjQ135", "80mLdXp", "dEY7RHQ", "8WrV1qw", "+zxVnNA", "Q55CU+C", "o6YiIz7", "xIwLeog", "OoD1C8w", "+UR2TQl", "vZ5yXE+", "gmB/WTO", "UneIqIg", "kQeX049", "BqErpsc", "eIyodOJ", "+qnDr4D", "fmlBfb1", "F3OAUNE", "b0aAln3", "6a0VWiV", "5XRhPxa", "S3gsgXq", "3bxzL8e", "igSbXP4"], "rds": {"m": ["FbtLogging", "CometSuspenseFalcoEvent", "IntlQtEventFalcoEvent"], "r": ["w6R9/0W"]}, "be": 1}, "CometNewsRegulationDialog.react": {"r": ["zJl4N4d", "oQBvcPb", "WzW470F", "rwpPGnF", "fQlL/9u", "Sr6Aahz", "dEY7RHQ", "xIwLeog", "+UR2TQl", "UneIqIg", "kQeX049", "fmlBfb1", "b0aAln3", "6a0VWiV", "3bxzL8e", "S3gsgXq", "igSbXP4", "BqErpsc"], "rdfds": {"m": ["FDSTooltipDeferredImpl.react"]}, "rds": {"m": ["FbtLogging", "CometSuspenseFalcoEvent", "IntlQtEventFalcoEvent"], "r": ["w6R9/0W"]}, "be": 1}, "VideoPlayerShakaPerformanceLogger": {"r": ["Al+X9aL", "eaBodOx", "F3OAUNE", "b0aAln3"], "be": 1}, "CometBugReportingDialog.react": {"r": ["BWxeAad", "icvPYVd", "5ZcHlAi", "BRE+hDI", "JmFGL59", "ycTIAW1", "Vy3Bbiy", "c1LU2Ag", "zJl4N4d", "ojITMY3", "QNXKONW", "3g1t0NZ", "tVIZV0Z", "7lO0k+R", "aLfc+ws", "6VGcqRS", "4Ma1iMI", "dhV7BOC", "vTyhxMS", "WzW470F", "Xrd3WGG", "DtDUL5M", "oWsQbS6", "fQlL/9u", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toJd0rA", "eeFeY+E", "aM98A9L", "V/2V00p", "cUz2PyF", "8zIXEiN", "yUqVmZ2", "fqo+KiO", "MvDd1/g", "EqgZAyz", "04RdAeA", "Sr6Aahz", "HnAkhYd", "PScPYIr", "YTYt1rp", "SGfmCy4", "/ABZdos", "NWX9MlZ", "DdbKnc9", "Dw6m1KW", "+uep2j1", "kU+Wjsc", "vhI7dSU", "cEZMQvV", "Dy29lk1", "dEY7RHQ", "8WrV1qw", "vX5djkY", "+zxVnNA", "aXdCNxD", "dXoghUN", "UaqI1yF", "ffjgdoB", "wpWTO9l", "NDT8ZcT", "rag9nX5", "vZvSvXW", "loplRt/", "YcahXsH", "PI8rDi/", "xgWX83u", "p7OHbpn", "o6YiIz7", "tRo7x53", "W2q32qT", "F9vwxQo", "H1xSsqu", "xIwLeog", "9ljYqAX", "mRJ4GEO", "HLK1Yuz", "1NyP4fn", "hQTZGJ9", "r0yfBAv", "Xti7cDw", "3bxzL8e", "OoD1C8w", "aUsV+ZM", "+UR2TQl", "vZ5yXE+", "YLJ6WbM", "UneIqIg", "stdBzsD", "Uuvjz2E", "QRP7M/5", "AM+Jr2d", "cYUftsH", "fspCOAH", "86Oiqp3", "N8LDyNo", "1ar2nQ0", "EZk1aEK", "fBY288l", "llFC02g", "lkkDAwT", "sJiZVd7", "jOqQQE9", "oC0IBvL", "d2Q3HEX", "8yosRK4", "kArBjcw", "YlsjK3T", "JWhXVzk", "kQeX049", "BqErpsc", "vYAJdqN", "lD27Xzj", "pmqBWiS", "+qnDr4D", "Vo5JV7+", "fmlBfb1", "uG84D/Y", "FZnP6It", "/JYRIgK", "ghU/efn", "F3OAUNE", "b0aAln3", "t/ZsChu", "qRZH+bN", "bbLCiUL", "VlsG0Qa", "6a0VWiV", "X1Wm31c", "grCpL4r", "rcI/l57", "cJoQBHY", "7z3ZuCc", "T8sz4wU", "S3gsgXq", "HGVWAwO", "B4wSUBY", "d60fGUl", "Qny7GZl", "RmfsxWX", "87ouXLV", "0ZM2JNa", "nu9Op5R", "MLQYIaG", "HsC1Xx4", "8SJxzQh", "ibNaSu6", "WO/2RdL", "ogYRfIi", "OHHSBJL", "SLiUPR7", "G9pj27s", "vpbcu9X", "AM4VhF0", "/VWhfKx", "LUWQZ8u", "d1lq6jp", "igSbXP4", "q/7xany"], "rdfds": {"m": ["FDSTooltipDeferredImpl.react", "CometCaptureScreenshotTool.react", "CometAdsBugReportingCategory.react", "CometExceptionDialog.react", "GetLsDatabase"], "r": ["ga2vRTt", "KPxcHWu", "/RQop9e", "Xcsq+ML", "azfOg0F", "GxmKLih", "6RoZJk2", "51Zu8du", "rqAmTFz", "fBPefy0", "D6p9uYq", "UhlDJwB", "Vk5fAdB"]}, "rds": {"m": ["ContextualConfig", "MAWWebWorkerSingleton", "FDSConfirmationDialogImpl.react", "BladeRunnerClient", "CometToast.react", "MAWBridgeObserver", "bumpVultureJSHash", "FbtLogging", "DGWRequestStreamClient", "CometSuspenseFalcoEvent", "PersistedQueue", "IntlQtEventFalcoEvent", "json-bigint", "MqttLongPollingRunner", "LSVoprfWasm"], "r": ["w6R9/0W", "3MiTeE0", "ZD+S2f8", "yzm5eQH"]}, "be": 1}, "KeyframesRenderer": {"r": ["HLyjvhV", "1W5pzvT", "6a0VWiV"], "be": 1}, "FBKeyframesLoggedSession": {"r": ["9bdSDSi", "SLiUPR7", "fQlL/9u", "HLyjvhV", "YLJ6WbM", "1W5pzvT", "KQ+loWk", "6a0VWiV", "dEY7RHQ", "S3gsgXq", "Sr6Aahz"], "be": 1}, "KeyframesAssetDecoder": {"r": ["fQlL/9u", "1W5pzvT", "KQ+loWk", "fmlBfb1", "6a0VWiV", "S3gsgXq", "Sr6Aahz"], "be": 1}, "CometUFIVoteCountDialog.react": {"r": ["BWxeAad", "UHS/25B", "5ZcHlAi", "8jW6Z3P", "AM4VhF0", "c1LU2Ag", "zJl4N4d", "lr6ew3o", "HFlmHy3", "7lO0k+R", "aLfc+ws", "dhV7BOC", "WzW470F", "fQlL/9u", "lvLtOwk", "V/2V00p", "yUqVmZ2", "fqo+KiO", "NrtiKph", "EqgZAyz", "04RdAeA", "Sr6Aahz", "M1MW+Si", "vhI7dSU", "dEY7RHQ", "8WrV1qw", "tuQh/4O", "fzG10qZ", "+zxVnNA", "FUpSXjV", "UaqI1yF", "sW0y7ab", "tRo7x53", "PLFVLtw", "xIwLeog", "HLK1Yuz", "LHUE5Ub", "r0yfBAv", "OoD1C8w", "ipSndkH", "+UR2TQl", "vZ5yXE+", "UneIqIg", "mOYAbny", "9YQ3krq", "YJaIoCW", "OpIFwqF", "r3BqJsA", "dU+KFGJ", "kQeX049", "ew5l1G+", "BqErpsc", "+qnDr4D", "ocvWTxp", "fmlBfb1", "rSCWjJs", "nGJyh3S", "F3OAUNE", "b0aAln3", "Jjqfj8u", "aOCC6m8", "6a0VWiV", "tXSoCdA", "Bj9SjIs", "S3gsgXq", "3bxzL8e", "B4wSUBY", "Q55CU+C", "XMP8fLK", "igSbXP4"], "rdfds": {"m": ["FDSTooltipDeferredImpl.react", "GetLsDatabase"], "r": ["Vk5fAdB", "AM+Jr2d"]}, "rds": {"m": ["MWChatTabSharedQPLAnnotations", "MWChatOpenSecureTabForUserCallback", "MWLSPreloadThreadForContact", "ContextualConfig", "BladeRunnerClient", "MAWVerifyThreadCutover", "CometToast.react", "MWChatLogOpenChatTab", "FriendingCometFriendRequestCancelMutation", "FriendingCometFriendRequestConfirmMutation", "FriendingCometFriendRequestDeleteMutation", "FriendingCometFriendRequestSendMutation", "FDSTabMenu.react", "MWChatTabOpenThreadCapabilitiesLoggingGated", "CometRelayEF", "FbtLogging", "DGWRequestStreamClient", "CometSuspenseFalcoEvent", "IntlQtEventFalcoEvent", "MAWThreadCutover", "MqttLongPollingRunner", "CometExceptionDialog.react", "hasDefaultThreadOneToOneCapability", "MAWMainThreadLogger", "MAWWebWorkerSingleton", "CometRelayEnvironment", "EncryptedBackupUploadFailureFalcoEvent", "EncryptedBackupUploadSuccessFalcoEvent", "sendToSentQPLLogger", "LSVoprfWasm", "MAWBridgeObserver", "EBIsEbEnabledSubscriber", "EBWriteMinosMailboxKeysToCache", "EBMinosVerifySingleEpoch", "PersistedQueue"], "r": ["lD27Xzj", "mRJ4GEO", "stdBzsD", "SLiUPR7", "YLJ6WbM", "w6R9/0W", "ERarnfS", "cJoQBHY", "Dw6m1KW", "Dy29lk1", "W2q32qT", "grCpL4r", "TEBB51l", "3MiTeE0", "yzm5eQH"]}, "be": 1}, "VideoPlayerNextgendashWorkerEnvironment": {"r": ["B4wSUBY", "BqErpsc", "fmlBfb1", "F3OAUNE", "b0aAln3", "3bxzL8e"], "be": 1}, "VideoPlayerHTML5ApiCea608State": {"r": ["9GYFIOR", "6a0VWiV"], "be": 1}, "VideoPlayerHTML5ApiWebVttState": {"r": ["9GYFIOR"], "be": 1}, "KeyEventTypedLogger": {"r": ["rCasuzG", "w6R9/0W", "6a0VWiV", "S3gsgXq", "Sr6Aahz"], "be": 1}, "CometFeedTombstoneFactory.react": {"r": ["c1LU2Ag", "zJl4N4d", "WzW470F", "fQlL/9u", "Sr6Aahz", "eejXjh7", "dEY7RHQ", "HhOLZv6", "xIwLeog", "+UR2TQl", "UneIqIg", "kQeX049", "Pz4N/UZ", "fmlBfb1", "F3OAUNE", "b0aAln3", "6a0VWiV", "S3gsgXq", "3bxzL8e", "igSbXP4"], "rds": {"m": ["FbtLogging", "CometSuspenseFalcoEvent", "IntlQtEventFalcoEvent"], "r": ["w6R9/0W"]}, "be": 1}, "CometHovercardQueryRenderer.react": {"r": ["WzW470F", "fQlL/9u", "Sr6Aahz", "dEY7RHQ", "+zxVnNA", "AnILcqd", "+UR2TQl", "UneIqIg", "kSBEAJR", "fmlBfb1", "vunQ0c5", "F3OAUNE", "wnm0qxx", "6a0VWiV", "S3gsgXq", "3bxzL8e"], "be": 1}, "CometUnifiedShareSheetDialog.react": {"r": ["OotE7mX", "7cDw5lL", "5ZcHlAi", "oD/CqYu", "c1LU2Ag", "zJl4N4d", "M3f7g0U", "lr6ew3o", "FlX0L67", "7lO0k+R", "ZQvJHOc", "aLfc+ws", "V74TdxN", "dhV7BOC", "vTyhxMS", "WzW470F", "fQlL/9u", "9o5QL6R", "8zIXEiN", "yUqVmZ2", "3e1KAxr", "fqo+KiO", "dRzsvGe", "Pcsk72i", "04RdAeA", "Sr6Aahz", "M1MW+Si", "HWNocUu", "MnVo2aO", "0v1O4gw", "6kBWdiH", "eejXjh7", "Dw6m1KW", "8ug66M5", "Dy29lk1", "dEY7RHQ", "a88Ad18", "oEdkrad", "v+uDnVu", "W2q32qT", "xIwLeog", "mRJ4GEO", "rDXZndH", "KpxglbU", "e7tQB1X", "r0yfBAv", "slHrMON", "+UR2TQl", "ncPSbsy", "itp/xXY", "UneIqIg", "stdBzsD", "R3FKt/X", "AM+Jr2d", "1XdChB+", "ItL5+o1", "7UYuNic", "Cs+cukw", "SL3Uz4K", "jKEyzV1", "NOcJNLa", "xMFqckF", "kQeX049", "BqErpsc", "+qnDr4D", "wmmlFbB", "fmlBfb1", "LuHuJ91", "ayyrnWQ", "k57/HUr", "F3OAUNE", "b0aAln3", "7+4+/Y/", "KHq4KhJ", "4EHtWLU", "bbLCiUL", "6a0VWiV", "grCpL4r", "+YBYEis", "S3gsgXq", "3bxzL8e", "PK+F/MP", "jgPqmY3", "5VgrE1i", "HUzfpWY", "UxdVV57", "ggGfIQq", "H1jy+x/", "hB3HmPI", "TrwjuC0", "H7eyX/h", "igSbXP4", "TEBB51l", "B4wSUBY"], "rdfds": {"m": ["UnifiedShareSheetComposerSection.react", "UnifiedShareSheetMessengerSection.react", "UnifiedShareSheetSharingOptionsSection.react", "FDSTooltipDeferredImpl.react", "CometExceptionDialog.react", "GetLsDatabase", "UnifiedShareSheetFriendsFeedComposerSection.react", "CometComposerClientErrorMessage.react"], "r": ["hIkUVdb", "TvzPlDV", "BpsYMpu", "cWiXD6v", "3V2Xot9", "8LbgiWz", "i4CrvYu", "EzP7bX1", "BWxeAad", "GR6Cq5p", "jiM4LmN", "lJv37mu", "9mSO72G", "Lq3HpVY", "pUGpOvY", "qfVcy12", "3FqyLS2", "tYlWRD4", "T8qboaB", "nFsTNjJ", "6hSk48V", "8jW6Z3P", "BNU0jo9", "I+xSE/e", "7VTYpw7", "LHI+p96", "SUImyBw", "GmM9mRy", "Nc4MiT5", "lmr4KaY", "8yF50Ai", "QcEn6y5", "rHmh3sl", "Jn0P8Y+", "aT8eM7o", "XA3A8XO", "bFENf+A", "H3kq1tg", "l9zbJTz", "vg+fw9x", "e56YkiO", "bIlbDQH", "F7ML0+a", "JCbeqG9", "Sa5NeBO", "tadXoV3", "AM2M7Pp", "g6apr04", "YzRDiEz", "iLCTNyP", "WktD9Cw", "/VeHYb4", "rpZd0S1", "f+gJDyQ", "b2dYbyi", "G67HnM6", "+dn85S9", "/kkOgx3", "Ld2p5v3", "4p+JRWR", "8Gk0DMA", "ePU6Tbw", "+312sWv", "jBonzwd", "+UL8Tpy", "jFMbfaR", "nP5XLJr", "1d8Uoy6", "KENSO9Y", "oF6Plie", "yfr8tE+", "cFNH8a0", "LSHcyDl", "PxyDiSj", "TgS/vhe", "qPB2M6L", "7hdxfqa", "TBYeUEy", "lxmHHZK", "k03KUxU", "HF0Twol", "N57hjl4", "gr2FW4/", "w8X7Q7u", "+ISoPLA", "ADwB+G0", "NH8SsM9", "z8CauSQ", "PMZLJ80", "m1hOrsu", "su0h8DZ", "0YaIyZb", "HsiO4c5", "gWl5LCD", "SpYVXeD", "PO5eZU3", "w6R9/0W", "96ks/Xo", "+bGK9ad", "kzhLPe8", "JoPh5yD", "pe/RT9B", "74zNR2g", "nQbgdUp", "psQDGpx", "vhI7dSU", "rHjQ135", "OYjPhNm", "VUeIUkc", "xp4ImVI", "UYmI+GK", "gdMQTbM", "unqbfcp", "8WrV1qw", "b/eTD2J", "B+5KHZ+", "aINM4Q8", "K47Ckms", "n6tfCUD", "TuYREcN", "+zxVnNA", "llwAFtq", "uIX25y7", "0+heN/J", "N/vRBVJ", "UaqI1yF", "15itoyT", "CPtfj1t", "3YRytZr", "A5W4nZ3", "Xu3tDev", "FMqtL1o", "JgGlnLc", "sCjm+Yb", "MgKiaJn", "02d+gRA", "HwWnpJj", "Oi4NxdN", "sgl2pMi", "bQhI7GY", "N+sotdu", "FPEeweo", "0vIfjJM", "IKFZtXQ", "gUATRzI", "MLjC8UI", "JRZjPe5", "180F4gD", "LsPbQ/L", "QCvjme6", "8N0YBCE", "tRo7x53", "boeTD6t", "ZfWmFZy", "K2dQxft", "oRaPilD", "19JPONl", "HLK1Yuz", "0frBl3D", "Wtqx6Y7", "WNwptxP", "gDCAJKu", "E2rEch4", "bRdUIoS", "5DRFeKx", "xvBEKM8", "Ns1vBfY", "Z6VCeUW", "pJzF+q6", "OoD1C8w", "ntCdn6n", "TbGs6/p", "T9ChB+0", "MduUQdJ", "AH4m43u", "X+oBYJE", "UbL4CQm", "vZ5yXE+", "b<PERSON><PERSON><PERSON><PERSON>", "H/vFQCj", "kDKn14A", "4zi/AFk", "5FTrDGG", "3OGVOyL", "xcBxTPy", "3F8z+dY", "nookUis", "1Cu1JIP", "a/Z+BCQ", "UJFYPMZ", "s1jhi8t", "dSR0j0H", "rIVwBcu", "UtQq7+g", "5twxztF", "TeK1XKp", "700+/6W", "IPU4NQd", "ze83gae", "KeRf35c", "llFC02g", "C7D+vEj", "wCrtyOq", "bXI03Wh", "mVu9uoT", "WI59aUS", "ClDCH53", "Mgwp148", "KLzg2CD", "HeYtjgz", "XMP8fLK", "99pUYEy", "Kv6q2cC", "SPrY11N", "J0P76gH", "A0ACyj7", "SL4XDv7", "h11k3h5", "QkX67Ft", "9x4c2GH", "/pXVZGK", "+iUProi", "2ibftsI", "yH7vKwO", "vfknPFm", "Tj16T2y", "4wcAGvi", "6679oPv", "VBYg64A", "RuQeYF1", "mb2FyEe", "65AtEbt", "SziyUR7", "ugZ5ced", "KNaOiUn", "gY2ZD6c", "tqz4pss", "saIt3ym", "pMC4hWu", "Ok10Exp", "mUjCKLX", "cngQrUl", "IdfGV10", "O9+1txH", "yPjOCCT", "jWWddi1", "Mw+GpS/", "D6XDYIc", "eyIHSOv", "JI7UIHz", "fKBhU/u", "X1Wm31c", "ug1uvNU", "A2vW0y/", "S+INbLD", "PyCcSfn", "iCcGRVy", "mhD3JYD", "llbVdIJ", "7iQLlvN", "Catu5KG", "rgwfod6", "28Nm4jp", "scKq6Vs", "BS9vhvV", "OBmSBCp", "QFiH7vy", "sYXuZU/", "PqHe6/3", "lqDeKzt", "bq0rwvh", "2pNZSSg", "6SVXUI4", "QbB9hXs", "h4brfME", "n6bBGlv", "7oGtCMe", "IMdCkYz", "2WJ17sH", "PyXKfsc", "Z+ZhA+U", "PR/QqP4", "Sl2mOF/", "PpZ/prg", "pBaKGZW", "Ws4kX8p", "0mrha7k", "wwbaauH", "1Vyr9Qr", "lHOyYgN", "10sGnCR", "CkFhcbo", "L0bJ3QZ", "wUzAehd", "D6p9uYq", "9bdSDSi", "C9n16BW", "8TrOtV3", "9MjDiQ4", "UC5UTmP", "sn8Wapu", "0vn/yFz", "/9XLdaK", "FcNell1", "Vk5fAdB", "JWFDcmK", "2EJL/If", "oubeIYT", "VBlHWsL", "BwdkyfY", "1Gl3Z2Z", "kVC0/wZ"]}, "rds": {"m": ["MAWWebWorkerSingleton", "FbSharingEventFalcoEvent", "FbSharingSendFalcoEvent", "CometToast.react", "EBIsEbEnabledSubscriber", "FbtLogging", "CometSuspenseFalcoEvent", "PersistedQueue", "IntlQtEventFalcoEvent", "MAWMainThreadLogger", "ContextualConfig", "BladeRunnerClient", "CometRelayEnvironment", "hasDefaultThreadOneToOneCapability", "EncryptedBackupUploadFailureFalcoEvent", "EncryptedBackupUploadSuccessFalcoEvent", "sendToSentQPLLogger", "LSVoprfWasm", "MAWBridgeObserver", "EBWriteMinosMailboxKeysToCache", "EBMinosVerifySingleEpoch", "DGWRequestStreamClient", "ComposerTelemetryFalcoEvent", "linkAttachmentAreaFeedCreationDataTransform", "CometComposerEmojiPluginForLexical.react", "composerLexicalMessageFeedTransform", "FeedComposerCometMentionsPluginForLexical.react", "CometComposerIsDirtyPluginForLexical.react", "MLCInstrumentationPlugin__INTERNAL.react", "FDSAlertDialogImpl.react", "FDSConfirmationDialogImpl.react", "ComposerStoryCreateMutation", "AudienceOverridesClientFalcoEvent", "composerAudienceFeedTransform", "ComposerStoryEditMutation", "composerTrackingTransform", "CometComposerLinksPluginForLexicalWithDefaultExport.react", "CometRelayEF", "bumpVultureJSHash", "cometComposerQPLLogger", "MaybeUpdateTargetBasedOnODCutoverOrDefaultE2EE", "MAWVerifyThreadCutover", "MqttLongPollingRunner", "CometComposerHashtagPluginForLexical.react", "cometPushToast", "ODS", "CometComposerWarnStatusTextLengthForLexical.react", "MAWMiActOnMiThreadExistsForJid__DO_NOT_USE", "getUpgradedCometHashtagComposerHandler"], "r": ["ERarnfS", "3MiTeE0", "SLiUPR7", "YLJ6WbM", "yzm5eQH", "DDSlFiM", "zopYFkM", "KViJ+xR", "Y7KrVSA", "hp7MII7", "kIlybN8", "Z137xwt", "5MdZ3xo", "khW4oBp", "5RjwtZm", "82enPUW", "4zEfisS", "39wkuVA", "IIBk8nD", "Y/5CFWr", "wlYtfRY", "puF/Jv0", "8EKiu92", "IxmgSpq", "CGPhg4D", "LsiHKqb", "lnfihh+", "9xukryL", "jTvsNNJ", "XRVr06B", "o3UHCOB", "8O+i0Uv", "WwozWWD", "NrtiKph", "sGaAkoX", "SW/Pwbx", "lD27Xzj", "8uRk9jl", "Q7wos0g"]}, "be": 1}, "ReshareWarningCometDialog.react": {"r": ["WzW470F", "Sr6Aahz", "eejXjh7", "dEY7RHQ", "3Z8XRPr", "UneIqIg", "fmlBfb1", "QxTlvuf", "F3OAUNE", "6a0VWiV", "S3gsgXq", "3bxzL8e"], "be": 1}, "CometFocusedStoryDialogViewRoot.react": {"r": ["yOm2EYG", "c1LU2Ag", "zJl4N4d", "3JwW/sv", "9bdSDSi", "/0RruWF", "dhV7BOC", "WzW470F", "fQlL/9u", "rpZd0S1", "3e1KAxr", "PNVU5rx", "02nrRf5", "PSodRI+", "04RdAeA", "Sr6Aahz", "QaNXVuO", "yx6GLga", "5fRW/ex", "eejXjh7", "rHjQ135", "dEY7RHQ", "+zxVnNA", "RdpCget", "UaqI1yF", "CPtfj1t", "A+OlLU8", "tRo7x53", "xIwLeog", "Ausgm2K", "r0yfBAv", "3bxzL8e", "+UR2TQl", "UneIqIg", "abQMyJn", "+1z1MmN", "MXfmqY5", "8ItDKbY", "SBqDt8y", "XMP8fLK", "kQeX049", "BqErpsc", "+qnDr4D", "HzbNPua", "eaBodOx", "CNyGFUs", "ocvWTxp", "fmlBfb1", "F3OAUNE", "Z47tVyT", "b0aAln3", "6a0VWiV", "+Vxr4+Q", "S3gsgXq", "B4wSUBY", "igSbXP4"], "rds": {"m": ["Banzai", "ContextualConfig", "FDSConfirmationDialogImpl.react", "BladeRunnerClient", "CometToast.react", "CometFeedStoriesStrategyErrorsTypedLoggerLite", "CometFeedUnitLoadingIndicatorMask.react", "json-bigint", "FbtLogging", "DGWRequestStreamClient", "CometSuspenseFalcoEvent", "CometUFIReactionsAnimationPreloader", "ODS", "IntlQtEventFalcoEvent", "FDSTooltipDeferredImpl.react", "MqttLongPollingRunner", "KeyframesRenderer", "FBKeyframesLoggedSession", "KeyframesAssetDecoder"], "r": ["SLiUPR7", "YLJ6WbM", "yUqVmZ2", "w6R9/0W", "vhI7dSU", "ZD+S2f8", "HLyjvhV", "yPjOCCT", "1W5pzvT", "KQ+loWk"]}, "be": 1}, "KeyframesPluginRandomSubdocument": {"r": ["dQvu7CT", "V/UX1Wg", "6iU1NsB", "fmlBfb1", "/r9v1Kb"], "be": 1}, "KeyframesPluginRandomSubdocumentSchema": {"r": ["1yqwozP"], "be": 1}, "KeyframesPluginSoundSchema": {"r": ["M+waYRw", "1W5pzvT"], "be": 1}, "KeyframesPluginSound": {"r": ["WMm0L8D", "dQvu7CT", "1W5pzvT"], "be": 1}, "KeyframesPluginTrimPathSchema": {"r": ["dQvu7CT", "1W5pzvT"], "be": 1}, "KeyframesPluginTrimPath": {"r": ["Nm4S7Tg", "dQvu7CT", "mFkoxEV", "1W5pzvT"], "be": 1}, "KeyframesPluginLayerNameSchema": {"r": ["E9ERssr"], "be": 1}, "KeyframesPluginLayerName": {"r": ["l4PzlRm", "dQvu7CT"], "be": 1}, "KeyframesPluginDynamicProperties": {"r": ["dQvu7CT", "3UpiF8K"], "be": 1}, "KeyframesPluginDynamicPropertiesSchema": {"r": ["88Pbcfe"], "be": 1}, "KeyframesPluginLayerTags": {"r": ["dQvu7CT"], "be": 1}, "KeyframesPluginLayerTagsSchema": {"r": ["dQvu7CT"], "be": 1}, "KeyframesPluginSubdocumentSwap": {"r": ["dQvu7CT", "r4Fh30Y"], "be": 1}, "KeyframesPluginSubdocumentSwapSchema": {"r": ["cumX1+0"], "be": 1}, "ManageConstituentBadgeDialog.react": {"r": ["aztdKki", "1xo+hDv", "zJl4N4d", "WzW470F", "691Bs6K", "fQlL/9u", "0fX8tIP", "LepQ8Dg", "Sr6Aahz", "H6f46ct", "dEY7RHQ", "VAKtv0Q", "+zxVnNA", "am21hQJ", "xIwLeog", "+UR2TQl", "UneIqIg", "wO4Dw2o", "kQeX049", "+qnDr4D", "fmlBfb1", "dLciGzt", "F3OAUNE", "b0aAln3", "6a0VWiV", "S3gsgXq", "3bxzL8e", "B4wSUBY", "BqErpsc", "igSbXP4"], "rdfds": {"m": ["FDSTooltipDeferredImpl.react"]}, "rds": {"m": ["ContextualConfig", "BladeRunnerClient", "CometRelayEF", "FbtLogging", "DGWRequestStreamClient", "CometSuspenseFalcoEvent", "IntlQtEventFalcoEvent", "MqttLongPollingRunner", "CometExceptionDialog.react"], "r": ["SLiUPR7", "YLJ6WbM", "yUqVmZ2", "w6R9/0W"]}, "be": 1}, "WebSpeedInteractionsTypedLogger": {"r": ["9NiATAn", "w6R9/0W", "6a0VWiV", "S3gsgXq", "Sr6Aahz"], "be": 1}, "PerfXSharedFields": {"r": ["fmlBfb1", "6a0VWiV"], "be": 1}, "ExceptionDialog": {"r": ["DrMj<PERSON>yn", "lAY3K2N", "zJl4N4d", "dC77nhW", "WzW470F", "gQMM0GX", "fQlL/9u", "MeQok+1", "1NIJ61Z", "ZoNh5oc", "dEY7RHQ", "Iak57b2", "Q55CU+C", "rzjumBy", "98Y3qDH", "+UR2TQl", "igSbXP4", "SW/Pwbx", "6iU1NsB", "Md/Ftky", "fmlBfb1", "6a0VWiV", "3bxzL8e", "w6R9/0W"], "rds": {"m": ["FbtLogging", "IntlQtEventFalcoEvent"]}, "be": 1}, "react": {"r": ["WzW470F", "fmlBfb1", "3bxzL8e"], "be": 1}, "CometLoggedOutPopupCTA.react": {"r": ["3V2Xot9", "QjUdE2H", "BWxeAad", "c1LU2Ag", "zJl4N4d", "sjzhGIG", "I+GHswV", "WzW470F", "fQlL/9u", "rpZd0S1", "3OQxuIz", "x8yY7UR", "Sr6Aahz", "TBYeUEy", "R6tXgFd", "w6R9/0W", "cEZMQvV", "dEY7RHQ", "8WrV1qw", "Iak57b2", "+zxVnNA", "xIwLeog", "r0yfBAv", "OoD1C8w", "+UR2TQl", "UneIqIg", "llFC02g", "bv1jtSc", "kQeX049", "BqErpsc", "fmlBfb1", "dfIe6wS", "F3OAUNE", "Z47tVyT", "b0aAln3", "6a0VWiV", "X1Wm31c", "S3gsgXq", "3bxzL8e", "igSbXP4", "CPtfj1t"], "rdfds": {"m": ["FDSTooltipDeferredImpl.react"]}, "rds": {"m": ["FbtLogging", "CometSuspenseFalcoEvent", "IntlQtEventFalcoEvent"]}, "be": 1}}, "indexUpgrades": {"__hblp": ":7171,12350,1551,12501,9102,8409,7528,9418,10192,13264,13893,13025,13007,13171,12738,12833,7608,6839,8948,8689,13178,12295,12502,12934,13180,13182,9736,9710,747,13986,529,530,532,9728,14403,12693,12695,10641,12267,12370,10173,9003,10062,10646,11937,13798,13862,5263,13274,13495,13496,12273,12274,5113,5111,12275,12276,10945,3416,13696,3573,803,7692", "__hsdp": ":384,487,23,488,478,477,480,479,489,490,475,476,483,485,551,550,697,696,665,664,526,527,438,437,788,789,545,544,680,681,444,445,465,464,533,534,469,468,547,548,568,566,503,509,505,512,511,515,504,497,495,499,496,508,498,507,517,513,501,516,494,493,434,435,729,728,669,668,430,463,24,154,25,223,160,158,462,461,472,471,161,212,48,253,424,535,572,565,597,598,612,599,519,520,675,521,575,576,674,470,457,474,414,567,506,510,500,514,323,725,5,153,415,12,151,133,1,2,138,146,8,6,224,183,136,18,144,14,3,150,26,149,152,20,16,17,215,200,199,132,185,317,299,37,13,238,242,188,486,392,403,724,556,440,442,428,216,554,502,423,439,555,559,443,602,600,594,601,53,603,607,662,214,536,578,76,460,528,522,524,523,466,467,518,436,108,159,229,529,530,532,492,318,484,482,595,592,596,663,370,54,570,569,571,563,562,134,140,9,7,19,145,15,4,27,131,312,40,22,688,558,560,552,561,441,609,458,593,369,531,411,209,608,135,139,11,10,38,39,42,33,34,44,41,21,557,210,46,137,45,35,36,553,47,43,427,543,539,538,541,537,540,542"}}}, "jsmods": {"define": [["cr:6640", ["PromiseImpl"], {"__rc": ["PromiseImpl", null]}, -1], ["cr:70", ["FBInteractionTracingDependencies"], {"__rc": ["FBInteractionTracingDependencies", null]}, -1], ["cr:244", ["CometEmoji.react"], {"__rc": ["CometEmoji.react", null]}, -1], ["cr:310", ["RunWWW"], {"__rc": ["RunWWW", null]}, -1], ["cr:619", ["setTimeoutCometLoggingPriWWW"], {"__rc": ["setTimeoutCometLoggingPriWWW", null]}, -1], ["cr:686", [], {"__rc": [null, null]}, -1], ["cr:734", [], {"__rc": [null, null]}, -1], ["cr:755", ["warningWWW"], {"__rc": ["warningWWW", null]}, -1], ["cr:1078", [], {"__rc": [null, null]}, -1], ["cr:1080", ["unexpectedUseInComet"], {"__rc": ["unexpectedUseInComet", null]}, -1], ["cr:1126", ["TimeSliceImpl"], {"__rc": ["TimeSliceImpl", null]}, -1], ["cr:1293", ["ReactDOM.classic"], {"__rc": ["ReactDOM.classic", null]}, -1], ["cr:2448", ["useHeroBootloadedComponent"], {"__rc": ["useHeroBootloadedComponent", null]}, -1], ["cr:3725", ["clearTimeoutWWWOrMobile"], {"__rc": ["clearTimeoutWWWOrMobile", null]}, -1], ["cr:3976", [], {"__rc": [null, null]}, -1], ["cr:4344", ["setTimeoutWWWOrMobile"], {"__rc": ["setTimeoutWWWOrMobile", null]}, -1], ["cr:4874", [], {"__rc": [null, null]}, -1], ["cr:5079", [], {"__rc": [null, null]}, -1], ["cr:5128", [], {"__rc": [null, null]}, -1], ["cr:5621", ["CometLinkOldImpl.react"], {"__rc": ["CometLinkOldImpl.react", null]}, -1], ["cr:7063", ["ErrorBoundary.react"], {"__rc": ["ErrorBoundary.react", null]}, -1], ["cr:7162", ["ReactDOMCompatibilityLayer"], {"__rc": ["ReactDOMCompatibilityLayer", null]}, -1], ["cr:7385", ["clearIntervalWWW"], {"__rc": ["clearIntervalWWW", null]}, -1], ["cr:7388", ["setIntervalWWW"], {"__rc": ["setIntervalWWW", null]}, -1], ["cr:7389", ["setIntervalAcrossTransitionsWWW"], {"__rc": ["setIntervalAcrossTransitionsWWW", null]}, -1], ["cr:7391", ["setTimeoutAcrossTransitionsWWW"], {"__rc": ["setTimeoutAcrossTransitionsWWW", null]}, -1], ["cr:7422", ["ImageDownloadTrackerWWW"], {"__rc": ["ImageDownloadTrackerWWW", null]}, -1], ["cr:7730", ["getFbtResult"], {"__rc": ["getFbtResult", null]}, -1], ["cr:8907", ["HeroTracingCoreConfigWWW"], {"__rc": ["HeroTracingCoreConfigWWW", null]}, -1], ["cr:8908", ["HeroTracingCoreDependenciesWWW"], {"__rc": ["HeroTracingCoreDependenciesWWW", null]}, -1], ["cr:8958", ["FBJSON"], {"__rc": ["FBJSON", null]}, -1], ["cr:8959", ["DTSG"], {"__rc": ["DTSG", null]}, -1], ["cr:8960", ["DTSG_ASYNC"], {"__rc": ["DTSG_ASYNC", null]}, -1], ["cr:10448", [], {"__rc": [null, null]}, -1], ["cr:11054", [], {"__rc": [null, null]}, -1], ["cr:15957", [], {"__rc": [null, null]}, -1], ["cr:17264", [], {"__rc": [null, null]}, -1], ["cr:20266", ["useEmptyFunction"], {"__rc": ["useEmptyFunction", null]}, -1], ["cr:20362", ["FDSTextImpl.react"], {"__rc": ["FDSTextImpl.react", null]}, -1], ["cr:20414", ["FDSTextContext_Old.react"], {"__rc": ["FDSTextContext_Old.react", null]}, -1], ["cr:22912", [], {"__rc": [null, null]}, -1], ["cr:23134", [], {"__rc": [null, null]}, -1], ["cr:696703", [], {"__rc": [null, null]}, -1], ["cr:955714", [], {"__rc": [null, null]}, -1], ["cr:1008801", [], {"__rc": [null, null]}, -1], ["cr:1064332", [], {"__rc": [null, null]}, -1], ["cr:1080422", [], {"__rc": [null, null]}, -1], ["cr:1106516", [], {"__rc": [null, null]}, -1], ["cr:1108857", [], {"__rc": [null, null]}, -1], ["cr:1294158", ["React.classic"], {"__rc": ["React.classic", null]}, -1], ["cr:1294159", ["ReactDOM.classic"], {"__rc": ["ReactDOM.classic", null]}, -1], ["cr:1522191", ["CometLinkTrackingUtils.facebook"], {"__rc": ["CometLinkTrackingUtils.facebook", null]}, -1], ["cr:1645510", [], {"__rc": [null, null]}, -1], ["cr:1984081", [], {"__rc": [null, null]}, -1], ["cr:534", [], {"__rc": [null, null]}, -1], ["cr:851", [], {"__rc": [null, null]}, -1], ["cr:984", [], {"__rc": [null, null]}, -1], ["cr:2928", ["relay-runtime"], {"__rc": ["relay-runtime", null]}, -1], ["cr:5655", [], {"__rc": [null, null]}, -1], ["cr:5906", ["VideoPlayerNextgendashEngine"], {"__rc": ["VideoPlayerNextgendashEngine", null]}, -1], ["cr:7269", [], {"__rc": [null, null]}, -1], ["cr:7329", [], {"__rc": [null, null]}, -1], ["cr:7383", ["BanzaiWWW"], {"__rc": ["BanzaiWWW", null]}, -1], ["cr:7438", [], {"__rc": [null, null]}, -1], ["cr:7581", [], {"__rc": [null, null]}, -1], ["cr:9378", [], {"__rc": [null, null]}, -1], ["cr:9379", [], {"__rc": [null, null]}, -1], ["cr:9984", [], {"__rc": [null, null]}, -1], ["cr:10321", [], {"__rc": [null, null]}, -1], ["cr:17286", [], {"__rc": [null, null]}, -1], ["cr:21303", [], {"__rc": [null, null]}, -1], ["cr:925100", ["RunBlue"], {"__rc": ["RunBlue", null]}, -1], ["cr:1110430", [], {"__rc": [null, null]}, -1], ["cr:1121434", [], {"__rc": [null, null]}, -1], ["cr:1467370", ["RelayFBScheduler"], {"__rc": ["RelayFBScheduler", null]}, -1], ["cr:1752405", ["QPLAddBlueRequestHeaders"], {"__rc": ["QPLAddBlueRequestHeaders", null]}, -1], ["cr:945", [], {"__rc": [null, null]}, -1], ["cr:17228", ["BaseBlueModal.react"], {"__rc": ["BaseBlueModal.react", null]}, -1], ["cr:20294", [], {"__rc": [null, null]}, -1], ["cr:17197", ["CometFeedUnitStoryStrategyImpl.react"], {"__rc": ["CometFeedUnitStoryStrategyImpl.react", null]}, -1], ["cr:2099", [], {"__rc": [null, null]}, -1], ["cr:3843", ["BaseHovercardTriggerAccessible.react"], {"__rc": ["BaseHovercardTriggerAccessible.react", null]}, -1], ["cr:7663", ["ActorHovercardAccessible.react"], {"__rc": ["ActorHovercardAccessible.react", null]}, -1], ["cr:1408565", [], {"__rc": [null, null]}, -1], ["cr:6243", ["CometUFIUSSShareAction.react"], {"__rc": ["CometUFIUSSShareAction.react", null]}, -1], ["cr:293", ["LexicalHistoryPlugin.prod"], {"__rc": ["LexicalHistoryPlugin.prod", null]}, -1], ["cr:370", ["Lexical.prod"], {"__rc": ["Lexical.prod", null]}, -1], ["cr:430", [], {"__rc": [null, null]}, -1], ["cr:509", ["LexicalComposerContext.prod"], {"__rc": ["LexicalComposerContext.prod", null]}, -1], ["cr:537", ["useCAALoggedOutDialogWrapperHook"], {"__rc": ["useCAALoggedOutDialogWrapperHook", null]}, -1], ["cr:556", ["HTML"], {"__rc": ["HTML", null]}, -1], ["cr:739", ["LexicalPlainTextPlugin.prod"], {"__rc": ["LexicalPlainTextPlugin.prod", null]}, -1], ["cr:1688", [], {"__rc": [null, null]}, -1], ["cr:1689", [], {"__rc": [null, null]}, -1], ["cr:2139", ["LexicalHashtag.prod"], {"__rc": ["LexicalHashtag.prod", null]}, -1], ["cr:2169", ["URITruncator"], {"__rc": ["URITruncator", null]}, -1], ["cr:2181", ["LexicalText.prod"], {"__rc": ["LexicalText.prod", null]}, -1], ["cr:2260", ["LexicalErrorBoundary.prod"], {"__rc": ["LexicalErrorBoundary.prod", null]}, -1], ["cr:2483", ["LexicalContentEditable.prod"], {"__rc": ["LexicalContentEditable.prod", null]}, -1], ["cr:2774", ["LexicalUtils.prod"], {"__rc": ["LexicalUtils.prod", null]}, -1], ["cr:2857", [], {"__rc": [null, null]}, -1], ["cr:2905", ["LexicalLink.prod"], {"__rc": ["LexicalLink.prod", null]}, -1], ["cr:2908", ["LexicalOverflow.prod"], {"__rc": ["LexicalOverflow.prod", null]}, -1], ["cr:2969", [], {"__rc": [null, null]}, -1], ["cr:3118", [], {"__rc": [null, null]}, -1], ["cr:3119", [], {"__rc": [null, null]}, -1], ["cr:3258", [], {"__rc": [null, null]}, -1], ["cr:3456", [], {"__rc": [null, null]}, -1], ["cr:3749", [], {"__rc": [null, null]}, -1], ["cr:4846", ["CometUFICommentActorLinkBadges.react"], {"__rc": ["CometUFICommentActorLinkBadges.react", null]}, -1], ["cr:4917", [], {"__rc": [null, null]}, -1], ["cr:5235", ["useEmptyFunction"], {"__rc": ["useEmptyFunction", null]}, -1], ["cr:6470", ["CometUFIComposerActions.react"], {"__rc": ["CometUFIComposerActions.react", null]}, -1], ["cr:6669", ["DataStore"], {"__rc": ["DataStore", null]}, -1], ["cr:6756", [], {"__rc": [null, null]}, -1], ["cr:6969", [], {"__rc": [null, null]}, -1], ["cr:7068", [], {"__rc": [null, null]}, -1], ["cr:7181", ["CometUFIComposerFocusedStatePluginsRow.react"], {"__rc": ["CometUFIComposerFocusedStatePluginsRow.react", null]}, -1], ["cr:14671", ["FDSCautionCircleFilled16PNGIcon.react"], {"__rc": ["FDSCautionCircleFilled16PNGIcon.react", null]}, -1], ["cr:20267", ["useEmptyFunction"], {"__rc": ["useEmptyFunction", null]}, -1], ["cr:20350", ["useFocusedStoryDialogLauncher"], {"__rc": ["useFocusedStoryDialogLauncher", null]}, -1], ["cr:20391", ["BaseContextualLayer.react"], {"__rc": ["BaseContextualLayer.react", null]}, -1], ["cr:921407", ["useNoopDebuggingInfoComponent"], {"__rc": ["useNoopDebuggingInfoComponent", null]}, -1], ["cr:1073372", ["useOnBeforeUnloadBlue"], {"__rc": ["useOnBeforeUnloadBlue", null]}, -1], ["cr:1634616", ["UserActivityBlue"], {"__rc": ["UserActivityBlue", null]}, -1], ["cr:1829844", [], {"__rc": [null, null]}, -1], ["cr:2011405", ["CometDefaultKeyCommands"], {"__rc": ["CometDefaultKeyCommands", null]}, -1], ["cr:2011406", [], {"__rc": [null, null]}, -1], ["cr:5623", [], {"__rc": [null, null]}, -1], ["cr:1077", [], {"__rc": [null, null]}, -1], ["cr:4641", [], {"__rc": [null, null]}, -1], ["cr:4814", [], {"__rc": [null, null]}, -1], ["cr:5863", [], {"__rc": [null, null]}, -1], ["cr:6625", [], {"__rc": [null, null]}, -1], ["cr:13723", ["CometFeedStoryMessageBodyLinkedEntityRenderer"], {"__rc": ["CometFeedStoryMessageBodyLinkedEntityRenderer", null]}, -1], ["cr:14112", ["FDSAppWhatsappOutline20PNGIcon.react"], {"__rc": ["FDSAppWhatsappOutline20PNGIcon.react", null]}, -1], ["cr:14375", ["FDSAppWhatsappFilled12PNGIcon.react"], {"__rc": ["FDSAppWhatsappFilled12PNGIcon.react", null]}, -1], ["cr:14377", ["FDSAppWhatsappOutline24PNGIcon.react"], {"__rc": ["FDSAppWhatsappOutline24PNGIcon.react", null]}, -1], ["cr:14900", ["FDSEyeOutline16PNGIcon.react"], {"__rc": ["FDSEyeOutline16PNGIcon.react", null]}, -1], ["cr:15446", ["FDSPlayOutline16PNGIcon.react"], {"__rc": ["FDSPlayOutline16PNGIcon.react", null]}, -1], ["cr:14882", ["FDSEmojiOutline16PNGIcon.react"], {"__rc": ["FDSEmojiOutline16PNGIcon.react", null]}, -1], ["cr:14951", ["FDSFollowFilled12PNGIcon.react"], {"__rc": ["FDSFollowFilled12PNGIcon.react", null]}, -1], ["nux:55", [], {"__rc": [null, null]}, -1], ["IntlCurrentLocale", [], {"code": "en_GB"}, 5954], ["CometPersistQueryParams", [], {"relative": {}, "domain": {}}, 6231], ["IntlVariationHoldout", [], {"disable_variation": false}, 6533], ["IntlNumberTypeProps", ["IntlCLDRNumberType05"], {"module": {"__m": "IntlCLDRNumberType05"}}, 7027], ["JSSelfProfilerTrackedInteractions", [], {"interactions": [{"action": "*", "tracePolicy": "*"}, {"action": "*", "tracePolicy": "*"}]}, 6918], ["GetAsyncParamsExtraData", [], {"extra_data": {}}, 7511], ["ClickIDURLBlocklistSVConfig", [], {"block_list_url": ["https://www.youtube.com/watch?v=f1J38FlDKxo", "https://www.youtube.com/watch?v=6xt7nTuO85A"]}, 7631], ["AdsManagerReadRegions", [], {"excluded_endpoints": ["/am_tabular", "/ad_limits_insights", "/ads_reporting", "/column_suggestions", "/customaudiences", "/insights", "/reporting", "/edit", "/adspixels"], "excluded_preloaders": ["AdsPEInsightsEdgeDataLoaderPreloader", "AdsPEInsightsEdgeSummaryDataLoaderPreloader", "AdsPEInsightsColumnPresetDataLoaderPreloader", "AdsReportBuilderBusinessViewReportPreloader", "AdsReportBuilderAdAccountViewReportPreloader", "AdsReportBuilderManageUnifiedReportsPreloader"]}, 7950], ["BootloaderConfig", [], {"deferBootloads": false, "enableLoadingUnavailableResources": true, "enableRetryOnStuckResource": false, "immediatesBugFixKillswitch": true, "jsRetries": [200, 500], "jsRetryAbortNum": 2, "jsRetryAbortTime": 5, "silentDups": false, "timeout": 60000, "tieredLoadingFromTier": 100, "hypStep4": false, "phdOn": false, "phdSeparateBitmaps": false, "btCutoffIndex": 1716, "fastPathForAlreadyRequired": true, "earlyRequireLazy": false, "enableTimeoutLoggingForNonComet": true, "deferLongTailManifest": true, "lazySoT": false, "csrOn": false, "nonce": "MQDPJX8z", "translationRetries": [200, 500], "translationRetryAbortNum": 3, "translationRetryAbortTime": 50}, 329], ["CSSLoaderConfig", [], {"timeout": 5000}, 619], ["CurrentUserInitialData", [], {"ACCOUNT_ID": "0", "USER_ID": "0", "NAME": "", "SHORT_NAME": null, "IS_BUSINESS_PERSON_ACCOUNT": false, "HAS_SECONDARY_BUSINESS_PERSON": false, "IS_FACEBOOK_WORK_ACCOUNT": false, "IS_INSTAGRAM_BUSINESS_PERSON": false, "IS_MESSENGER_ONLY_USER": false, "IS_DEACTIVATED_ALLOWED_ON_MESSENGER": false, "IS_MESSENGER_CALL_GUEST_USER": false, "IS_WORK_MESSENGER_CALL_GUEST_USER": false, "IS_WORKROOMS_USER": false, "APP_ID": "************", "IS_BUSINESS_DOMAIN": false}, 270], ["IntlPhonologicalRules", [], {"meta": {"/_B/": "([.,!?\\s]|^)", "/_E/": "([.,!?\\s]|$)"}, "patterns": {"/\u0001(.*)('|&#039;)s\u0001(?:'|&#039;)s(.*)/": "\u0001$1$2s\u0001$3", "/_\u0001([^\u0001]*)\u0001/": "javascript"}}, 1496], ["IntlViewerContext", [], {"GENDER": 3, "regionalLocale": null}, 772], ["LinkshimHandlerConfig", [], {"supports_meta_referrer": false, "default_meta_referrer_policy": "default", "switched_meta_referrer_policy": "origin", "non_linkshim_lnfb_mode": "ie", "link_react_default_hash": "AT0-BaplsbLQ5ReeB6JMnRUbDnX32xGZTCD7qi7cheY4n1cWsYdTY5k4O1Y-Mlo-Yw-AA1cUPSQolFCDnpASTWiu0BvfEvQoST2KIBLTsrWMyeF-IZADQ1eTDNmgFwV-PzTdeK0jyiCyk7wf", "untrusted_link_default_hash": "AT2On8k_zplpLMRkNuudBKGbWy7dJBPxUl0KF4zK1kGqhSU34P5N0QzMwPYYY7VKBPKElo1lgAyeEq-GgMEV5VdEfKcLu3kPK9nZrFl6UnYtj2z85HFAk5rz23Hle9k7YmToRhqZMpwcAHlE", "linkshim_host": "l.facebook.com", "linkshim_path": "/l.php", "linkshim_enc_param": "h", "linkshim_url_param": "u", "use_rel_no_opener": false, "use_rel_no_referrer": false, "always_use_https": false, "onion_always_shim": true, "middle_click_requires_event": false, "www_safe_js_mode": "hover", "m_safe_js_mode": null, "ghl_param_link_shim": false, "click_ids": [], "is_linkshim_supported": true, "current_domain": "facebook.com", "blocklisted_domains": ["ad.doubleclick.net", "ads-encryption-url-example.com", "bs.serving-sys.com", "ad.atdmt.com", "adform.net", "ad13.adfarm1.adition.com", "ilovemyfreedoms.com", "secure.adnxs.com"], "is_mobile_device": false}, 27], ["NumberFormatConfig", [], {"decimalSeparator": ".", "numberDelimiter": ",", "minDigitsForThousandsSeparator": 4, "standardDecimalPatternInfo": {"primaryGroupSize": 3, "secondaryGroupSize": 3}, "numberingSystemData": null}, 54], ["RelayAPIConfigDefaults", [], {"accessToken": "", "actorID": "0", "customHeaders": {}, "enableNetworkLogger": false, "enableVerboseNetworkLogger": false, "fetchTimeout": 30000, "graphURI": "/api/graphql/", "retryDelays": [1000, 3000], "useXController": true, "xhrEncoding": null, "subscriptionTopicURI": null, "withCredentials": false, "isProductionEndpoint": false, "workRequestTaggingProduct": null, "encryptionKeyParams": null, "graphBatchURI": "/api/graphqlbatch/"}, 926], ["ServerNonce", [], {"ServerNonce": "SIzg2fG-lTcN7NSCIKNTdj"}, 141], ["SiteData", [], {"server_revision": 1024716595, "client_revision": 1024713728, "push_phase": "C3", "pkg_cohort": "BP:DEFAULT", "haste_session": "20283.BP:DEFAULT.2.0...0", "pr": 1, "manifest_base_uri": "https://static.xx.fbcdn.net", "manifest_origin": null, "manifest_version_prefix": null, "be_one_ahead": false, "is_rtl": false, "is_experimental_tier": false, "is_jit_warmed_up": false, "hsi": "7526955515560961055", "semr_host_bucket": "5", "bl_hash_version": 2, "comet_env": 0, "wbloks_env": false, "ef_page": "relay_ef:ProfileCometTimelineFeedRefetchQuery", "compose_bootloads": false, "spin": 4, "__spin_r": 1024713728, "__spin_b": "trunk", "__spin_t": 1752505897, "vip": "************"}, 317], ["SprinkleConfig", [], {"param_name": "jazoest", "version": 2, "should_randomize": false}, 2111], ["UserAgentData", [], {"browserArchitecture": "32", "browserFullVersion": null, "browserMinorVersion": null, "browserName": "Unknown", "browserVersion": null, "deviceName": "Unknown", "engineName": "Unknown", "engineVersion": null, "platformArchitecture": "32", "platformName": "Unknown", "platformVersion": null, "platformFullVersion": null}, 527], ["PromiseUsePolyfillSetImmediateGK", [], {"www_always_use_polyfill_setimmediate": false}, 2190], ["JSErrorLoggingConfig", [], {"appId": ************, "extra": [], "reportInterval": 50, "sampleWeight": null, "sampleWeightKey": "__jssesw", "projectBlocklist": []}, 2776], ["ImmediateImplementationExperiments", [], {"prefer_message_channel": true}, 3419], ["FBDomainsSVConfig", [], {"domains": {"__map": [["www.facebook.com", 1], ["tfbnw.net", 1], ["m.beta.facebook.com", 1], ["touch.beta.facebook.com", 1], ["www.dev.facebook.com", 1], ["fb.me", 1], ["s.fb.com", 1], ["m.fbjs.facebook.com", 1], ["facebook.com.es", 1], ["www.fbjs.facebook.com", 1], ["m.facebook.com", 1], ["facebook.fr", 1], ["fbsbx.com", 1], ["embed.fbsbx.com", 1], ["attachment.fbsbx.com", 1], ["lookaside.fbsbx.com", 1], ["web.facebook.com", 1], ["fb.com", 1], ["messenger.com", 1], ["secure.facebook.com", 1], ["secure.my-od.facebook.com", 1], ["www.my-od.facebook.com", 1]]}}, 3828], ["ClickIDDomainBlacklistSVConfig", [], {"domains": ["craigslist", "tfbnw.net", "canadiantire.ca", "o2.co.uk", "archive.org", "reddit.com", "redd.it", "gmail.com", "cvk.gov.ua", "electoralsearch.in", "yahoo.com", "cve.mitre.org", "usenix.org", "ky.gov", "voteohio.gov", "vote.pa.gov", "oversightboard.com", "wi.gov", "pbs.twimg.com", "media.discordapp.net", "vastadeal.com", "theaustralian.com.au", "alloygator.com", "elsmannimmobilien.de", "news.com.au", "dennisbonnen.com", "stoett.com", "investorhour.com", "perspectivasur.com", "bonnegueule.fr", "firstent.org", "twitpic.com", "kollosche.com.au", "nau.edu", "arcourts.gov", "lomberg.de", "network4.hu", "balloonrace.com", "awstrack.me", "ic3.gov", "sos.wyo.gov", "cnpq.br", "0.discoverapp.com", "apple.com", "apple.co", "applecard.apple", "services.apple", "appletvplus.com", "applepay.apple", "wallet.apple", "beatsbydre.com", "dinn.com.mx", "soriana.com", "facebook.sso.datasite.com", "fycextras.com", "rik.parlament.gov.rs", "elections.delaware.gov", "dge.sn", "facebook.co1.qualtrics.com", "instagram.qualtrics.com", "ec.europa.eu", "www.wyoroad.info"]}, 3829], ["UriNeedRawQuerySVConfig", [], {"uris": ["dms.netmng.com", "doubleclick.net", "r.msn.com", "watchit.sky.com", "graphite.instagram.com", "www.kfc.co.th", "learn.pantheon.io", "www.landmarkshops.in", "www.ncl.com", "s0.wp.com", "www.tatacliq.com", "bs.serving-sys.com", "kohls.com", "lazada.co.th", "xg4ken.com", "technopark.ru", "officedepot.com.mx", "bestbuy.com.mx", "booking.com", "nibio.no", "myworkdayjobs.com", "united-united.com", "gcc.gnu.org"]}, 3871], ["WebConnectionClassServerGuess", [], {"connectionClass": "GOOD"}, 4705], ["BootloaderEndpointConfig", [], {"retryEnabled": false, "debugNoBatching": false, "maxBatchSize": -1, "endpointURI": "https://www.facebook.com/ajax/bootloader-endpoint/", "adsManagerReadRegions": false}, 5094], ["ServerTimeData", [], {"serverTime": 1752505897608, "timeOfRequestStart": 1752505897387.9, "timeOfResponseStart": 1752505897387.9}, 5943], ["TransportSelectingClientContextualConfig", [], {"rawConfig": "{\"name\":\"rti/web_rs_transport_selecting_client\",\"cctype\":\"dense\",\"version\":1,\"policy_id\":\"static\",\"sample_rate\":1000,\"contexts\":[{\"name\":\"method\",\"type\":\"STRING\",\"callsite\":true,\"buckets\":[{\"name\":\"rollout_group_1\",\"strategy\":\"in\",\"values\":[\"FBGQLS:FEEDBACK_LIKE_SUBSCRIBE\",\"Falco\",\"FBLQ:comet_notifications_live_query_experimental\"]},{\"name\":\"rollout_group_6\",\"strategy\":\"in\",\"values\":[\"FBGQLS:COMMENT_CREATE_SUBSCRIBE\",\"FBGQLS:COMMENT_LIKE_SUBSCRIBE\",\"FBGQLS:FEEDBACK_COMMENT_PERMISSION_TOGGLE_SUBSCRIBE\",\"FBGQLS:FEEDBACK_TYPING_SUBSCRIBE\"]},{\"name\":\"rollout_group_4\",\"strategy\":\"regex\",\"values\":[\"FBGQLS:.*\"]},{\"name\":\"rollout_group_3\",\"strategy\":\"regex\",\"values\":[\"FBLQ:.*\"]},{\"name\":\"skywalker\",\"strategy\":\"in\",\"values\":[\"SKY:test_topic\",\"live/api/copyright\",\"intern_notify\",\"locplat/ttm\",\"rti_widget_dashboard\",\"srt/user_metrics_counter\",\"media_manager_instagram_composer_create_update\",\"cubism_annotations/fleet_health\",\"srt/notifications\",\"ads/reporting/snapshot\",\"unidash/widget\",\"cubism_annotations\",\"ads/reporting/export\",\"pubx/notification/update\",\"ads/powereditor/import\",\"lwi_async_create\",\"video_edit\",\"metric_graph_realtime\",\"vcc_video_posting_www\",\"cms/object_archive_copy_created\",\"cms/branch_updated\",\"cms/object_saved\",\"codeless_event_tracking\",\"srt/job_updated\",\"video_broadcast\",\"video/broadcast/error\",\"vcpanel/api\",\"lwi_everywhere_plugin\",\"commercial_break_v2\",\"advanced_analytics/query\",\"cubism_annotations/ads_mastercook_models\",\"gqls/comment_like_subscribe\",\"live/api/copyright\",\"shiba/mock_bot_error\",\"shiba/save_state\",\"video_list_publishing_progress_update\",\"assistant_wizard\",\"gizmo/manage\",\"collab/presentation/request\",\"snaptu/push_notif\"]},{\"name\":\"skywalker_bulletin\",\"strategy\":\"in\",\"values\":[\"www/sr/hot_reload\"]},{\"name\":\"rollout_group_5\",\"strategy\":\"regex\",\"values\":[\"Collabri|RealtimeClientSync:.*\"]},{\"name\":\"default\",\"strategy\":\"catch_all\"}]}],\"outputs\":[{\"name\":\"group\",\"type\":\"STRING\"},{\"name\":\"dgwUpsampleMultiplier\",\"type\":\"FLOAT\"}],\"vector\":[\"group1\",\"0.01\",\"group6\",\"0.001\",\"group4\",\"1.0\",\"group3\",\"1.0\",\"skywalker\",\"1.0\",\"skywalker_bulletin\",\"1.0\",\"group5\",\"1.0\",\"default_group\",\"1.0\"],\"vectorDefaults\":[\"default_group\",\"1.0\"],\"timestamp\":1663366072}"}, 5968], ["CookiePrivacySandboxConfig", [], {"is_affected_by_samesite_lax": false}, 7723], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", [], {"domain": "facebook.com"}, 6421], ["RtiWebRequestStreamClient", [], {"ThrottledMethods": {}, "overrideHeaders": {}}, 6639], ["CookieCoreConfig", [], {"alsfid": {}, "c_user": {"t": 31536000}, "cppo": {"t": 86400}, "dpr": {"t": 604800}, "fbl_st": {"t": 31536000}, "hckd": {}, "i_user": {"t": 31536000}, "locale": {"t": 604800}, "m_ls": {"t": 34560000}, "m_pixel_ratio": {"t": 604800}, "noscript": {}, "presence": {"t": 2592000}, "sfau": {}, "usida": {}, "vpd": {"t": 5184000}, "wd": {"t": 604800}, "wl_cbv": {"t": 7776000}, "x-referer": {}, "x-src": {"t": 1}}, 2104], ["ZeroCategoryHeader", [], {}, 1127], ["ZeroRewriteRules", [], {"rewrite_rules": {}, "whitelist": {"/hr/r": 1, "/hr/p": 1, "/zero/unsupported_browser/": 1, "/zero/policy/optin": 1, "/zero/optin/write/": 1, "/zero/optin/legal/": 1, "/zero/optin/free/": 1, "/about/privacy/": 1, "/about/privacy/update/": 1, "/privacy/explanation/": 1, "/zero/toggle/welcome/": 1, "/zero/toggle/nux/": 1, "/zero/toggle/settings/": 1, "/fup/interstitial/": 1, "/work/landing": 1, "/work/login/": 1, "/work/email/": 1, "/ai.php": 1, "/js_dialog_resources/dialog_descriptions_android.json": 0, "/connect/jsdialog/MPlatformAppInvitesJSDialog/": 0, "/connect/jsdialog/MPlatformOAuthShimJSDialog/": 0, "/connect/jsdialog/MPlatformLikeJSDialog/": 0, "/qp/interstitial/": 1, "/qp/action/redirect/": 1, "/qp/action/close/": 1, "/zero/support/ineligible/": 1, "/zero_balance_redirect/": 1, "/zero_balance_redirect": 1, "/zero_balance_redirect/l/": 1, "/l.php": 1, "/lsr.php": 1, "/ajax/dtsg/": 1, "/checkpoint/block/": 1, "/exitdsite": 1, "/zero/balance/pixel/": 1, "/zero/balance/": 1, "/zero/balance/carrier_landing/": 1, "/zero/flex/logging/": 1, "/tr": 1, "/tr/": 1, "/sem_campaigns/sem_pixel_test/": 1, "/bookmarks/flyout/body/": 1, "/zero/subno/": 1, "/confirmemail.php": 1, "/policies/": 1, "/mobile/internetdotorg/classifier/": 1, "/zero/dogfooding": 1, "/xti.php": 1, "/zero/fblite/config/": 1, "/hr/zsh/wc/": 1, "/ajax/bootloader-endpoint/": 1, "/mobile/zero/carrier_page/": 1, "/mobile/zero/carrier_page/education_page/": 1, "/mobile/zero/carrier_page/feature_switch/": 1, "/mobile/zero/carrier_page/settings_page/": 1, "/aloha_check_build": 1, "/upsell/zbd/softnudge/": 1, "/mobile/zero/af_transition/": 1, "/mobile/zero/af_transition/action/": 1, "/mobile/zero/freemium/": 1, "/mobile/zero/freemium/redirect/": 1, "/mobile/zero/freemium/zero_fup/": 1, "/privacy/policy/": 1, "/privacy/center/": 1, "/data/manifest/": 1, "/cmon": 1, "/cmon/": 1, "/zero/minidt/": 1, "/diagnostics": 1, "/diagnostics/": 1, "/4oh4.php": 1, "/autologin.php": 1, "/birthday_help.php": 1, "/checkpoint/": 1, "/contact-importer/": 1, "/cr.php": 1, "/legal/terms/": 1, "/login.php": 1, "/login/": 1, "/mobile/account/": 1, "/n/": 1, "/remote_test_device/": 1, "/upsell/buy/": 1, "/upsell/buyconfirm/": 1, "/upsell/buyresult/": 1, "/upsell/promos/": 1, "/upsell/continue/": 1, "/upsell/h/promos/": 1, "/upsell/loan/learnmore/": 1, "/upsell/purchase/": 1, "/upsell/promos/upgrade/": 1, "/upsell/buy_redirect/": 1, "/upsell/loan/buyconfirm/": 1, "/upsell/loan/buy/": 1, "/upsell/sms/": 1, "/wap/a/channel/reconnect.php": 1, "/wap/a/nux/wizard/nav.php": 1, "/wap/appreg.php": 1, "/wap/birthday_help.php": 1, "/wap/c.php": 1, "/wap/confirmemail.php": 1, "/wap/cr.php": 1, "/wap/login.php": 1, "/wap/r.php": 1, "/zero/datapolicy": 1, "/a/timezone.php": 1, "/a/bz": 1, "/bz/reliability": 1, "/r.php": 1, "/mr/": 1, "/reg/": 1, "/registration/log/": 1, "/terms/": 1, "/f123/": 1, "/expert/": 1, "/experts/": 1, "/terms/index.php": 1, "/terms.php": 1, "/srr/": 1, "/msite/redirect/": 1, "/fbs/pixel/": 1, "/contactpoint/preconfirmation/": 1, "/contactpoint/cliff/": 1, "/contactpoint/confirm/submit/": 1, "/contactpoint/confirmed/": 1, "/contactpoint/login/": 1, "/preconfirmation/contactpoint_change/": 1, "/help/contact/": 1, "/survey/": 1, "/upsell/loyaltytopup/accept/": 1, "/settings/": 1, "/lite/": 1, "/zero_status_update/": 1, "/operator_store/": 1, "/upsell/": 1, "/wifiauth/login/": 1}}, 1478], ["CookieCoreLoggingConfig", [], {"maximumIgnorableStallMs": 16.67, "sampleRate": 9.7e-05, "sampleRateClassic": 1e-10, "sampleRateFastStale": 1e-08}, 3401], ["CometCustomKeyCommands", [], {"customCommands": {}, "areSingleKeysDisabled": null, "modifiedKeyboardShortcutsPreference": 4}, 4521], ["CometRelayConfig", [], {"gc_release_buffer_size": 50}, 4685], ["WebDriverConfig", [], {"isTestRunning": false, "isJestE2ETestRun": false, "isXRequestConfigEnabled": false, "auxiliaryServiceInfo": {}, "testPath": null, "originHost": null, "experiments": null}, 5332], ["CometMaxEnqueuedToastsSitevarConfig", [], {"max": 2}, 4763], ["EmojiConfig", [], {"pixelRatio": "1", "schemaAuth": "https://static.xx.fbcdn.net/images/emoji.php/v9", "hasEmojiPickerSearch": false}, 1421], ["DateFormatConfig", [], {"numericDateOrder": ["d", "m", "y"], "numericDateSeparator": "/", "shortDayNames": ["Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat", "Sun"], "narrowDayNames": ["M", "T", "W", "T", "F", "S", "S"], "timeSeparator": ":", "weekStart": 6, "formats": {"D": "D", "D g:ia": "D H:i", "D M d": "j F", "D M d, Y": "l, j F Y", "D M j": "<PERSON>, j <PERSON>", "D M j, g:ia": "j F H:i", "D M j, y": "l, j F Y", "D M j, Y g:ia": "l, j F Y H:i", "D, M j, Y": "l, j F Y", "F d": "j F", "F d, Y": "j F Y", "F g": "j F", "F j": "j F", "F j, Y": "j F Y", "F j, Y @ g:i A": "j F Y H:i", "F j, Y g:i a": "j F Y H:i", "F jS": "j F", "F jS, g:ia": "j F H:i", "F jS, Y": "j F Y", "F Y": "F Y", "g A": "H", "g:i": "H:i", "g:i A": "H:i", "g:i a": "H:i", "g:iA": "H:i", "g:ia": "H:i", "g:ia F jS, Y": "j F Y H:i", "g:iA l, F jS": "l, j F Y H:i", "g:ia M j": "j F H:i", "g:ia M jS": "j F H:i", "g:ia, F jS": "j F H:i", "g:iA, l M jS": "l, j F Y H:i", "g:sa": "H:i", "H:I - M d, Y": "j F Y H:i", "h:i a": "H:i", "h:m:s m/d/Y": "d/m/Y H:i:s", "j": "j", "l F d, Y": "l, j F Y", "l g:ia": "l H:i", "l, F d, Y": "l, j F Y", "l, F j": "j F", "l, F j, Y": "l, j F Y", "l, F jS": "j F", "l, F jS, g:ia": "l, j F Y H:i", "l, M j": "j F", "l, M j, Y": "l, j F Y", "l, M j, Y g:ia": "l, j F Y H:i", "M d": "j F", "M d, Y": "j F Y", "M d, Y g:ia": "j F Y H:i", "M d, Y ga": "j F Y H", "M j": "j F", "M j, Y": "j F Y", "M j, Y g:i A": "j F Y H:i", "M j, Y g:ia": "j F Y H:i", "M jS, g:ia": "j F H:i", "M Y": "F Y", "M y": "j F", "m-d-y": "d/m/Y", "M. d": "j F", "M. d, Y": "j F Y", "j F Y": "j F Y", "m.d.y": "d/m/Y", "m/d": "d/m", "m/d/Y": "d/m/Y", "m/d/y": "d/m/Y", "m/d/Y g:ia": "d/m/Y H:i", "m/d/y H:i:s": "d/m/Y H:i:s", "m/d/Y h:m": "d/m/Y H:i:s", "n": "d/m", "n/j": "d/m", "n/j, g:ia": "d/m/Y H:i", "n/j/y": "d/m/Y", "Y": "Y", "Y-m-d": "d/m/Y", "Y/m/d": "d/m/Y", "y/m/d": "d/m/Y", "j / F / Y": "j / F / Y"}, "ordinalSuffixes": {"1": "st", "2": "nd", "3": "rd", "4": "th", "5": "th", "6": "th", "7": "th", "8": "th", "9": "th", "10": "th", "11": "th", "12": "th", "13": "th", "14": "th", "15": "th", "16": "th", "17": "th", "18": "th", "19": "th", "20": "th", "21": "st", "22": "nd", "23": "rd", "24": "th", "25": "th", "26": "th", "27": "th", "28": "th", "29": "th", "30": "th", "31": "st"}}, 165], ["UFI2Config", [], {"sessionID": "333571d8-298f-4c5c-a73b-17f437936384", "composerInputSamplingRateDenominator": 100, "legacyReplyFetchShortfallSimulationFactor": 1, "legacyTopLevelFetchShortfallSimulationFactor": 1, "serverRenderedAttachmentStyles": ["event", "file_upload", "music_aggregation", "tool_share"], "shouldUseSimplifiedAttachments": true, "attachmentStylesToSimplify": ["event", "file_upload", "music_aggregation", "tool_share"], "shouldShowWritingHelp": false}, 3350], ["CLDRDateFormatConfig", [], {"supportedPHPFormatsKeys": {"D": "E", "D g:ia": "Ejm", "D M d": "MMMEd", "D M d, Y": "yMMMEd", "D M j": "MMMEd", "D M j, y": "yMMMEd", "D, M j": "MMMEd", "D, M j, Y": "yMMMEd", "F d": "MMMMd", "F d, Y": "date_long", "F j": "MMMMd", "F j, Y": "date_long", "F j, Y @ g:i A": "dateTime_long_short", "F j, Y g:i a": "dateTime_long_short", "F j, Y @ g:i:s A": "dateTime_long_medium", "F jS": "MMMMd", "F jS, g:ia": "dateTime_long_short", "F jS, Y": "date_long", "F Y": "yMMMM", "g A": "j", "G:i": "time_short", "g:i": "time_short", "g:i a": "time_short", "g:i A": "time_short", "g:i:s A": "time_medium", "g:ia": "time_short", "g:iA": "time_short", "g:ia F jS, Y": "dateTime_long_short", "g:iA l, F jS": "dateTime_full_short", "g:ia M jS": "dateTime_medium_short", "g:ia, F jS": "dateTime_long_short", "g:iA, l M jS": "dateTime_full_short", "h:i a": "time_short", "h:m:s m/d/Y": "dateTime_short_short", "j": "d", "j F Y": "date_long", "l F d, Y": "date_full", "l, F d, Y": "date_full", "l, F j": "date_full", "l, F j, Y": "date_full", "l, F jS": "date_full", "l, F jS, g:ia": "dateTime_full_short", "l, M j": "date_full", "l, M j, Y": "date_full", "l, M j, Y g:ia": "dateTime_full_short", "M d": "MMMd", "M d, Y": "date_medium", "M d, Y g:ia": "dateTime_medium_short", "M d, Y ga": "dateTime_medium_short", "M j": "MMMd", "M j, Y": "date_medium", "M j, Y g:i A": "dateTime_medium_short", "M j, Y g:ia": "dateTime_medium_short", "M jS, g:ia": "dateTime_medium_short", "M y": "yMMM", "M Y": "yMMM", "M. d": "MMMd", "M. d, Y": "date_medium", "m/d": "Md", "m/d/Y g:ia": "dateTime_short_short", "m/d/y H:i:s": "dateTime_short_short", "n": "M", "n/j": "Md", "n/j, g:ia": "dateTime_short_short", "n/j/y": "date_short", "Y": "y"}, "isLocaleInConfigerator": true, "CLDRConfigeratorFormats": {"dateFormats": {"full": "EEEE d MMMM y", "long": "d MMMM y", "medium": "d MMM y", "short": "dd/MM/y"}, "timeFormats": {"full": "HH:mm:ss zzzz", "long": "HH:mm:ss z", "medium": "HH:mm:ss", "short": "HH:mm"}, "dateTimeFormats": {"full": "{1}, {0}", "long": "{1}, {0}", "medium": "{1}, {0}", "short": "{1}, {0}"}, "availableFormats": {"Bh": "h B", "Bhm": "h:mm B", "Bhms": "h:mm:ss B", "E": "ccc", "EBhm": "E h:mm B", "EBhms": "E h:mm:ss B", "EHm": "E HH:mm", "EHms": "E HH:mm:ss", "Ed": "E d", "Ehm": "E h:mm a", "Ehm-alt-ascii": "E h:mm a", "Ehms": "E h:mm:ss a", "Ehms-alt-ascii": "E h:mm:ss a", "Gy": "y G", "GyMMM": "MMM y G", "GyMMMEEEEd": "EEEE d MMM y G", "GyMMMEd": "<PERSON>, d MMM y G", "GyMMMd": "d MMM y G", "GyMd": "dd/MM/y G", "H": "HH", "Hm": "HH:mm", "Hms": "HH:mm:ss", "Hmsv": "HH:mm:ss v", "Hmv": "HH:mm v", "M": "L", "MEd": "E, dd/MM", "MMM": "LLL", "MMMEEEEd": "EEEE d MMM", "MMMEd": "<PERSON>, d <PERSON><PERSON>", "MMMMEEEEd": "EEEE d MMMM", "MMMMW-count-one": "'week' W 'of' MMMM", "MMMMW-count-other": "'week' W 'of' MMMM", "MMMMd": "d MMMM", "MMMd": "d <PERSON><PERSON>", "MMdd": "dd/MM", "Md": "dd/MM", "d": "d", "h": "h a", "h-alt-ascii": "h a", "hm": "h:mm a", "hm-alt-ascii": "h:mm a", "hms": "h:mm:ss a", "hms-alt-ascii": "h:mm:ss a", "hmsv": "h:mm:ss a v", "hmsv-alt-ascii": "h:mm:ss a v", "hmv": "h:mm a v", "hmv-alt-ascii": "h:mm a v", "ms": "mm:ss", "y": "y", "yM": "MM/y", "yMEd": "E, dd/MM/y", "yMMM": "MMM y", "yMMMEEEEd": "EEEE d MMM y", "yMMMEd": "<PERSON>, d <PERSON><PERSON> y", "yMMMM": "MMMM y", "yMMMMEEEEd": "EEEE d MMMM y", "yMMMd": "d MMM y", "yMd": "dd/MM/y", "yQQQ": "QQQ y", "yQQQQ": "QQQQ y", "yw-count-one": "'week' w 'of' Y", "yw-count-other": "'week' w 'of' Y"}}, "CLDRRegionalConfigeratorFormats": {"dateFormats": {"full": "EEEE d MMMM y", "long": "d MMMM y", "medium": "d MMM y", "short": "dd/MM/y"}, "timeFormats": {"full": "HH:mm:ss zzzz", "long": "HH:mm:ss z", "medium": "HH:mm:ss", "short": "HH:mm"}, "dateTimeFormats": {"full": "{1}, {0}", "long": "{1}, {0}", "medium": "{1}, {0}", "short": "{1}, {0}"}, "availableFormats": {"Bh": "h B", "Bhm": "h:mm B", "Bhms": "h:mm:ss B", "E": "ccc", "EBhm": "E h:mm B", "EBhms": "E h:mm:ss B", "EHm": "E HH:mm", "EHms": "E HH:mm:ss", "Ed": "E d", "Ehm": "E h:mm a", "Ehm-alt-ascii": "E h:mm a", "Ehms": "E h:mm:ss a", "Ehms-alt-ascii": "E h:mm:ss a", "Gy": "y G", "GyMMM": "MMM y G", "GyMMMEEEEd": "EEEE d MMM y G", "GyMMMEd": "<PERSON>, d MMM y G", "GyMMMd": "d MMM y G", "GyMd": "dd/MM/y G", "H": "HH", "Hm": "HH:mm", "Hms": "HH:mm:ss", "Hmsv": "HH:mm:ss v", "Hmv": "HH:mm v", "M": "L", "MEd": "E, dd/MM", "MMM": "LLL", "MMMEEEEd": "EEEE d MMM", "MMMEd": "<PERSON>, d <PERSON><PERSON>", "MMMMEEEEd": "EEEE d MMMM", "MMMMW-count-one": "'week' W 'of' MMMM", "MMMMW-count-other": "'week' W 'of' MMMM", "MMMMd": "d MMMM", "MMMd": "d <PERSON><PERSON>", "MMdd": "dd/MM", "Md": "dd/MM", "d": "d", "h": "h a", "h-alt-ascii": "h a", "hm": "h:mm a", "hm-alt-ascii": "h:mm a", "hms": "h:mm:ss a", "hms-alt-ascii": "h:mm:ss a", "hmsv": "h:mm:ss a v", "hmsv-alt-ascii": "h:mm:ss a v", "hmv": "h:mm a v", "hmv-alt-ascii": "h:mm a v", "ms": "mm:ss", "y": "y", "yM": "MM/y", "yMEd": "E, dd/MM/y", "yMMM": "MMM y", "yMMMEEEEd": "EEEE d MMM y", "yMMMEd": "<PERSON>, d <PERSON><PERSON> y", "yMMMM": "MMMM y", "yMMMMEEEEd": "EEEE d MMMM y", "yMMMd": "d MMM y", "yMd": "dd/MM/y", "yQQQ": "QQQ y", "yQQQQ": "QQQQ y", "yw-count-one": "'week' w 'of' Y", "yw-count-other": "'week' w 'of' Y"}}, "CLDRToPHPSymbolConversion": {"G": "", "yyyy": "Y", "yy": "y", "y": "Y", "LLLLL": "M", "LLLL": "f", "LLL": "M", "LL": "m", "L": "n", "MMMMM": "M", "MMMM": "F", "MMM": "M", "MM": "m", "M": "n", "dd": "d", "d": "j", "ccccc": "D", "cccc": "l", "ccc": "D", "cc": "D", "c": "D", "EEEEE": "D", "EEEE": "l", "EEE": "D", "EE": "D", "E": "D", "aaaaa": "A", "aaaa": "A", "aaa": "A", "aa": "A", "a": "A", "bbbbb": "A", "bbbb": "A", "bbb": "A", "bb": "A", "b": "A", "BBBBB": "A", "BBBB": "A", "BBB": "A", "BB": "A", "B": "A", "HH": "H", "H": "G", "hh": "h", "h": "g", "K": "g", "mm": "i", "m": "i", "ss": "s", "s": "s", "z": "", "zz": "", "zzz": ""}, "intlDateSpecialChars": {"cldrDelimiter": "'", "singleQuote": "'", "singleQuoteHolder": "*"}}, 3019], ["IsInternSite", [], {"is_intern_site": false}, 4517], ["MqttWebDeviceID", [], {"clientID": "b65b1c7b-cd79-403b-bc43-fa2053627eaf"}, 5003], ["IntlCompactDecimalNumberFormatConfig", [], {"short_patterns": {"3": {"4": {"min_fraction_digits": null, "min_integer_digits": 1, "positive_prefix_pattern": "", "positive_suffix_pattern": "K", "negative_prefix_pattern": "-", "negative_suffix_pattern": "K"}, "24": {"min_fraction_digits": null, "min_integer_digits": 1, "positive_prefix_pattern": "", "positive_suffix_pattern": "K", "negative_prefix_pattern": "-", "negative_suffix_pattern": "K"}}, "4": {"4": {"min_fraction_digits": null, "min_integer_digits": 2, "positive_prefix_pattern": "", "positive_suffix_pattern": "K", "negative_prefix_pattern": "-", "negative_suffix_pattern": "K"}, "24": {"min_fraction_digits": null, "min_integer_digits": 2, "positive_prefix_pattern": "", "positive_suffix_pattern": "K", "negative_prefix_pattern": "-", "negative_suffix_pattern": "K"}}, "5": {"4": {"min_fraction_digits": null, "min_integer_digits": 3, "positive_prefix_pattern": "", "positive_suffix_pattern": "K", "negative_prefix_pattern": "-", "negative_suffix_pattern": "K"}, "24": {"min_fraction_digits": null, "min_integer_digits": 3, "positive_prefix_pattern": "", "positive_suffix_pattern": "K", "negative_prefix_pattern": "-", "negative_suffix_pattern": "K"}}, "6": {"4": {"min_fraction_digits": null, "min_integer_digits": 1, "positive_prefix_pattern": "", "positive_suffix_pattern": "M", "negative_prefix_pattern": "-", "negative_suffix_pattern": "M"}, "24": {"min_fraction_digits": null, "min_integer_digits": 1, "positive_prefix_pattern": "", "positive_suffix_pattern": "M", "negative_prefix_pattern": "-", "negative_suffix_pattern": "M"}}, "7": {"4": {"min_fraction_digits": null, "min_integer_digits": 2, "positive_prefix_pattern": "", "positive_suffix_pattern": "M", "negative_prefix_pattern": "-", "negative_suffix_pattern": "M"}, "24": {"min_fraction_digits": null, "min_integer_digits": 2, "positive_prefix_pattern": "", "positive_suffix_pattern": "M", "negative_prefix_pattern": "-", "negative_suffix_pattern": "M"}}, "8": {"4": {"min_fraction_digits": null, "min_integer_digits": 3, "positive_prefix_pattern": "", "positive_suffix_pattern": "M", "negative_prefix_pattern": "-", "negative_suffix_pattern": "M"}, "24": {"min_fraction_digits": null, "min_integer_digits": 3, "positive_prefix_pattern": "", "positive_suffix_pattern": "M", "negative_prefix_pattern": "-", "negative_suffix_pattern": "M"}}, "9": {"4": {"min_fraction_digits": null, "min_integer_digits": 1, "positive_prefix_pattern": "", "positive_suffix_pattern": "B", "negative_prefix_pattern": "-", "negative_suffix_pattern": "B"}, "24": {"min_fraction_digits": null, "min_integer_digits": 1, "positive_prefix_pattern": "", "positive_suffix_pattern": "B", "negative_prefix_pattern": "-", "negative_suffix_pattern": "B"}}, "10": {"4": {"min_fraction_digits": null, "min_integer_digits": 2, "positive_prefix_pattern": "", "positive_suffix_pattern": "B", "negative_prefix_pattern": "-", "negative_suffix_pattern": "B"}, "24": {"min_fraction_digits": null, "min_integer_digits": 2, "positive_prefix_pattern": "", "positive_suffix_pattern": "B", "negative_prefix_pattern": "-", "negative_suffix_pattern": "B"}}, "11": {"4": {"min_fraction_digits": null, "min_integer_digits": 3, "positive_prefix_pattern": "", "positive_suffix_pattern": "B", "negative_prefix_pattern": "-", "negative_suffix_pattern": "B"}, "24": {"min_fraction_digits": null, "min_integer_digits": 3, "positive_prefix_pattern": "", "positive_suffix_pattern": "B", "negative_prefix_pattern": "-", "negative_suffix_pattern": "B"}}, "12": {"4": {"min_fraction_digits": null, "min_integer_digits": 1, "positive_prefix_pattern": "", "positive_suffix_pattern": "T", "negative_prefix_pattern": "-", "negative_suffix_pattern": "T"}, "24": {"min_fraction_digits": null, "min_integer_digits": 1, "positive_prefix_pattern": "", "positive_suffix_pattern": "T", "negative_prefix_pattern": "-", "negative_suffix_pattern": "T"}}, "13": {"4": {"min_fraction_digits": null, "min_integer_digits": 2, "positive_prefix_pattern": "", "positive_suffix_pattern": "T", "negative_prefix_pattern": "-", "negative_suffix_pattern": "T"}, "24": {"min_fraction_digits": null, "min_integer_digits": 2, "positive_prefix_pattern": "", "positive_suffix_pattern": "T", "negative_prefix_pattern": "-", "negative_suffix_pattern": "T"}}, "14": {"4": {"min_fraction_digits": null, "min_integer_digits": 3, "positive_prefix_pattern": "", "positive_suffix_pattern": "T", "negative_prefix_pattern": "-", "negative_suffix_pattern": "T"}, "24": {"min_fraction_digits": null, "min_integer_digits": 3, "positive_prefix_pattern": "", "positive_suffix_pattern": "T", "negative_prefix_pattern": "-", "negative_suffix_pattern": "T"}}}, "long_patterns": {"3": {"4": {"min_fraction_digits": null, "min_integer_digits": 1, "positive_prefix_pattern": "", "positive_suffix_pattern": " thousand", "negative_prefix_pattern": "-", "negative_suffix_pattern": " thousand"}, "24": {"min_fraction_digits": null, "min_integer_digits": 1, "positive_prefix_pattern": "", "positive_suffix_pattern": " thousand", "negative_prefix_pattern": "-", "negative_suffix_pattern": " thousand"}}, "4": {"4": {"min_fraction_digits": null, "min_integer_digits": 2, "positive_prefix_pattern": "", "positive_suffix_pattern": " thousand", "negative_prefix_pattern": "-", "negative_suffix_pattern": " thousand"}, "24": {"min_fraction_digits": null, "min_integer_digits": 2, "positive_prefix_pattern": "", "positive_suffix_pattern": " thousand", "negative_prefix_pattern": "-", "negative_suffix_pattern": " thousand"}}, "5": {"4": {"min_fraction_digits": null, "min_integer_digits": 3, "positive_prefix_pattern": "", "positive_suffix_pattern": " thousand", "negative_prefix_pattern": "-", "negative_suffix_pattern": " thousand"}, "24": {"min_fraction_digits": null, "min_integer_digits": 3, "positive_prefix_pattern": "", "positive_suffix_pattern": " thousand", "negative_prefix_pattern": "-", "negative_suffix_pattern": " thousand"}}, "6": {"4": {"min_fraction_digits": null, "min_integer_digits": 1, "positive_prefix_pattern": "", "positive_suffix_pattern": " million", "negative_prefix_pattern": "-", "negative_suffix_pattern": " million"}, "24": {"min_fraction_digits": null, "min_integer_digits": 1, "positive_prefix_pattern": "", "positive_suffix_pattern": " million", "negative_prefix_pattern": "-", "negative_suffix_pattern": " million"}}, "7": {"4": {"min_fraction_digits": null, "min_integer_digits": 2, "positive_prefix_pattern": "", "positive_suffix_pattern": " million", "negative_prefix_pattern": "-", "negative_suffix_pattern": " million"}, "24": {"min_fraction_digits": null, "min_integer_digits": 2, "positive_prefix_pattern": "", "positive_suffix_pattern": " million", "negative_prefix_pattern": "-", "negative_suffix_pattern": " million"}}, "8": {"4": {"min_fraction_digits": null, "min_integer_digits": 3, "positive_prefix_pattern": "", "positive_suffix_pattern": " million", "negative_prefix_pattern": "-", "negative_suffix_pattern": " million"}, "24": {"min_fraction_digits": null, "min_integer_digits": 3, "positive_prefix_pattern": "", "positive_suffix_pattern": " million", "negative_prefix_pattern": "-", "negative_suffix_pattern": " million"}}, "9": {"4": {"min_fraction_digits": null, "min_integer_digits": 1, "positive_prefix_pattern": "", "positive_suffix_pattern": " billion", "negative_prefix_pattern": "-", "negative_suffix_pattern": " billion"}, "24": {"min_fraction_digits": null, "min_integer_digits": 1, "positive_prefix_pattern": "", "positive_suffix_pattern": " billion", "negative_prefix_pattern": "-", "negative_suffix_pattern": " billion"}}, "10": {"4": {"min_fraction_digits": null, "min_integer_digits": 2, "positive_prefix_pattern": "", "positive_suffix_pattern": " billion", "negative_prefix_pattern": "-", "negative_suffix_pattern": " billion"}, "24": {"min_fraction_digits": null, "min_integer_digits": 2, "positive_prefix_pattern": "", "positive_suffix_pattern": " billion", "negative_prefix_pattern": "-", "negative_suffix_pattern": " billion"}}, "11": {"4": {"min_fraction_digits": null, "min_integer_digits": 3, "positive_prefix_pattern": "", "positive_suffix_pattern": " billion", "negative_prefix_pattern": "-", "negative_suffix_pattern": " billion"}, "24": {"min_fraction_digits": null, "min_integer_digits": 3, "positive_prefix_pattern": "", "positive_suffix_pattern": " billion", "negative_prefix_pattern": "-", "negative_suffix_pattern": " billion"}}, "12": {"4": {"min_fraction_digits": null, "min_integer_digits": 1, "positive_prefix_pattern": "", "positive_suffix_pattern": " trillion", "negative_prefix_pattern": "-", "negative_suffix_pattern": " trillion"}, "24": {"min_fraction_digits": null, "min_integer_digits": 1, "positive_prefix_pattern": "", "positive_suffix_pattern": " trillion", "negative_prefix_pattern": "-", "negative_suffix_pattern": " trillion"}}, "13": {"4": {"min_fraction_digits": null, "min_integer_digits": 2, "positive_prefix_pattern": "", "positive_suffix_pattern": " trillion", "negative_prefix_pattern": "-", "negative_suffix_pattern": " trillion"}, "24": {"min_fraction_digits": null, "min_integer_digits": 2, "positive_prefix_pattern": "", "positive_suffix_pattern": " trillion", "negative_prefix_pattern": "-", "negative_suffix_pattern": " trillion"}}, "14": {"4": {"min_fraction_digits": null, "min_integer_digits": 3, "positive_prefix_pattern": "", "positive_suffix_pattern": " trillion", "negative_prefix_pattern": "-", "negative_suffix_pattern": " trillion"}, "24": {"min_fraction_digits": null, "min_integer_digits": 3, "positive_prefix_pattern": "", "positive_suffix_pattern": " trillion", "negative_prefix_pattern": "-", "negative_suffix_pattern": " trillion"}}}}, 2981], ["cr:334", ["ghlTestUBTFacebook"], {"__rc": ["ghlTestUBTFacebook", null]}, -1], ["cr:1543261", [], {"__rc": [null, null]}, -1], ["cr:8828", [], {"__rc": [null, null]}, -1], ["cr:1094907", [], {"__rc": [null, null]}, -1], ["cr:6943", ["EventListenerImplForCacheStorage"], {"__rc": ["EventListenerImplForCacheStorage", null]}, -1], ["cr:3024", [], {"__rc": [null, null]}, -1], ["cr:2046346", [], {"__rc": [null, null]}, -1], ["cr:23105", [], {"__rc": [null, null]}, -1], ["cr:9610", [], {"__rc": [null, null]}, -1], ["cr:308", [], {"__rc": [null, null]}, -1], ["cr:3286", [], {"__rc": [null, null]}, -1], ["cr:3287", [], {"__rc": [null, null]}, -1], ["cr:2012305", [], {"__rc": [null, null]}, -1], ["cr:13978", ["FDSChevronLeftFilled16PNGIcon.react"], {"__rc": ["FDSChevronLeftFilled16PNGIcon.react", null]}, -1], ["cr:13982", ["FDSChevronRightFilled16PNGIcon.react"], {"__rc": ["FDSChevronRightFilled16PNGIcon.react", null]}, -1], ["cr:15667", ["FDSShopsFilled20PNGIcon.react"], {"__rc": ["FDSShopsFilled20PNGIcon.react", null]}, -1], ["cr:23096", ["useFeedComposerCometMentionsNetworkDataSource"], {"__rc": ["useFeedComposerCometMentionsNetworkDataSource", null]}, -1], ["cr:23097", ["useFeedComposerCometMentionsNullstateDataSource"], {"__rc": ["useFeedComposerCometMentionsNullstateDataSource", null]}, -1], ["cr:1094133", [], {"__rc": [null, null]}, -1], ["cr:2682", ["warningBlueish"], {"__rc": ["warningBlueish", null]}, -1], ["cr:11202", [], {"__rc": [null, null]}, -1], ["cr:1105154", [], {"__rc": [null, null]}, -1], ["cr:5277", ["ReactDOM.classic.prod-or-profiling"], {"__rc": ["ReactDOM.classic.prod-or-profiling", null]}, -1], ["cr:13683", [], {"__rc": [null, null]}, -1], ["cr:7386", ["clearTimeoutWWW"], {"__rc": ["clearTimeoutWWW", null]}, -1], ["cr:7390", ["setTimeoutWWW"], {"__rc": ["setTimeoutWWW", null]}, -1], ["cr:1003267", ["clearIntervalBlue"], {"__rc": ["clearIntervalBlue", null]}, -1], ["cr:896461", ["setIntervalBlue"], {"__rc": ["setIntervalBlue", null]}, -1], ["cr:896462", ["setIntervalAcrossTransitionsBlue"], {"__rc": ["setIntervalAcrossTransitionsBlue", null]}, -1], ["cr:986633", ["setTimeoutAcrossTransitionsBlue"], {"__rc": ["setTimeoutAcrossTransitionsBlue", null]}, -1], ["cr:1183579", ["InlineFbtResultImpl"], {"__rc": ["InlineFbtResultImpl", null]}, -1], ["cr:3798", [], {"__rc": [null, null]}, -1], ["cr:1292365", ["React-prod.classic"], {"__rc": ["React-prod.classic", null]}, -1], ["cr:355", [], {"__rc": [null, null]}, -1], ["cr:506", [], {"__rc": [null, null]}, -1], ["cr:1020", [], {"__rc": [null, null]}, -1], ["cr:2336", [], {"__rc": [null, null]}, -1], ["cr:2887", ["VideoPlayerNextgendashWorkerEnvironmentImportUnconditionally"], {"__rc": ["VideoPlayerNextgendashWorkerEnvironmentImportUnconditionally", null]}, -1], ["cr:4596", [], {"__rc": [null, null]}, -1], ["cr:5058", [], {"__rc": [null, null]}, -1], ["cr:6193", [], {"__rc": [null, null]}, -1], ["cr:6632", [], {"__rc": [null, null]}, -1], ["cr:8971", [], {"__rc": [null, null]}, -1], ["cr:8972", [], {"__rc": [null, null]}, -1], ["cr:8973", [], {"__rc": [null, null]}, -1], ["cr:1724253", [], {"__rc": [null, null]}, -1], ["cr:1642797", ["BanzaiBase"], {"__rc": ["BanzaiBase", null]}, -1], ["cr:708886", ["EventProfilerImpl"], {"__rc": ["EventProfilerImpl", null]}, -1], ["cr:2286", [], {"__rc": [null, null]}, -1], ["cr:1207300", [], {"__rc": [null, null]}, -1], ["cr:1761713", ["CometUFIShareActionLinkLabel"], {"__rc": ["CometUFIShareActionLinkLabel", null]}, -1], ["cr:2904", ["LexicalHistory.prod"], {"__rc": ["LexicalHistory.prod", null]}, -1], ["cr:2903", ["LexicalDragon.prod"], {"__rc": ["LexicalDragon.prod", null]}, -1], ["cr:2909", ["LexicalPlainText.prod"], {"__rc": ["LexicalPlainText.prod", null]}, -1], ["cr:4793", ["useLexicalEditable.prod"], {"__rc": ["useLexicalEditable.prod", null]}, -1], ["cr:2180", ["LexicalSelection.prod"], {"__rc": ["LexicalSelection.prod", null]}, -1], ["cr:1798298", [], {"__rc": [null, null]}, -1], ["cr:15630", ["FDSSendFilled16PNGIcon.react"], {"__rc": ["FDSSendFilled16PNGIcon.react", null]}, -1], ["cr:19850", [], {"__rc": [null, null]}, -1], ["MarauderConfig", [], {"app_version": "******* (1024716595)", "gk_enabled": false}, 31], ["CurrentEnvironment", [], {"facebookdotcom": true, "messengerdotcom": false, "workplacedotcom": false, "instagramdotcom": false, "workdotmetadotcom": false, "horizondotmetadotcom": false}, 827], ["RTISubscriptionManagerConfig", [], {"config": {}, "autobot": {}, "assimilator": {}, "unsubscribe_release": true, "bladerunner_www_sandbox": null, "is_intern": false}, 1081], ["MqttWebConfig", [], {"fbid": "0", "appID": 219994525426954, "endpoint": "wss://edge-chat.facebook.com/chat", "pollingEndpoint": "https://edge-chat.facebook.com/mqtt/pull", "subscribedTopics": [], "capabilities": 10, "clientCapabilities": 3, "chatVisibility": false, "hostNameOverride": ""}, 3790], ["RequestStreamE2EClientSamplingConfig", [], {"sampleRate": 500000, "methodToSamplingMultiplier": {"RTCSessionMessage": 10000, "Presence": 0.01, "FBGQLS:VOD_TICKER_SUBSCRIBE": 0.01, "FBGQLS:STORIES_TRAY_SUBSCRIBE": 100, "Collabri": 0.1, "FBGQLS:WORK_AVAILABILITY_STATUS_FANOUT_SUBSCRIBE": 0.1, "FBGQLS:GROUP_UNSEEN_ACTIVITY_SUBSCRIBE": 0.1, "FBGQLS:GROUP_RESET_UNSEEN_ACTIVITY_SUBSCRIBE": 0.1, "FBGQLS:INTERN_CALENDAR_UPDATE_SUBSCRIBE": 0.1, "SKY:gizmo_manage": 10000, "FBGQLS:FEEDBACK_LIKE_SUBSCRIBE": 10, "FBGQLS:HUDDLE_USERS_REQUESTED_TO_SPEAK_COUNT_SUBSCRIBE": 1000}}, 4501], ["DGWWebConfig", [], {"appId": "************", "appVersion": "0", "dgwVersion": "2", "endpoint": "", "fbId": "0", "authType": ""}, 5508], ["NewsRegulationErrorMessageData", [], {"availableErrorCodes": [2216007, 2216012], "errorCodeToRegType": {"2216007": "c18", "2216012": "coppa"}, "learnMoreLinks": {"c18": {"regulated_user": "https://www.facebook.com/help/787040499275067", "user": "https://www.facebook.com/help/2579891418969617"}, "au00": {"regulated_user": "https://www.facebook.com/help/787040499275067", "user": "https://www.facebook.com/help/2579891418969617"}, "global_block": {"regulated_user": "https://www.facebook.com/help/787040499275067", "user": "https://www.facebook.com/help/2579891418969617"}}, "learnMoreLink": {"regulated_user": "https://www.facebook.com/help/787040499275067", "user": "https://www.facebook.com/help/2579891418969617"}, "appealLinks": {"c18": "https://www.facebook.com/help/contact/419859403337390"}}, 7133], ["DTSGInitialData", [], {}, 258], ["DTSGInitData", [], {"token": "", "async_get_token": ""}, 3515], ["MentionsCommonWordsBlocklist", [], {"wordList": ["100", "A", "ALL", "AME", "AND", "Aam", "Add", "<PERSON><PERSON>", "Alh", "All", "<PERSON>", "Ame", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Apu", "Bac", "Bah", "<PERSON><PERSON>", "<PERSON><PERSON>", "Bir", "But", "Can", "Com", "Con", "Dad", "D<PERSON>", "FOR", "For", "Gal", "Gan", "God", "Goo", "Gre", "HAH", "<PERSON>p", "Har", "Hin", "Hop", "How", "JAI", "Jai", "<PERSON><PERSON>", "Kwa", "Let", "Loo", "<PERSON>v", "M", "MAN", "Maa", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "More", "Nam", "New", "<PERSON><PERSON>", "<PERSON><PERSON>", "Not", "PAR", "Pag", "Pak", "Per", "Pol", "Pro", "<PERSON><PERSON>", "S", "Se<PERSON>", "Sir", "Sob", "Sor", "Str", "Sub", "<PERSON><PERSON>", "Suv", "Suvo", "THE", "<PERSON>ha", "The", "There", "<PERSON><PERSON>", "This", "Tru", "Vai", "<PERSON>er", "Waa", "Wat", "Wel", "Wha", "What", "Whi", "Wow", "YOU", "Yes", "You", "<PERSON><PERSON>", "a", "abou", "alam", "alla", "allah", "amar", "amee", "amin", "andi", "b", "baba", "babu", "baby", "back", "bada", "baha", "baka", "bala", "bali", "ball", "bana", "band", "bang", "bara", "bari", "basa", "basi", "bata", "bath", "baya", "beat", "beau", "beli", "best", "bett", "bhag", "bhai", "bhan", "bhar", "birt", "blac", "bles", "bond", "boss", "brea", "call", "came", "camp", "care", "carr", "cele", "chan", "chil", "chin", "clea", "coll", "come", "comm", "cont", "cook", "coul", "coun", "d", "dada", "dhan", "diya", "done", "ever", "every", "fami", "free", "frien", "full", "g", "gali", "game", "gani", "gard", "gene", "gina", "good", "great", "gree", "guru", "h", "haha", "hair", "hand", "happ", "hard", "hari", "have", "hear", "hell", "here", "high", "hind", "home", "hope", "hous", "i", "idol", "indi", "inte", "jaga", "jana", "just", "k", "kaba", "kala", "kali", "kama", "kami", "kana", "kang", "kara", "kart", "kasa", "kasi", "kata", "kenya", "khat", "khus", "kind", "kita", "know", "koth", "kuya", "laba", "lang", "last", "like", "littl", "little", "look", "love", "lovel", "m", "ma", "maha", "mala", "mali", "mama", "manu", "many", "marr", "mate", "math", "mati", "medi", "mera", "minh", "miss", "more", "much", "musi", "must", "mwan", "n", "nahi", "nang", "nani", "neve", "news", "night", "p", "page", "pain", "park", "part", "pata", "path", "peace", "peop", "peopl", "pera", "pers", "photo", "plan", "post", "powe", "pres", "pric", "r", "ra", "radh", "rahi", "rama", "s", "sa", "saha", "same", "sana", "sang", "sath", "saya", "shre", "shub", "shya", "sing", "siya", "stat", "stil", "stre", "sunda", "sweet", "t", "tala", "tara", "thak", "tham", "than", "thank", "that", "then", "there", "they", "thing", "time", "toma", "tran", "turn", "v", "valo", "very", "wala", "wali", "walk", "wall", "wana", "wang", "well", "white", "will", "with", "work", "year", "youn", "your", "गुरु", "श्री", "<PERSON><PERSON><PERSON><PERSON>", "আমিন", "আল<PERSON><PERSON>া", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "কষ্ট", "কিছু", "জামা", "জ<PERSON><PERSON>ন", "থেকে", "দিদি", "পরিবার", "প্রতি", "বাংল", "বাংলাদেশ", "বাবা", "বৃষ্টি", "ব্যা", "ভালো", "ভালোবাস", "মানু", "মানুষ", "সকাল", "সত্য", "সবাই", "সুন্দর"]}, 6179], ["WebLoomConfig", [], {"adaptive_config": {"interactions": {"modules": {"2097": 1, "2804": 1, "12068": 1}, "events": {}}, "qpl": {"modules": {}, "events": {}}, "modules": null, "events": null}}, 4171], ["FbtResultGK", [], {"shouldReturnFbtResult": true, "inlineMode": "NO_INLINE"}, 876], ["CometVideoPlayerNextgendashWorkerResource", [], {"url": "https://static.xx.fbcdn.net/rsrc.php/v4/yy/r/SaT3Y-NYtvp.js", "name": "VideoPlayerNextgendashWorkerEntrypointBundle", "dynamicModules": {"SiteData": {"server_revision": 1024716595, "client_revision": 1024713728, "push_phase": "C3", "pkg_cohort": "BP:DEFAULT", "haste_session": "20283.BP:DEFAULT.2.0...0", "pr": 1, "manifest_base_uri": "https://static.xx.fbcdn.net", "manifest_origin": null, "manifest_version_prefix": null, "be_one_ahead": false, "is_rtl": false, "is_experimental_tier": false, "is_jit_warmed_up": false, "hsi": "7526955515560961055", "semr_host_bucket": "5", "bl_hash_version": 2, "comet_env": 0, "wbloks_env": false, "ef_page": "relay_ef:ProfileCometTimelineFeedRefetchQuery", "compose_bootloads": false, "spin": 4, "__spin_r": 1024713728, "__spin_b": "trunk", "__spin_t": 1752505897, "vip": "************"}, "InitialCookieConsent": {"deferCookies": false, "initialConsent": [1, 2], "noCookies": false, "shouldShowCookieBanner": false, "shouldWaitForDeferredDatrCookie": false, "optedInIntegrations": ["adobe_marketo_rest_api", "blings_io_video", "chili_piper_api", "cloudfront_cdn", "giphy_media", "google_ads_pixel_frame_legacy", "google_ads_pixel_img_legacy", "google_ads_pixel_legacy", "google_ads_remarketing_tag", "google_ads_services", "google_analytics_4_tag", "google_analytics_img", "google_cached_img", "google_double_click_loading", "google_double_click_redirecting", "google_double_click_uri_connect", "google_double_click_uri_frame", "google_double_click_uri_img", "google_fonts", "google_fonts_font", "google_maps", "google_paid_ads_frame", "google_paid_ads_img", "google_translate", "google_universal_analytics_legacy", "google_universal_analytics_legacy_img", "google_universal_analytics_legacy_script", "jio", "linkedin_insight", "linkedin_insight_img", "mapbox_maps_api", "medallia_digital_experience_analytics", "microsoft_exchange", "nytimes_oembed", "reachtheworld_s3", "soundcloud_oembed", "spotify_oembed", "spreaker_oembed", "ted_oembed", "tenor_api", "tenor_images", "tenor_media", "tiktok_oembed", "twitter_analytics_pixel", "twitter_analytics_pixel_img", "twitter_legacy_embed", "vimeo_oembed", "youtube_embed", "youtube_oembed", "advertiser_hosted_pixel", "airbus_sat", "amazon_media", "apps_for_office", "ark<PERSON>_captcha", "aspnet_cdn", "autodesk_fusion", "bing_maps", "bing_widget", "boku_wallet", "bootstrap", "box", "cardinal_centinel_api", "chromecast_extensions", "cloudflare_cdnjs", "cloudflare_datatables", "cloudflare_relay", "conversions_api_gateway", "demandbase_api", "digitalglobe_maps_api", "dlocal", "dropbox", "esri_sat", "facebook_sdk", "fastly_relay", "gmg_pulse_embed_iframe", "google_ads_conversions_tag", "google_drive", "google_fonts_legacy", "google_hosted_libraries", "google_oauth_api", "google_oauth_api_v2", "google_recaptcha", "here_map_ext", "hive_streaming_video", "iproov", "isptoolbox", "j<PERSON>y", "js_delivr", "kbank", "mathjax", "meshy", "meta_pixel", "metacdn", "microsoft_excel", "microsoft_office_addin", "microsoft_onedrive", "microsoft_speech", "microsoft_teams", "mmi_tiles", "oculus", "open_street_map", "paypal_billing_agreement", "paypal_oauth_api", "payu", "payu_india", "plaid", "platformized_adyen_checkout", "plotly", "pydata", "razorpay", "recruitics", "rstudio", "salesforce_lighting", "shopify_app_bridge", "stripe", "team_center", "tripshot", "trustly_direct_debit_ach", "twilio_voice", "unifier", "unpkg", "unsplash_api", "unsplash_image_loading", "vega", "yoti_api", "youtube_oembed_api", "google_apis", "google_apis_scripts", "google_img", "google_tag", "google_uri_frame", "google_uri_script"], "hasGranularThirdPartyCookieConsent": true, "exemptedIntegrations": ["advertiser_hosted_pixel", "airbus_sat", "amazon_media", "apps_for_office", "ark<PERSON>_captcha", "aspnet_cdn", "autodesk_fusion", "bing_maps", "bing_widget", "boku_wallet", "bootstrap", "box", "cardinal_centinel_api", "chromecast_extensions", "cloudflare_cdnjs", "cloudflare_datatables", "cloudflare_relay", "conversions_api_gateway", "demandbase_api", "digitalglobe_maps_api", "dlocal", "dropbox", "esri_sat", "facebook_sdk", "fastly_relay", "gmg_pulse_embed_iframe", "google_ads_conversions_tag", "google_drive", "google_fonts_legacy", "google_hosted_libraries", "google_oauth_api", "google_oauth_api_v2", "google_recaptcha", "here_map_ext", "hive_streaming_video", "iproov", "isptoolbox", "j<PERSON>y", "js_delivr", "kbank", "mathjax", "meshy", "meta_pixel", "metacdn", "microsoft_excel", "microsoft_office_addin", "microsoft_onedrive", "microsoft_speech", "microsoft_teams", "mmi_tiles", "oculus", "open_street_map", "paypal_billing_agreement", "paypal_oauth_api", "payu", "payu_india", "plaid", "platformized_adyen_checkout", "plotly", "pydata", "razorpay", "recruitics", "rstudio", "salesforce_lighting", "shopify_app_bridge", "stripe", "team_center", "tripshot", "trustly_direct_debit_ach", "twilio_voice", "unifier", "unpkg", "unsplash_api", "unsplash_image_loading", "vega", "yoti_api", "youtube_oembed_api"]}, "cr:3725": {"__bbox": {"__rc": ["clearTimeoutWWWOrMobile", null]}}, "cr:4344": {"__bbox": {"__rc": ["setTimeoutWWWOrMobile", null]}}, "cr:6640": {"__bbox": {"__rc": [null, null]}}, "cr:7385": {"__bbox": {"__rc": ["clearIntervalWWW", null]}}, "cr:7386": {"__bbox": {"__rc": ["clearTimeoutWWW", null]}}, "cr:7388": {"__bbox": {"__rc": ["setIntervalWWW", null]}}, "cr:7390": {"__bbox": {"__rc": ["setTimeoutWWW", null]}}, "cr:806696": {"__bbox": {"__rc": ["clearTimeoutComet", null]}}, "cr:807042": {"__bbox": {"__rc": ["setTimeoutComet", null]}}, "cr:896461": {"__bbox": {"__rc": ["setIntervalComet", null]}}, "cr:925100": {"__bbox": {"__rc": ["RunComet", null]}}, "cr:1003267": {"__bbox": {"__rc": ["clearIntervalComet", null]}}}, "hsdp": {"gkxData": {"5415": {"result": false, "hash": null}, "7742": {"result": false, "hash": null}, "8068": {"result": false, "hash": null}, "8869": {"result": false, "hash": null}, "9063": {"result": false, "hash": null}, "20935": {"result": false, "hash": null}, "20936": {"result": false, "hash": null}}, "metaconfigData": {"57": {"value": 0}, "84": {"value": ""}, "100": {"value": 0}, "101": {"value": 0}, "102": {"value": 0}, "109": {"value": 0}, "110": {"value": 0}, "111": {"value": 0}, "113": {"value": ""}, "119": {"value": 0}, "120": {"value": false}, "121": {"value": 0}, "122": {"value": 0}, "123": {"value": 0}, "124": {"value": 0}, "125": {"value": 0}, "126": {"value": 0}, "127": {"value": 0}, "128": {"value": 0}, "129": {"value": 0}, "130": {"value": ""}, "131": {"value": 0}, "133": {"value": 0}, "134": {"value": 0}, "143": {"value": false}, "154": {"value": false}}, "qplData": {"93": {}, "106": {}}, "justknobxData": {"3323": {"r": true}}}}, 7463], ["CometVideoPlayerNextgendashWorkerWithDebugResource", [], {"url": "https://static.xx.fbcdn.net/rsrc.php/v4/yI/r/1GTN-ZFCrmJ.js", "name": "VideoPlayerNextgendashWorkerWithDebugEntrypointBundle", "dynamicModules": {"SiteData": {"server_revision": 1024716595, "client_revision": 1024713728, "push_phase": "C3", "pkg_cohort": "BP:DEFAULT", "haste_session": "20283.BP:DEFAULT.2.0...0", "pr": 1, "manifest_base_uri": "https://static.xx.fbcdn.net", "manifest_origin": null, "manifest_version_prefix": null, "be_one_ahead": false, "is_rtl": false, "is_experimental_tier": false, "is_jit_warmed_up": false, "hsi": "7526955515560961055", "semr_host_bucket": "5", "bl_hash_version": 2, "comet_env": 0, "wbloks_env": false, "ef_page": "relay_ef:ProfileCometTimelineFeedRefetchQuery", "compose_bootloads": false, "spin": 4, "__spin_r": 1024713728, "__spin_b": "trunk", "__spin_t": 1752505897, "vip": "************"}, "InitialCookieConsent": {"deferCookies": false, "initialConsent": [1, 2], "noCookies": false, "shouldShowCookieBanner": false, "shouldWaitForDeferredDatrCookie": false, "optedInIntegrations": ["adobe_marketo_rest_api", "blings_io_video", "chili_piper_api", "cloudfront_cdn", "giphy_media", "google_ads_pixel_frame_legacy", "google_ads_pixel_img_legacy", "google_ads_pixel_legacy", "google_ads_remarketing_tag", "google_ads_services", "google_analytics_4_tag", "google_analytics_img", "google_cached_img", "google_double_click_loading", "google_double_click_redirecting", "google_double_click_uri_connect", "google_double_click_uri_frame", "google_double_click_uri_img", "google_fonts", "google_fonts_font", "google_maps", "google_paid_ads_frame", "google_paid_ads_img", "google_translate", "google_universal_analytics_legacy", "google_universal_analytics_legacy_img", "google_universal_analytics_legacy_script", "jio", "linkedin_insight", "linkedin_insight_img", "mapbox_maps_api", "medallia_digital_experience_analytics", "microsoft_exchange", "nytimes_oembed", "reachtheworld_s3", "soundcloud_oembed", "spotify_oembed", "spreaker_oembed", "ted_oembed", "tenor_api", "tenor_images", "tenor_media", "tiktok_oembed", "twitter_analytics_pixel", "twitter_analytics_pixel_img", "twitter_legacy_embed", "vimeo_oembed", "youtube_embed", "youtube_oembed", "advertiser_hosted_pixel", "airbus_sat", "amazon_media", "apps_for_office", "ark<PERSON>_captcha", "aspnet_cdn", "autodesk_fusion", "bing_maps", "bing_widget", "boku_wallet", "bootstrap", "box", "cardinal_centinel_api", "chromecast_extensions", "cloudflare_cdnjs", "cloudflare_datatables", "cloudflare_relay", "conversions_api_gateway", "demandbase_api", "digitalglobe_maps_api", "dlocal", "dropbox", "esri_sat", "facebook_sdk", "fastly_relay", "gmg_pulse_embed_iframe", "google_ads_conversions_tag", "google_drive", "google_fonts_legacy", "google_hosted_libraries", "google_oauth_api", "google_oauth_api_v2", "google_recaptcha", "here_map_ext", "hive_streaming_video", "iproov", "isptoolbox", "j<PERSON>y", "js_delivr", "kbank", "mathjax", "meshy", "meta_pixel", "metacdn", "microsoft_excel", "microsoft_office_addin", "microsoft_onedrive", "microsoft_speech", "microsoft_teams", "mmi_tiles", "oculus", "open_street_map", "paypal_billing_agreement", "paypal_oauth_api", "payu", "payu_india", "plaid", "platformized_adyen_checkout", "plotly", "pydata", "razorpay", "recruitics", "rstudio", "salesforce_lighting", "shopify_app_bridge", "stripe", "team_center", "tripshot", "trustly_direct_debit_ach", "twilio_voice", "unifier", "unpkg", "unsplash_api", "unsplash_image_loading", "vega", "yoti_api", "youtube_oembed_api", "google_apis", "google_apis_scripts", "google_img", "google_tag", "google_uri_frame", "google_uri_script"], "hasGranularThirdPartyCookieConsent": true, "exemptedIntegrations": ["advertiser_hosted_pixel", "airbus_sat", "amazon_media", "apps_for_office", "ark<PERSON>_captcha", "aspnet_cdn", "autodesk_fusion", "bing_maps", "bing_widget", "boku_wallet", "bootstrap", "box", "cardinal_centinel_api", "chromecast_extensions", "cloudflare_cdnjs", "cloudflare_datatables", "cloudflare_relay", "conversions_api_gateway", "demandbase_api", "digitalglobe_maps_api", "dlocal", "dropbox", "esri_sat", "facebook_sdk", "fastly_relay", "gmg_pulse_embed_iframe", "google_ads_conversions_tag", "google_drive", "google_fonts_legacy", "google_hosted_libraries", "google_oauth_api", "google_oauth_api_v2", "google_recaptcha", "here_map_ext", "hive_streaming_video", "iproov", "isptoolbox", "j<PERSON>y", "js_delivr", "kbank", "mathjax", "meshy", "meta_pixel", "metacdn", "microsoft_excel", "microsoft_office_addin", "microsoft_onedrive", "microsoft_speech", "microsoft_teams", "mmi_tiles", "oculus", "open_street_map", "paypal_billing_agreement", "paypal_oauth_api", "payu", "payu_india", "plaid", "platformized_adyen_checkout", "plotly", "pydata", "razorpay", "recruitics", "rstudio", "salesforce_lighting", "shopify_app_bridge", "stripe", "team_center", "tripshot", "trustly_direct_debit_ach", "twilio_voice", "unifier", "unpkg", "unsplash_api", "unsplash_image_loading", "vega", "yoti_api", "youtube_oembed_api"]}, "cr:3725": {"__bbox": {"__rc": ["clearTimeoutWWWOrMobile", null]}}, "cr:4344": {"__bbox": {"__rc": ["setTimeoutWWWOrMobile", null]}}, "cr:6640": {"__bbox": {"__rc": [null, null]}}, "cr:7385": {"__bbox": {"__rc": ["clearIntervalWWW", null]}}, "cr:7386": {"__bbox": {"__rc": ["clearTimeoutWWW", null]}}, "cr:7388": {"__bbox": {"__rc": ["setIntervalWWW", null]}}, "cr:7390": {"__bbox": {"__rc": ["setTimeoutWWW", null]}}, "cr:806696": {"__bbox": {"__rc": ["clearTimeoutComet", null]}}, "cr:807042": {"__bbox": {"__rc": ["setTimeoutComet", null]}}, "cr:896461": {"__bbox": {"__rc": ["setIntervalComet", null]}}, "cr:925100": {"__bbox": {"__rc": ["RunComet", null]}}, "cr:1003267": {"__bbox": {"__rc": ["clearIntervalComet", null]}}}, "hsdp": {"gkxData": {"5415": {"result": false, "hash": null}, "7742": {"result": false, "hash": null}, "8068": {"result": false, "hash": null}, "8869": {"result": false, "hash": null}, "9063": {"result": false, "hash": null}, "20935": {"result": false, "hash": null}, "20936": {"result": false, "hash": null}}, "metaconfigData": {"57": {"value": 0}, "84": {"value": ""}, "100": {"value": 0}, "101": {"value": 0}, "102": {"value": 0}, "109": {"value": 0}, "110": {"value": 0}, "111": {"value": 0}, "113": {"value": ""}, "119": {"value": 0}, "120": {"value": false}, "121": {"value": 0}, "122": {"value": 0}, "123": {"value": 0}, "124": {"value": 0}, "125": {"value": 0}, "126": {"value": 0}, "127": {"value": 0}, "128": {"value": 0}, "129": {"value": 0}, "130": {"value": ""}, "131": {"value": 0}, "133": {"value": 0}, "134": {"value": 0}, "143": {"value": false}, "154": {"value": false}}, "qplData": {"93": {}, "106": {}}, "justknobxData": {"3323": {"r": true}}}}, 7466], ["VideoPlayerNextgendashWorkerV2EntrypointWorkerResource", [], {"name": "VideoPlayerNextgendashWorkerV2EntrypointWorkerBundle"}, 8047], ["TimeSliceInteractionSV", [], {"on_demand_reference_counting": true, "on_demand_profiling_counters": true, "default_rate": 1000, "lite_default_rate": 100, "interaction_to_lite_coinflip": {"ADS_INTERFACES_INTERACTION": 0, "ads_perf_scenario": 0, "ads_wait_time": 0, "Event": 1}, "interaction_to_coinflip": {"ADS_INTERFACES_INTERACTION": 1, "ads_perf_scenario": 1, "ads_wait_time": 1, "Event": 100}, "enable_heartbeat": false, "maxBlockMergeDuration": 0, "maxBlockMergeDistance": 0, "enable_banzai_stream": true, "user_timing_coinflip": 50, "banzai_stream_coinflip": 0, "compression_enabled": true, "ref_counting_fix": false, "ref_counting_cont_fix": false, "also_record_new_timeslice_format": false, "force_async_request_tracing_on": false}, 2609], ["VideoPlayerStateBasedLoggingEvents", [], {"StateBasedLoggingEventNames": ["cancelled_requested_playing", "caption_change", "chromecast_availability_checked", "chromecast_not_supported", "chromecast_button_visible", "entered_fs", "entered_hd", "error", "exited_fs", "exited_hd", "finished_loading", "finished_playing", "headset_connected", "headset_disconnected", "host_error", "muted", "paused", "player_format_changed", "quality_change", "requested_playing", "scrubbed", "seeked", "started_playing", "unmuted", "unpaused", "volume_changed", "volume_decrease", "volume_increase", "viewport_rotated", "viewport_zoomed", "heading_reset", "guide_entered", "guide_exited", "spherical_fallback_entered", "played_for_three_seconds", "commercial_break_offscreen", "commercial_break_onscreen", "ad_break_starting_indicator", "ad_break_non_interruptive_ad_start", "ad_break_non_interruptive_ad_click", "ad_break_pre_roll_ad_start", "ad_break_tap_on_trailer", "ad_break_tap_start_from_trailer", "representation_ended", "heart_beat", "stale", "viewability_changed", "video_logging_session_timeout", "video_logging_session_wakeup", "retry_on_error", "playback_speed_changed", "video_warmup_evicted", "suppress_video_off", "suppress_video_on", "error_recovery_attempt", "displayed_frames", "video_viewability_updated"]}, 3462], ["VideoPlayerContextSensitiveConfigPayload", [], {"context_sensitive_values": {"buffering_overflow_threshold": [{"value": 3.0832147598267, "contexts": [{"name": "connection_quality", "value": "POOR"}]}, {"value": 3.0832147598267, "contexts": [{"name": "connection_quality", "value": "MODERATE"}]}], "enable_request_pipelining_for_live": [{"value": false, "contexts": [{"name": "latency_level", "value": "low"}]}], "initial_stream_buffer_size_float": [{"value": 10, "contexts": [{"name": "is_ad", "value": true}]}], "is_low_latency": [{"value": true, "contexts": [{"name": "latency_level", "value": "low"}]}], "live_stream_buffer_size_float": [{"value": 10, "contexts": [{"name": "content_category", "value": "esports"}]}, {"value": 6.5, "contexts": [{"name": "content_category", "value": "gaming"}]}], "num_predictive_segments": [{"value": 4, "contexts": [{"name": "latency_level", "value": "low"}]}], "oz_www_append_byte_target_without_range": [{"value": 1, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_bandwidth_ignore_on_stream_write_samples": [{"value": true, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_bandwidth_use_response_time_adjustment": [{"value": true, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_buffer_ahead_target": [{"value": 10, "contexts": [{"name": "streaming_implementation", "value": "pdash"}, {"name": "content_category", "value": "gaming"}]}, {"value": 14, "contexts": [{"name": "streaming_implementation", "value": "pdash"}, {"name": "is_latency_sensitive_broadcast", "value": true}]}, {"value": 24, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_catchup_timeout_after_buffering_sec": [{"value": 0.001, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_catchup_timeout_after_play_sec": [{"value": 30, "contexts": [{"name": "fbls_tier", "value": "user"}]}], "oz_www_ignore_reset_after_seek_if_bufferahead": [{"value": true, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_in_play_buffer_overflow_target": [{"value": 1, "contexts": [{"name": "connection_quality", "value": "POOR"}]}, {"value": 1, "contexts": [{"name": "connection_quality", "value": "MODERATE"}]}], "oz_www_latencymanager_stalled_edgelatency_sec": [{"value": 2, "contexts": [{"name": "streaming_implementation", "value": "pdash"}, {"name": "latency_level", "value": "ultra-low"}]}, {"value": 6, "contexts": [{"name": "streaming_implementation", "value": "pdash"}, {"name": "content_category", "value": "gaming"}]}, {"value": 10, "contexts": [{"name": "streaming_implementation", "value": "pdash"}, {"name": "is_latency_sensitive_broadcast", "value": true}]}, {"value": 18, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_latencymanager_stalled_edgelatency_sec_on": [{"value": false, "contexts": [{"name": "streaming_implementation", "value": "pdash"}, {"name": "latency_level", "value": "ultra-low"}]}, {"value": true, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_live_initial_playback_position": [{"value": -14, "contexts": [{"name": "content_category", "value": "gaming"}]}], "oz_www_live_rewind_seek_to_live_delta": [{"value": 4, "contexts": [{"name": "streaming_implementation", "value": "pdash"}, {"name": "is_latency_sensitive_broadcast", "value": true}]}], "oz_www_minimum_bytes_to_sample_on_close": [{"value": 10000, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_ms_promise_for_null": [{"value": true, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_ms_promise_for_null_ms": [{"value": 1000, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_network_reload_mpd_json": [{"value": "[\"504\", \"404\"]", "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_network_retry_intervals_json": [{"value": "{\"0\": 1000, \"404\": 2000, \"502\": 1000, \"503\": 1000, \"504\": 1000, \"20\": 1, \"429\": 2000}", "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_overwrite_live_time_range_block_margin": [{"value": 5, "contexts": [{"name": "latency_level", "value": "low"}, {"name": "fbls_tier", "value": "user"}]}], "oz_www_overwrite_livehead_fall_behind_block_threshold": [{"value": 10, "contexts": [{"name": "latency_level", "value": "low"}]}], "oz_www_pdash_download_cursor_catchup_threshold_gop_multiplier": [{"value": 1, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_pdash_download_cursor_catchup_threshold_sec": [{"value": 1, "contexts": [{"name": "streaming_implementation", "value": "pdash"}, {"name": "latency_level", "value": "ultra-low"}]}, {"value": 2, "contexts": [{"name": "streaming_implementation", "value": "pdash"}, {"name": "is_latency_sensitive_broadcast", "value": true}, {"name": "latency_level", "value": "low"}]}, {"value": 6, "contexts": [{"name": "streaming_implementation", "value": "pdash"}, {"name": "is_latency_sensitive_broadcast", "value": true}]}, {"value": 10, "contexts": [{"name": "streaming_implementation", "value": "pdash"}, {"name": "latency_level", "value": "low"}]}, {"value": 10, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_pdash_download_cursor_catchup_tolerance_sec": [{"value": 1, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_pdash_use_pdash_segmentlocator": [{"value": true, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_per_stream_duration_target": [{"value": 0.5, "contexts": [{"name": "connection_quality", "value": "GOOD"}]}, {"value": 0.5, "contexts": [{"name": "connection_quality", "value": "EXCELLENT"}]}], "oz_www_playback_speed_latency_slowdown_adjustment_rate": [{"value": 0.2, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_playback_speed_latency_speedup_adjustment_rate": [{"value": 0.1, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_playback_speed_min_buffer_sec": [{"value": 0.5, "contexts": [{"name": "streaming_implementation", "value": "pdash"}, {"name": "latency_level", "value": "ultra-low"}]}], "oz_www_player_emit_mpdparsed_early": [{"value": true, "contexts": [{"name": "is_live", "value": true}]}], "oz_www_player_emit_mpdready_early": [{"value": true, "contexts": [{"name": "is_live", "value": true}]}], "oz_www_player_formats_for_low_latency": [{"value": "[\"*\"]", "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_playhead_manager_buffered_auto_seek_playhead_slack": [{"value": 0.01, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_playhead_manager_buffered_numerical_error": [{"value": 0, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_playhead_manager_clamp_initial_playback_position": [{"value": true, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_pre_start_buffer_ahead_target": [{"value": 10, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_reset_catchup_timeout_after_play_sec_on_overwrite": [{"value": true, "contexts": [{"name": "content_category", "value": "gaming"}]}, {"value": true, "contexts": [{"name": "fbls_tier", "value": "user"}]}], "oz_www_seek_on_latency_level_change_allowed": [{"value": "[[\"low\",\"normal\"],[\"normal\",\"normal\"]]", "contexts": [{"name": "content_category", "value": "gaming"}, {"name": "is_live", "value": true}]}], "oz_www_skip_videobuffer_gaps": [{"value": true, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_skip_videobuffer_gaps_on_buffer_updated": [{"value": true, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_stale_mpd_buffer_ahead_target": [{"value": 2, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_start_buffer_underflow_target": [{"value": 1.1, "contexts": [{"name": "connection_quality", "value": "POOR"}]}, {"value": 1.1, "contexts": [{"name": "connection_quality", "value": "MODERATE"}]}], "oz_www_steadystate_minbuffer_buckets": [{"value": 4, "contexts": [{"name": "streaming_implementation", "value": "pdash"}, {"name": "latency_level", "value": "ultra-low"}]}, {"value": 20, "contexts": [{"name": "streaming_implementation", "value": "pdash"}, {"name": "is_latency_sensitive_broadcast", "value": true}]}], "oz_www_steadystate_minbuffer_sec": [{"value": 1, "contexts": [{"name": "streaming_implementation", "value": "pdash"}, {"name": "latency_level", "value": "ultra-low"}]}, {"value": 1.75, "contexts": [{"name": "streaming_implementation", "value": "pdash"}, {"name": "is_latency_sensitive_broadcast", "value": true}]}], "oz_www_stream_types_eligible_for_partial_playback": [{"value": "audio", "contexts": [{"name": "is_live", "value": true}]}], "oz_www_systemic_risk_abr_initial_risk_factor": [{"value": 4, "contexts": [{"name": "is_live", "value": true}]}], "oz_www_systemic_risk_abr_low_mos_resolution": [{"value": 260, "contexts": [{"name": "is_live", "value": true}]}], "oz_www_time_to_first_byte_ignore_above_threshold_ms": [{"value": 200, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}], "oz_www_use_live_latency_manager": [{"value": true, "contexts": [{"name": "streaming_implementation", "value": "pdash"}]}]}, "static_values": {"stream_buffer_size_float": 15, "initial_stream_buffer_size_float": 7.5, "min_switch_interval": 5000, "min_eval_interval": 1000, "bandwidth_upgrade_target": 0.9, "live_bandwidth_upgrade_target": 0.63308577239513, "resolution_constraint_factor": 2, "resolve_video_time_update_on_fragmented_time_ranges": true, "live_send_push_headers": false, "live_initial_stream_buffer_size_float": 3.5, "enable_video_debug": false, "reappend_init_segment_after_abort": false, "current_time_during_ready_state_zero_throws": false, "www_videos_nudge_timestamp_correction_s": 0, "www_videos_extended_spl_logging_for_connected_tv": false, "live_abr_send_push_headers": false, "shaka_native_promise": false, "oz_www_skip_videobuffer_gaps": true, "oz_www_skip_timerange_gaps": true, "oz_www_skip_videobuffer_gaps_on_buffer_updated": true, "oz_www_skip_videobuffer_gaps_max_gap_size_sec": 0, "use_resource_timing_entry_for_bandwidth": false, "force_lowest_representation_threshold": 0, "force_lower_representation_step_ratio": 0, "enable_request_pipelining_for_vod": false, "enable_streaming_for_vod": false, "use_continuous_streaming": false, "streaming_segment_size": 4, "streaming_bandwidth_update_interval": 180000, "multi_segment_decay": 0, "enable_request_pipelining_for_live": true, "enable_streaming_code_path": true, "ignore_errors_after_unload": true, "use_pending_seek_position_for_reference": false, "start_stream_buffer_size": 1, "videos_abr_debugger_storage": false, "fix_shaka_xhr_error_status": true, "set_current_time_in_resync": true, "live_stream_buffer_size_float": 3, "use_dimensions_fallbacks": true, "aggressive_fast_moving_average_half_life": 1.5779530704021, "aggressive_slow_moving_average_half_life": 9.2335087917745, "ignore_left_button_when_pausing": true, "disable_360_abr": false, "clear_buffer_on_seek_back": false, "buffering_overflow_threshold": 0, "use_buffer_target_only_after_buffering_goal_is_hit": false, "enable_content_protection": true, "drop_buffering_detection_from_html5_api": true, "exclude_tracks_without_smooth_playback": false, "mpd_parse_frame_and_audio_sampling_rate": false, "abort_loading_decisioning_logic": true, "buffer_velocity_time_in_past_to_consider": 0, "evaluate_abr_on_fetch_end": false, "ignore_recent_bandwidth_eval_on_fetch_end": false, "bandwidth_penalty_per_additional_video": 0.1, "overwrite_video_current_time_property": false, "low_start_stream_buffer_size": 2.5, "low_bandwidth_start_stream_buffer_size_threshold": 2000000, "connection_quality_context_throttle_frequency": 1000, "allow_seek_logging_in_mixin": false, "fix_pause_current_time_in_mixin": true, "better_set_current_time_in_resync": false, "resync_set_current_time_fudge_factor": 0, "enable_main_thread_availability_logging": true, "fix_overwritten_get_video_current_time": false, "should_use_oz_p2p": false, "oz_www_enable_lip_sync_abr_select_quality": false, "oz_www_enable_alternative_audio_tracks": false, "oz_www_enable_managed_media_source": false, "oz_www_video_cdn_url_refresh": false, "oz_www_no_rep_to_switch_fallback_progressive": false, "oz_www_enable_revoke_object_url_on_destroy": false, "oz_www_enable_abr_logging": false, "oz_www_fix_oz_p2p_enable_failed": true, "num_predictive_segments": 0, "oz_www_stream_reader_max_buffer_len": 0, "oz_www_generate_mos_segment_buffer_diff": false, "oz_www_pdash_use_pdash_segmentlocator": false, "oz_www_pdash_seq_based_approx": false, "oz_www_pdash_seq_based_approx_use_blockedrange": false, "oz_www_pdash_download_cursor_catchup_threshold_sec": 0, "oz_www_pdash_download_cursor_catchup_tolerance_sec": 0, "oz_www_pdash_download_cursor_catchup_skip_totalbufer": true, "oz_www_pdash_download_cursor_catchup_threshold_gop_multiplier": 0, "oz_www_pdash_download_cursor_tolerance_gop_multiplier": 0, "oz_www_pdash_download_cursor_bufferahead_gop_multiplier": 0, "oz_www_pdash_download_cursor_between_catchups_seg": 0, "oz_www_latencymanager_stalempd_edgelatency_sec": 0, "oz_www_latencymanager_stalempd_edgelatency_sec_on": false, "oz_www_latencymanager_stalled_edgelatency_sec_on": false, "oz_www_latencymanager_stalled_edgelatency_sec": 0, "oz_www_pdash_future_edgelatency_gops": 0, "oz_www_catchup_timeout_after_play_sec": 0, "oz_www_catchup_timeout_after_buffering_sec": 0, "oz_www_reset_catchup_timeout_after_play_sec_on_overwrite": true, "oz_www_catchup_min_interval_sec": 0, "oz_www_playback_speed_latency_adjustment_disabled": false, "oz_www_playback_speed_latency_adjustment_rate": 0, "oz_www_playback_speed_enabled_delay_sec": 4, "oz_www_playback_speed_min_buffer_sec": 1, "oz_www_playback_speed_min_duration_sec": 2, "oz_www_playback_speed_restore_min_duration_sec": 1, "oz_www_playback_speed_latency_slowdown_adjustment_rate": 0, "oz_www_playback_speed_latency_speedup_adjustment_rate": 0, "oz_www_pdash_download_cursor_use_totalbuffer": true, "oz_www_pdash_download_cursor_nocatchup_use_currentbuffer": false, "oz_www_playback_speed_min_sharpness_factor": 3, "oz_www_pdash_wait_on_mpd_refresh_when_error": true, "oz_www_pdash_download_cursor_pause_duration_of_gop": false, "oz_www_pdash_download_cursor_catchup_only_when_advancing": false, "oz_www_download_cursor_use_node_time": false, "oz_www_download_cursor_total_buffer_max_sec": 0, "oz_www_download_cursor_buffer_ahead_time_max_sec": 0, "oz_www_download_cursor_disable_buffer_ahead_rule_on": true, "oz_www_download_cursor_disable_buffer_ahead_rule_lr_on": false, "oz_www_download_cursor_1st_run_set_skipped_segment_on": true, "oz_www_download_cursor_1st_run_2_fallback": false, "oz_www_enable_predictive_dash": true, "oz_www_use_sc_timebased_segments": false, "oz_www_use_templated_pdash_segments": true, "oz_www_use_scf_timebased_segments": false, "oz_www_touch_cb_key": false, "oz_www_enable_dvl": true, "oz_www_use_dvl_with_timeout_ms": 0, "oz_www_dvl_update_interval_ms": 0, "oz_www_dvl_update_interval_reset_on_err": true, "oz_www_dvl_initial_segment_ignore_count": 1, "oz_www_use_inline_manifest_for_live": false, "oz_www_use_live_latency_manager": false, "oz_www_player_emit_mpdready_early": false, "oz_www_clear_on_seek": true, "oz_www_player_emit_mpdparsed_early": false, "oz_www_parse_number_templated_uri": true, "is_low_latency": false, "fire_seek_events": false, "shaka_buffer_abr": false, "evaluate_abr_on_tracks_and_bandwidth_change": true, "enable_double_ingest": true, "www_videos_playback_remove_src_attr_on_unload": false, "www_videos_playback_call_load_on_unload": false, "has_live_rewind_egress": false, "has_live_rewind_ui_www": false, "use_oz_with_fbms_eligible": true, "create_restore_abort_loading_promise": true, "oz_www_fix_representation_ended_timing": true, "oz_www_safely_log_player_seeks": true, "oz_www_enable_adaptation": true, "oz_www_fix_quick_starter_overhead": true, "oz_www_abr_restrict_from_index": 0, "oz_www_abr_restrict_to_index": 0, "oz_www_initial_switch_interval": 0, "oz_www_min_switch_interval": 100, "oz_www_abr_min_bandwidth_samples": 0, "oz_www_min_eval_interval": 100, "oz_www_bandwidth_estimator_half_life": 6, "oz_www_bandwidth_estimator_variance_penalty_half_life": 0, "oz_www_bandwidth_estimator_variance_penalty_down_factor": 0, "oz_www_bandwidth_estimator_variance_penalty_up_factor": 0, "oz_www_bandwidth_estimator_outlier_exclusion_factor": 50, "oz_www_bandwidth_estimator_std_dev_penalty_factor": 0, "oz_www_abr_confidence_threshold": 0.9, "oz_www_segments_to_stream": 5, "oz_www_per_stream_duration_target": 0, "oz_www_segments_to_stream_under_playhead_threshold": 0, "oz_www_low_segment_stream_playhead_threshold": 0, "oz_www_appends_per_segment": 6, "oz_www_append_byte_target_without_range": 100000, "oz_www_log_appended_secs": false, "oz_www_lazy_parse_sidx": false, "oz_www_abr_eval_buffer_threshold": 0, "oz_www_throttle_playback_rate_on_stall": true, "oz_www_use_prefetch_cache": true, "oz_www_force_initial_representation": true, "oz_www_buffer_ahead_target": 22, "oz_www_low_buffer_bandwidth_target_threshold": 10, "oz_www_low_buffer_bandwidth_target_increase_factor": 0, "oz_www_segments_to_stream_near_bandwidth_boundary": 5, "oz_www_bandwidth_boundary_standard_deviation_factor": 1, "oz_www_suppress_playing_event_while_buffering": false, "oz_www_resolution_constraint_factor": 2, "oz_www_pre_start_buffer_ahead_target": 16.924449682236, "oz_www_stale_mpd_buffer_ahead_target": 0, "oz_www_setup_buffer_target_onload": true, "oz_www_in_play_buffer_underflow_target": 0.1, "oz_www_in_play_buffer_overflow_target": 1, "oz_www_buffer_when_waiting": false, "oz_www_start_buffer_underflow_target": 0.1, "oz_www_byte_count_per_sample": 200000, "oz_www_minimum_bytes_to_sample_on_close": 25000, "oz_www_manifest_update_frequency_ms": 0, "oz_www_manifest_initial_update_delay_ms": 0, "oz_www_exclude_prefetch_bandwidth_samples": true, "oz_www_connection_quality_context_throttle_frequency": 2000, "oz_www_paused_stream_segments_count": 2, "oz_www_minimum_bandwidth_sample_duration": 10, "oz_www_maximum_bandwidth_sample_bandwidth": 100000000, "oz_www_max_bandwidth_sample_count": 30, "oz_www_use_performance_entry_on_stream_close": false, "oz_www_ignore_time_to_response_start": false, "oz_www_network_seg_timeout_ms": 0, "oz_www_bandwidth_response_time_handicap": 0, "oz_www_bandwidth_ignore_on_stream_write_samples": false, "oz_www_bandwidth_use_response_time_adjustment": false, "oz_www_use_scheduler": true, "oz_www_cdn_experiment_id": "", "oz_www_disable_audio_scheduler": false, "oz_www_no_new_loop_body_promise_when_stream_ongoing": true, "oz_www_fix_seek_performance": false, "oz_www_ignore_reset_after_seek_if_bufferahead": false, "oz_www_ignore_reset_after_seek_if_bufferahead_liverewind": true, "oz_www_enable_network_manager_error": false, "oz_www_parse_first_segment": false, "oz_www_enable_abr_for_first_request": false, "oz_www_update_media_source_duration": true, "oz_www_ms_promise_for_null": false, "oz_www_ms_promise_for_null_ms": 0, "oz_www_sbm_waits_for_update_end": true, "oz_www_sbm_cancel_operation_synchronous": false, "oz_www_sbm_recursively_waits_for_update_end": false, "oz_www_auto_seek_playhead_slack": 0.5, "oz_www_playhead_manager_buffered_auto_seek_playhead_slack": 0.5, "oz_www_playhead_manager_buffered_is_near_gap_threshold": 1.5, "oz_www_playhead_manager_buffered_numerical_error": 0.01, "oz_www_playhead_manager_handle_timerange_update_on_timeupdate": true, "oz_www_playhead_manager_buffered_timerange_update_on_timeupdate": true, "oz_www_playhead_manager_timeupdate_throttle_ms": 1000, "oz_www_playhead_manager_clamp_initial_playback_position": false, "oz_www_playhead_manager_buffer_gaps_skip_reverse": true, "oz_www_media_stream_buffer_gaps_ignore_before_seek": false, "oz_www_playhead_manager_initial_playback_position_lat_mgr": true, "oz_www_playheadman_dont_skip_ahead_past_last_fetched": false, "oz_www_seek_ahead_epsilon": 0.05, "oz_www_seek_ahead_use_native_current_time": true, "oz_www_timeline_offset_threshold": 10, "oz_www_live_rewind_seek_to_live_delta": 8, "oz_www_update_seekable_range": true, "oz_www_update_duration_when_init_appended": true, "oz_www_abr_prevent_down_switch_buffer_threshold": 11, "oz_www_check_buffer_range_once_for_playhead_update": false, "oz_www_fix_start_timestamp": true, "oz_www_fix_templated_manifest_r_field_check": true, "oz_www_seek_on_latency_level_change": false, "oz_www_seek_on_latency_level_change_allowed": "[]", "oz_www_live_initial_playback_position": -20, "oz_www_live_query_time_in_range": true, "oz_www_timerange_manager_cleanup": false, "oz_www_livehead_fall_behind_block_threshold": 30, "oz_www_overwrite_livehead_fall_behind_block_threshold": 0, "oz_www_live_time_range_block_margin": 5.9, "oz_www_overwrite_live_time_range_block_margin": 0, "oz_www_live_gracefully_handle_mpd_errors": true, "oz_www_live_no_segment_when_playhead_is_before_first_segment": true, "oz_www_live_disable_mpd_updates_when_paused": true, "oz_www_cleanup_video_node_on_destroy": true, "oz_www_detach_media_source_manager": true, "oz_www_enable_abortload_and_reload": true, "oz_www_live_playhead_catch_up": false, "oz_www_live_catch_up_only_when_paused": false, "oz_www_live_catch_up_fall_behind_threshold": 20, "oz_www_live_catch_up_live_head_delta": 6, "oz_www_live_numerical_error_epsilon": 0.0001, "oz_www_stop_manifest_update_when_static": true, "oz_www_buffer_end_only_when_buffering": false, "oz_www_stream_interrupt_check_mpd_stale_count_threshold": 6, "oz_www_reach_end_only_when_video_ended": false, "oz_www_allow_queueing_end_of_stream_when_update": false, "oz_www_set_source_buffer_append_window_end": false, "oz_www_use_stream_end_time_in_segment_locator": false, "oz_www_pausable_stream_throws_error_when_aborted": true, "oz_www_network_retry_intervals_json": "{\"0\": 1000, \"404\": 2000, \"502\": 1000, \"503\": 1000, \"504\": 1000, \"429\": 2000, \"20\": 1000}", "oz_www_network_retry_intervals_json_retry": false, "oz_www_network_reload_mpd_json_retry": false, "oz_www_network_end_broadcasts_json": "[\"410\"]", "oz_www_network_skip_json": "[]", "oz_www_network_reload_mpd_json": "[]", "oz_www_fallback_on_append_error": false, "oz_www_enable_appends_on_wait_update_end_failure": false, "oz_www_bandwidth_penalty_per_additional_video": 0, "oz_www_bandwidth_penalty_additional_video_start": 0, "oz_www_prefetch_cache_bandwidth_upper_limit": 0, "oz_www_throw_network_error_during_stream": false, "oz_www_retry_fetch_on_prefetch_error": true, "oz_www_use_abr_for_missing_default_representation": true, "oz_www_initial_manifest_request_retry_count": 3, "oz_www_download_time_buffer_delta_penalty_factor": 0, "oz_www_time_to_first_byte_estimate_half_life_ms": 500, "oz_www_handle_missing_manifest_segment_timeline": true, "oz_www_mos_upper_threshold": 0, "oz_www_mos_lower_threshold": 0, "oz_www_abr_use_download_time": false, "oz_www_minimum_download_additional_buffer_ms": 0, "oz_www_use_deferred_streaming_task": true, "oz_www_allow_abort_loading_from_autoplay_controller": false, "oz_www_enable_double_ingest": true, "oz_www_use_oz_credentials_provider": true, "oz_www_throw_on_non_zero_r_d_mismatch": true, "oz_www_fix_template_duration_artifact_in_manifest": true, "oz_www_bandwidth_cache_key": "bandwidthEstimate", "oz_www_estimate_video_bandwidth_only": true, "oz_www_default_bandwidth_estimate": 1000000, "oz_www_update_bandwidth_cache_on_sample": false, "oz_www_live_audio_ibr_bandwidth_percentage": 0.05, "comet_www_no_pause_on_blur_or_focus_events": false, "oz_www_live_gracefully_handle_no_network": true, "oz_www_live_max_try_attempts_on_404": 2, "oz_www_clear_buffer_when_switch_representation_initiator_is_user": true, "oz_www_use_segment_request_cache": false, "oz_www_seconds_to_stream": 10, "oz_www_seconds_to_stream_near_bandwidth_boundary": 10, "oz_www_queue_data_with_error_handling": false, "oz_www_clear_prepended_segments_count_on_append": true, "oz_www_back_off_pdash_504_retry": true, "oz_www_call_end_of_stream_in_quick_starter": true, "oz_www_instantiate_buffering_detector_before_quick_starter": true, "oz_www_maybe_end_stream_if_prepended_segments": true, "oz_www_seek_to_start_quick_starter": true, "oz_www_use_loose_manifest_updates": false, "oz_www_use_full_player_if_cached": true, "oz_www_enable_quickdashv2": false, "oz_www_copy_new_manifest": true, "oz_www_handle_switch_to_unparsed_representation_sidx": true, "oz_www_stable_buffered_timeranges_in_observedsourcebufferstate": true, "oz_www_emit_stream_error_event": true, "oz_www_max_start_eme_attempts": 3, "oz_www_listen_for_canplay_in_buffering_detector": true, "oz_www_handle_mpd_retries_outside_oz_mpd_updater": true, "oz_www_respect_initial_representation_on_setup": false, "oz_www_update_live_video_config_on_representation_switch": true, "oz_www_mpd_update_cancel_current_request_tracker": true, "oz_www_tagged_time_range_per_append_throttle": 0, "oz_www_fix_reload_manifest_retry": true, "oz_www_fix_source_buffer_error_logging": true, "oz_www_min_block_time_range_interval_ms": 0, "oz_www_fix_buffer_ahead_priority_strategy": true, "oz_www_append_last_segment_if_beyond_end": true, "oz_www_set_stream_anchor_only_on_done_status": true, "oz_www_fix_stream_deferred_cancel": true, "oz_www_sbm_abort_on_readable_stream_error": true, "oz_www_cancel_tracker_on_append_error": true, "oz_www_pixels_below_viewport_to_observe": 0, "oz_www_prioritize_by_viewport_static_penalty": 0, "oz_www_fix_setup_video_duration_on_representation_switch": true, "oz_www_handle_mpd_null_error_codes": true, "oz_www_fix_prepended_segments_tagging": true, "oz_www_xmlparser_use_domparser": true, "oz_www_cache_mos_threshold": false, "oz_www_load_video_node_on_unload": true, "oz_www_convert_dom_exception_to_oz_error": true, "oz_www_sbm_check_source_buffer_ready_state_on_cancel": true, "oz_www_prevent_unnecessary_seek_stream_anchor_reset": true, "oz_www_stream_types_eligible_for_partial_playback": "", "oz_www_partial_playback_buffer_overflow": 0.75, "oz_www_live_use_inline_manifest_after_request_manifest": false, "oz_www_live_gracefully_handle_410": true, "oz_www_segment_end_410_response": true, "oz_www_segment_end_410_response_loop_end": true, "oz_www_steadystate_minbuffer_sec": 0, "oz_www_steadystate_minbuffer_buckets": 0, "oz_www_steadystate_minbuffer_buckets_sec": 1, "oz_www_pixels_above_viewport_to_observe": 0, "oz_www_delay_stream_end_for_sourceended": true, "www_videos_shaka_use_xbox_buffered_fudge_factor": 0.05, "oz_www_media_source_is_available_in_ended_state": true, "oz_www_unset_override_oz_request_implementation_on_hive_error": false, "oz_www_live_gracefully_handle_502": true, "oz_www_systemic_risk_abr_apply_representation_restrictions": true, "oz_www_dynamic_mpd_initial_playback_position_offset_modifier": 4, "oz_www_player_formats_for_low_latency": "[]", "oz_www_loop_body_handle_error_interval_ms": 1, "oz_www_load_sequence_max_delay_ms": 0, "oz_www_get_fetch_body_text_when_response_not_ok": true, "oz_www_load_sequence_only_prioritize_first_count": 0, "oz_www_stream_interrupted_fuzzy_equals": false, "oz_www_sbm_skip_abort_on_media_error": true, "oz_www_sbm_abort_on_append_new_readable_stream": false, "oz_www_prevent_unnecessary_quickstarter_instance": true, "oz_www_stricter_mpd_parser_invariants": true, "oz_www_accept_external_buffering_detector": true, "oz_www_treat_inline_mpd_xml_empty_string_as_null": false, "oz_www_live_gracefully_handle_429": true, "oz_www_use_systemic_risk_abr": true, "oz_www_use_sidnee_abr": false, "oz_www_systemic_risk_abr_risk_factor": 1.75, "oz_www_time_to_first_byte_ignore_above_threshold_ms": 0, "oz_www_bandwidth_ttfb_samples_to_save": 5, "oz_www_blue_video_player_pass_inline_mpd_xml_empty_string_as_undefined": false, "oz_www_blue_video_player_force_xhr": false, "oz_www_set_global_config": false, "oz_www_systemic_risk_abr_low_mos_risk_factor": 1.3, "oz_www_systemic_risk_abr_min_watchable_mos": 30, "oz_www_systemic_risk_abr_low_mos_resolution": 0, "oz_www_prevent_multiple_successive_representation_switch": true, "oz_www_sidx_parser_memory_optimization": true, "oz_www_buffer_when_waiting_in_partial_buffer": true, "oz_www_stream_interrupt_in_play_buffer_overflow_target": 1, "oz_www_stream_interrupt_buffer_target_timeout_ms": 10000, "oz_www_recent_buffer_in_play_buffer_overflow_target": 3, "oz_www_recent_buffer_timeout_ms": 10000, "oz_www_use_ending_duration_for_gop_multiplier": true, "oz_www_live_trace_parse_emsg": true, "oz_www_catchup_use_timeline_range_end_time_as_end": false, "oz_www_perf_log_representation_id_more": true, "oz_www_systemic_risk_use_fetch_range_duration": false, "oz_www_use_simple_moving_average_estimator": false, "oz_www_http_no_cache": false, "oz_www_use_buffering_detector_for_playhead_interruption": true, "oz_www_enable_immediate_down_switch": false, "oz_www_handle_media_source_manager_errors": true, "oz_www_handle_invalid_webm_duration": false, "oz_www_parse_initialization_binary": true, "oz_www_systemic_risk_abr_high_estimate_confidence": 52, "oz_www_data_reader_concat_once": true, "oz_www_pump_concat_once": true, "oz_www_sbm_append_readable_stream_concat_once": true, "oz_www_log_appended_segment_info": false, "oz_www_log_media_element_error_source_buffer_updateend_error": true, "oz_www_log_extra_events": true, "oz_www_overwrite_video_current_time_property": true, "oz_www_clear_buffer_on_seek_into_unbuffered_range": true, "oz_www_numerical_range_utils_is_after_range_exclusive": true, "oz_www_server_side_abr_send_client_estimates": false, "oz_www_server_side_abr_use_server_estimates": false, "oz_www_live_gracefully_handle_504": true, "oz_www_debug_live_replay": false, "oz_www_append_retry_quota_exceeded_error": true, "oz_www_buffer_target_constraint_minimum_sec": 2, "oz_www_buffer_target_constraint_append_succeeded_reward": 0.2, "oz_www_always_use_current_time_in_playback_state": true, "oz_www_buffer_target_constraint_quota_exceeded_penalty": 0.3, "oz_www_retry_null_error_code_in_stream": false, "oz_www_emit_destroyed_after_media_keys_clear": false, "oz_www_do_not_end_stream": false, "oz_www_do_not_flush_data_queue_after_destroy": false, "oz_www_do_not_update_duration_vod": false, "oz_www_no_representation_error_detailed_message": true, "oz_www_mpd_updater_network_request_timeout_ms": 20000, "oz_www_restart_media_streams_on_stream_resumed": false, "oz_www_media_stream_tracker_cancel_on_seek": false, "oz_www_media_stream_fix_double_segments_compute": false, "oz_www_fix_initial_segment_non_zero_start_time": true, "oz_www_get_mpd_least_last_time_range": true, "oz_www_respect_playback_restrictions_in_abr_fallback": true, "oz_www_null_segment_for_no_buffer_target": true, "oz_www_systemic_risk_abr_initial_risk_factor": 1, "oz_www_systemic_risk_abr_document_hidden_risk_factor": 0, "oz_www_use_bandwidth_estimate_from_headers_in_abr": false, "oz_www_use_ttfb_from_headers": false, "oz_www_skip_playhead_adjustment_before_initial_playback_position": false, "oz_www_clear_buffer_on_seek_epsilon_s": 0, "oz_www_handle_buffered_timerange_update_on_ratechange": false, "oz_www_stub_safari_source_buffer_abort": false, "oz_www_clear_buffer_on_seek_nudge_s": 0, "oz_www_streaming_task_reject_current_stream_deferred": true, "oz_www_min_buffer_behind_playhead": 10, "oz_www_check_mediasource_readystate_before_end_of_stream": true, "oz_www_retry_video_element_error": true, "oz_www_clamp_seek_to_first_buffer_range_epsilon": 0.75, "oz_www_ignore_restrictions_when_all_representations_restricted": false, "oz_www_hive_maximum_trimming_seconds": 0, "oz_www_block_representation_status_codes_json": "[500, 503]", "oz_www_block_representation_status_codes_temporarily_json": "{}", "oz_www_revoke_object_url_on_detach": false, "oz_www_abort_clear_video_node_on_detach": false, "oz_www_proceed_on_representation_change_in_init_append_fail": true, "oz_www_should_check_that_source_buffer_attached": false, "oz_www_exclude_large_representations_after_restrictions": true, "oz_www_stop_loop_driver_immediately_on_cleanup": true, "oz_www_playhead_manager_handle_time_range_update_on_waiting": false, "oz_www_enable_error_recovery_attempt_logging": true, "oz_www_systemic_risk_abr_prefetch_initial_risk_factor": 5, "oz_www_prefetch_resolution_threshold": 810, "oz_www_throw_no_license_error": true, "oz_www_vtt_caption_representation": true, "oz_www_live_video_send_transaction_id_in_requests": false, "oz_www_log_exposure_on_oz_initialization": false, "oz_www_systemic_risk_abr_prefetch_low_mos_resolution": 0, "oz_www_allow_subsequent_prefetch": true, "oz_www_comet_video_player_live_vtt_captions": false, "oz_www_prefetch_retention_duration_ms": 0, "oz_www_clear_prefetch_before_unload": false, "oz_www_emit_captions_changed_event": true, "oz_www_use_vtt_captions_visible_buffer_strategy": false, "oz_www_disable_end_of_stream_in_caption_stream": true, "oz_www_append_once_per_stream_in_caption_stream": true, "oz_www_append_once_per_stream_in_application_stream": false, "oz_www_enable_era_logging_for_application_stream": false, "oz_www_emit_stream_gone_on_end_stream_before_start": true, "oz_www_sidx_segment_retry_attempts": 9999, "oz_www_sidx_segment_retry_interval_ms": 100, "oz_www_clear_buffer_around_playhead_boundary_ms": 5000, "oz_www_fix_manifest_fetch_mixed_promise_catch": true, "oz_www_fix_update_duration_check": false, "oz_www_never_decrease_mediasource_duration": false, "oz_www_normalize_mpd_fetch_errors": true, "oz_www_no_retry_on_empty_string_error_code": false, "oz_www_use_src_object_for_media_source": false, "oz_www_mpd_parse_all_adaptation_sets": true, "oz_www_mpd_ensure_playable_representations": false, "oz_www_ull_use_broadcast_sensitivity_type": true, "oz_www_ull_fallback_stall_count": 2, "oz_www_live_video_chunk_duration": 0, "oz_www_av1_check_hardware_support": false, "oz_www_ultra_low_latency_bandwidth_threshold": 10000000, "oz_www_bandwidth_estimate_header_key": "", "oz_www_bandwidth_estimate_key": "", "oz_www_fix_ull_fallback_stall_count": false, "oz_www_set_latency_context_immediately": false, "oz_www_fall_back_to_low_latency_in_ull": false, "oz_www_bandwidth_header_expire_threshold": 0, "oz_www_fix_live_rewind_user_selected_playback_speed": true, "oz_www_is_csvqm_enabled": true, "oz_www_msm_refactor_wait_for_sourceopen": false}}, 5261], ["DataStoreConfig", [], {"expandoKey": "__FB_STORE", "useExpando": true}, 2915], ["MessengerMSplitFlag", [], {"is_msplit_account": false, "is_dma_consent_declined": false}, 7414], ["cr:2680", ["WebPixelRatio"], {"__rc": ["WebPixelRatio", null]}, -1], ["cr:9989", ["Banzai"], {"__rc": ["Banzai", null]}, -1], ["cr:7387", ["requestIdleCallbackWWW"], {"__rc": ["requestIdleCallbackWWW", null]}, -1], ["cr:4048", [], {"__rc": [null, null]}, -1], ["cr:14238", ["FDSPoliticsFilled12PNGIcon.react"], {"__rc": ["FDSPoliticsFilled12PNGIcon.react", null]}, -1], ["cr:1088657", [], {"__rc": [null, null]}, -1], ["cr:2683", ["warningBlue"], {"__rc": ["warningBlue", null]}, -1], ["cr:5278", ["ReactDOM-prod.classic"], {"__rc": ["ReactDOM-prod.classic", null]}, -1], ["cr:806696", ["clearTimeoutBlue"], {"__rc": ["clearTimeoutBlue", null]}, -1], ["cr:807042", ["setTimeoutBlue"], {"__rc": ["setTimeoutBlue", null]}, -1], ["cr:1172", ["WebSession"], {"__rc": ["WebSession", null]}, -1], ["cr:2037", ["BanzaiAdapter"], {"__rc": ["BanzaiAdapter", null]}, -1], ["cr:3724", ["SetIdleTimeoutAcrossTransitions"], {"__rc": ["SetIdleTimeoutAcrossTransitions", null]}, -1], ["cr:9985", ["performanceAbsoluteNow"], {"__rc": ["performanceAbsoluteNow", null]}, -1], ["cr:9986", ["CurrentUser"], {"__rc": ["CurrentUser", null]}, -1], ["cr:9987", ["NavigationMetrics"], {"__rc": ["NavigationMetrics", null]}, -1], ["cr:9988", ["Visibility"], {"__rc": ["Visibility", null]}, -1], ["cr:6799", ["EventProfilerAdsSessionProvider"], {"__rc": ["EventProfilerAdsSessionProvider", null]}, -1], ["cr:862", ["LexicalClipboard.prod"], {"__rc": ["LexicalClipboard.prod", null]}, -1], ["FunnelLoggerConfig", [], {"freq": {"WWW_MESSENGER_VIDEO_CHAT_LINKS_FUNNEL": 1, "PIE_MANAGER_HIRING_HUB_FUNNEL": 1, "WWW_ESCALATION_TOOLS_NOTIFICATIONS_PAGE_FUNNEL": 1, "WWW_ONCALL_VIEW_FUNNEL": 1, "WWW_MESSENGER_GROUP_ESCALATION_FUNNEL": 10, "WWW_SPATIAL_REACTION_PRODUCTION_FUNNEL": 1, "CREATIVE_STUDIO_CREATION_FUNNEL": 1, "WWW_CANVAS_AD_CREATION_FUNNEL": 1, "WWW_CANVAS_EDITOR_FUNNEL": 1, "WWW_LINK_PICKER_DIALOG_FUNNEL": 1, "WWW_MEME_PICKER_DIALOG_FUNNEL": 1, "WWW_LEAD_GEN_FORM_CREATION_FUNNEL": 1, "WWW_LEAD_GEN_FORM_EDITOR_FUNNEL": 1, "WWW_LEAD_GEN_DESKTOP_AD_UNIT_FUNNEL": 1, "WWW_LEAD_GEN_MSITE_AD_UNIT_FUNNEL": 1, "WWW_CAMPFIRE_COMPOSER_UPSELL_FUNNEL": 1, "WWW_PMT_FUNNEL": 1, "WWW_PULSE_FUNNEL": 1, "WWW_ORGTOOL_FUNNEL": 1, "WWW_RECRUITING_PRODUCTS_ATTRIBUTION_FUNNEL": 1, "WWW_RECRUITING_PRODUCTS_FUNNEL": 1, "WWW_RECRUITING_SEARCH_FUNNEL": 1, "WWW_RECRUITING_BULK_EMAIL_FUNNEL": 1, "WWW_RECRUITING_LEAVE_HANDLER_FUNNEL": 1, "WWW_EXAMPLE_FUNNEL": 1, "WWW_REACTIONS_BLINGBAR_NUX_FUNNEL": 1, "WWW_REACTIONS_NUX_FUNNEL": 1, "WWW_COMMENT_REACTIONS_NUX_FUNNEL": 1, "WWW_MESSENGER_SHARE_TO_FB_FUNNEL": 10, "POLYGLOT_MAIN_FUNNEL": 1, "MSITE_EXAMPLE_FUNNEL": 10, "WWW_FEED_SHARE_DIALOG_FUNNEL": 100, "MSITE_AD_BREAKS_ONBOARDING_FLOW_FUNNEL": 1, "MSITE_FEED_ALBUM_CTA_FUNNEL": 10, "MSITE_FEED_SHARE_DIALOG_FUNNEL": 100, "MSITE_COMMENT_TYPING_FUNNEL": 500, "MSITE_HASHTAG_PROMPT_FUNNEL": 1, "WWW_SEARCH_AWARENESS_LEARNING_NUX_FUNNEL": 1, "WWW_CONSTITUENT_TITLE_UPSELL_FUNNEL": 1, "MTOUCH_FEED_MISSED_STORIES_FUNNEL": 10, "WWW_UFI_SHARE_LINK_FUNNEL": 1, "WWW_CMS_SEARCH_FUNNEL": 1, "GAMES_QUICKSILVER_FUNNEL": 1, "SOCIAL_SEARCH_CONVERSION_WWW_FUNNEL": 1, "SOCIAL_SEARCH_DASHBOARD_WWW_FUNNEL": 1, "SRT_USER_FLOW_FUNNEL": 1, "MSITE_PPD_FUNNEL": 1, "WWW_PAGE_CREATION_FUNNEL": 1, "NT_EXAMPLE_FUNNEL": 1, "WWW_LIVE_VIEWER_TIPJAR_FUNNEL": 1, "FACECAST_BROADCASTER_FUNNEL": 1, "WWW_FUNDRAISER_CREATION_FUNNEL": 1, "WWW_FUNDRAISER_EDIT_FUNNEL": 1, "WWW_OFFERS_SIMPLE_COMPOSE_FUNNEL": 1, "QP_TOOL_FUNNEL": 1, "WWW_OFFERS_SIMPLE_COMPOSE_POST_LIKE_FUNNEL": 1, "COLLEGE_COMMUNITY_NUX_ONBOARDING_FUNNEL": 1, "CASUAL_GROUP_PICKER_FUNNEL": 1, "TOPICS_TO_FOLLOW_FUNNEL": 1, "WWW_MESSENGER_SEARCH_SESSION_FUNNEL": 1, "WWW_LIVE_PRODUCER_FUNNEL": 1, "FX_PLATFORM_INVITE_JOIN_FUNNEL": 1, "CREATIVE_STUDIO_HUB_FUNNEL": 1, "WWW_SEE_OFFERS_CTA_NUX_FUNNEL": 1, "WWW_ADS_TARGETING_AUDIENCE_MANAGER_FUNNEL": 1, "WWW_AD_BREAKS_ONBOARDING_FUNNEL": 1, "WWW_AD_BREAK_HOME_ONBOARDING_FUNNEL": 1, "WWW_NOTIFS_UP_NEXT_FUNNEL": 10, "ADS_VIDEO_CAPTION_FUNNEL": 1, "KEYFRAMES_FUNNEL": 500, "SEARCH_ADS_WWW_FUNNEL": 1, "WWW_ALT_TEXT_COMPOSER_FUNNEL": 1, "BUSINESS_PAYMENTS_MERCHANT_ONBOARDING_FUNNEL": 1, "MERCHANT_PAYMENTS_MERCHANT_ONBOARDING_FUNNEL": 1, "SELLER_EXPERIENCE_ONBOARDING_NEW_FUNNEL": 1, "SELLER_EXPERIENCE_PAYOUT_SETUP_NEW_FUNNEL": 1, "SELLER_EXPERIENCE_SHOP_MANAGEMENT_FUNNEL": 1, "WWW_BUSINESS_CREATION_FUNNEL": 1, "WWW_BUSINESS_VERIFICATION_FUNNEL": 1, "WWW_APP_REVIEW_BUSINESS_VERIFICATION_FUNNEL": 1, "SELLER_EXPERIENCE_MIGRATION_FUNNEL": 1, "SELLER_EXPERIENCE_PAYOUT_SETUP_FUNNEL": 1, "PAYOUT_ONBOARDING_FUNNEL": 1, "SERVICES_INSTANT_BOOKING_SETTINGS_FUNNEL": 1, "SERVICES_FB_APPOINTMENTS_CTA_FULL_SETUP_FUNNEL": 1, "SERVICES_FB_APPOINTMENTS_CTA_CREATION_FUNNEL": 1, "FB_NEO_ONBOARDING_FUNNEL": 1, "FB_NEO_FRIENDING_FUNNEL": 1, "WWW_MESSENGER_CONTENT_SEARCH_FUNNEL": 1, "SEARCH_FUNNEL": 1, "SHADOW_SEARCH_FUNNEL": 1, "SHADOW_EARLY_END_SEARCH_FUNNEL": 1, "UNIDASH_EDIT_WIDGET_FUNNEL": 1, "PRIVATE_COMMENT_COMPOSER_FUNNEL": 1, "WEB_RTC_SCREEN_SHARING_FUNNEL": 1, "CHECKOUT_EXPERIENCES_FUNNEL": 1, "CHECKOUT_EXPERIENCES_SELLER_FUNNEL": 1, "WWW_SERVICES_INSTANT_BOOKING_CONSUMER_FUNNEL": 1, "WWW_SERVICES_BOOK_APPOINTMENT_CONSUMER_FUNNEL": 10, "WWW_SPHERICAL_DIRECTOR_FUNNEL": 1, "NATIVE_SUPPORT_FUNNEL": 1, "WWW_PRESENCE_FUNNEL": 1, "MESSENGER_UNIVERSAL_SEARCH_FUNNEL": 1, "MESSENGER_SECONDARY_SEARCH_FUNNEL": 1, "WORK_CHAT_SEARCH_FUNNEL": 1, "PRIVACY_SHORTCUTS_FUNNEL": 1, "PRIVACY_ACCESS_HUB_FUNNEL": 1, "WWW_POLITICIAN_OFFICE_SETTING_FUNNEL": 1, "WWW_CIVIC_ACTION_POST_INVITE_FUNNEL": 1, "WWW_MESSENGER_SHARE_FILE_PREVIEW_FUNNEL": 1, "ALL_VOICES_FUNNEL": 1, "AEC_APPLICATION_FUNNEL": 1, "INSTANT_EXPERIENCES_MINDBODY_FUNNEL": 1, "WWW_LAUNCHPAD_ONBOARDING_FUNNEL": 1, "GIZMO_VCPANEL_CALL_START_FUNNEL": 1, "WWW_FB_CHAT_NEW_SETTINGS_MENU_FUNNEL": 1, "KAIOS_REG_CONTACT_IMPORTER_FUNNEL": 1, "MAP_WEB_FUNNEL": 1, "SOCIAL_VR_INTERACTIVE_THREE_SIXTY_FUNNEL": 1, "WWW_SNOWLIFT_ACTIONS_FUNNEL": 1, "SVC_DEPLOYMENT_ONBOARDING_FUNNEL": 1, "FHT_FUNNEL": 1, "default": 1000}}, 1271], ["ProfileActionFollowIcon", [], {"follow_icon": "https://static.xx.fbcdn.net/rsrc.php/v4/yw/r/LJ8KuNpi23A.png", "following_icon": "https://static.xx.fbcdn.net/rsrc.php/v4/yb/r/3rG7SyNnpzs.png", "follow_icon_menu_item": "https://static.xx.fbcdn.net/rsrc.php/v4/ys/r/Wq8eg2y7j69.png", "following_icon_menu_item": "https://static.xx.fbcdn.net/rsrc.php/v4/yR/r/y3IG9Oy6c2r.png"}, 7000], ["GhlTennisKnobsConfig", [], {"ghlbox_log_validity_in_mins": 7200, "ghlbox_initialize_in_mins": 14400, "change_class_interval_in_mins": 1440}, 6687], ["EventConfig", [], {"sampling": {"bandwidth": 0, "play": 0, "playing": 0, "progress": 0, "pause": 0, "ended": 0, "seeked": 0, "seeking": 0, "waiting": 0, "loadedmetadata": 0, "canplay": 0, "selectionchange": 0, "change": 0, "timeupdate": 0, "adaptation": 0, "focus": 0, "blur": 0, "load": 0, "error": 0, "message": 0, "abort": 0, "storage": 0, "scroll": 200000, "mousemove": 20000, "mouseover": 10000, "mouseout": 10000, "mousewheel": 1, "MSPointerMove": 10000, "keydown": 0.1, "click": 0.02, "mouseup": 0.02, "__100ms": 0.001, "__default": 5000, "__min": 100, "__interactionDefault": 200, "__eventDefault": 100000}, "page_sampling_boost": 1, "interaction_regexes": {}, "interaction_boost": {}, "event_types": {}, "manual_instrumentation": false, "profile_eager_execution": false, "disable_heuristic": true, "disable_event_profiler": false}, 1726], ["cr:694370", ["requestIdleCallbackBlue"], {"__rc": ["requestIdleCallbackBlue", null]}, -1], ["cr:3695", [], {"__rc": [null, null]}, -1], ["cr:983844", [], {"__rc": [null, null]}, -1], ["cr:5695", ["EventListenerWWW"], {"__rc": ["EventListenerWWW", null]}, -1], ["cr:8909", ["ReactFiberErrorDialogWWW"], {"__rc": ["ReactFiberErrorDialogWWW", null]}, -1], ["cr:5866", ["BanzaiAdapterWWW"], {"__rc": ["BanzaiAdapterWWW", null]}, -1], ["cr:6016", ["NavigationMetricsWWW"], {"__rc": ["NavigationMetricsWWW", null]}, -1], ["cr:7384", ["cancelIdleCallbackWWW"], {"__rc": ["cancelIdleCallbackWWW", null]}, -1], ["cr:1497", ["LexicalHtml.prod"], {"__rc": ["LexicalHtml.prod", null]}, -1], ["CoreWarningGK", [], {"forceWarning": false}, 725], ["AdsInterfacesSessionConfig", [], {}, 2393], ["cr:1353359", ["EventListenerImplForBlue"], {"__rc": ["EventListenerImplForBlue", null]}, -1], ["cr:692209", ["cancelIdleCallbackBlue"], {"__rc": ["cancelIdleCallbackBlue", null]}, -1], ["BanzaiConfig", [], {"MAX_SIZE": 10000, "MAX_WAIT": 150000, "MIN_WAIT": null, "RESTORE_WAIT": 150000, "blacklist": ["time_spent"], "disabled": false, "gks": {"comet_flush_lazy_queue": true, "boosted_pagelikes": true, "platform_oauth_client_events": true, "sticker_search_ranking": true}, "known_routes": ["artillery_javascript_actions", "artillery_javascript_trace", "artillery_logger_data", "logger", "falco", "gk2_exposure", "js_error_logging", "loom_trace", "marauder", "perfx_custom_logger_endpoint", "qex", "require_cond_exposure_logging", "metaconfig_exposure"], "should_drop_unknown_routes": true, "should_log_unknown_routes": false}, 7], ["UFI2SilhouetteConfig", [], {"neutralSilhouette20": "https://scontent.fdac145-1.fna.fbcdn.net/v/t1.30497-1/84628273_176159830277856_972693363922829312_n.jpg?stp=c379.0.1290.1290a_cp0_dst-jpg_s24x24_tt6&_nc_cat=1&ccb=1-7&_nc_sid=7565cd&_nc_ohc=BVPx3gQwt3AQ7kNvwE2oHoB&_nc_oc=AdnHZzzweuqebhEU7w--rAL3Jk5ihf42fI1-1h3bMENsGDFS4Fc0cYDXl-Jpf3HpQqs&_nc_zt=24&_nc_ht=scontent.fdac145-1.fna&oh=00_AfSGtxWQLCyzOmqSSIBgxDdWOMiImtqwk-uNvU93lZ-srw&oe=689C9059", "neutralSilhouette24": "https://scontent.fdac145-1.fna.fbcdn.net/v/t1.30497-1/84628273_176159830277856_972693363922829312_n.jpg?stp=c379.0.1290.1290a_cp0_dst-jpg_s24x24_tt6&_nc_cat=1&ccb=1-7&_nc_sid=7565cd&_nc_ohc=BVPx3gQwt3AQ7kNvwE2oHoB&_nc_oc=AdnHZzzweuqebhEU7w--rAL3Jk5ihf42fI1-1h3bMENsGDFS4Fc0cYDXl-Jpf3HpQqs&_nc_zt=24&_nc_ht=scontent.fdac145-1.fna&oh=00_AfSGtxWQLCyzOmqSSIBgxDdWOMiImtqwk-uNvU93lZ-srw&oe=689C9059", "neutralSilhouette32": "https://scontent.fdac145-1.fna.fbcdn.net/v/t1.30497-1/84628273_176159830277856_972693363922829312_n.jpg?stp=c379.0.1290.1290a_cp0_dst-jpg_s32x32_tt6&_nc_cat=1&ccb=1-7&_nc_sid=7565cd&_nc_ohc=BVPx3gQwt3AQ7kNvwE2oHoB&_nc_oc=AdnHZzzweuqebhEU7w--rAL3Jk5ihf42fI1-1h3bMENsGDFS4Fc0cYDXl-Jpf3HpQqs&_nc_zt=24&_nc_ht=scontent.fdac145-1.fna&oh=00_AfRU7ESIkVIogmy0LvTE6aT24ZJe85D6_uKhpIjPB_6kig&oe=689C9059", "neutralSilhouette40": "https://scontent.fdac145-1.fna.fbcdn.net/v/t1.30497-1/84628273_176159830277856_972693363922829312_n.jpg?stp=c379.0.1290.1290a_cp0_dst-jpg_s40x40_tt6&_nc_cat=1&ccb=1-7&_nc_sid=7565cd&_nc_ohc=BVPx3gQwt3AQ7kNvwE2oHoB&_nc_oc=AdnHZzzweuqebhEU7w--rAL3Jk5ihf42fI1-1h3bMENsGDFS4Fc0cYDXl-Jpf3HpQqs&_nc_zt=24&_nc_ht=scontent.fdac145-1.fna&oh=00_AfROf9Nw3FwxwMA4TINVCebZIoT4C6E_cpS11esDdS8-RA&oe=689C9059", "neutralSilhouette60": "https://scontent.fdac145-1.fna.fbcdn.net/v/t1.30497-1/84628273_176159830277856_972693363922829312_n.jpg?stp=c379.0.1290.1290a_cp0_dst-jpg_s60x60_tt6&_nc_cat=1&ccb=1-7&_nc_sid=7565cd&_nc_ohc=BVPx3gQwt3AQ7kNvwE2oHoB&_nc_oc=AdnHZzzweuqebhEU7w--rAL3Jk5ihf42fI1-1h3bMENsGDFS4Fc0cYDXl-Jpf3HpQqs&_nc_zt=24&_nc_ht=scontent.fdac145-1.fna&oh=00_AfTRRhHScLKjaXns9w871mOAznef_9LZ5f9LJdWkOPDlZg&oe=689C9059"}, 3283], ["cr:9603", [], {"__rc": [null, "Aa37_Y7YemFDrmlzxVGFtukYAzIYtwei7nLH7xlRANJuTLwJeSD30jNRErUETpoLhOTM6zGVczWy1tKy1xLgQtedJCvz0ZlWIDKIKKkZK7aefi9MKMzkfwmQ7vqooeR6a9B-L57WgzBPyK-DbBoAz7VYupuhodmbIANMZKE"]}, -1], ["cr:9602", ["useCometLoggedOutPopupCTA"], {"__rc": ["useCometLoggedOutPopupCTA", "Aa37_Y7YemFDrmlzxVGFtukYAzIYtwei7nLH7xlRANJuTLwJeSD30jNRErUETpoLhOTM6zGVczWy1tKy1xLgQtedJCvz0ZlWIDKIKKkZK7aefi9MKMzkfwmQ7vqooeR6a9B-L57WgzBPyK-DbBoAz7VYupuhodmbIANMZKE"]}, -1], ["cr:2242", [], {"__rc": [null, "Aa1Nu9QF_S6TrQZ3_UraMGKj671bFJl0aCFTcW3MNoWjpdrmJGqJA1-v61TafbpwHJ6ON-Q955xo0FJV_Rhp16VQyrofU0J2dMrEUa5MAUqEBjns8isDLtY_Jkr8ZPnnsU0KGxwkmfwwQ0U"]}, -1], ["cr:4301", ["useCometLoggedOutPopupCTA"], {"__rc": ["useCometLoggedOutPopupCTA", "Aa0TTnVagAzMtvV6Bmd0tzJYPfbM_9D8OgufFlVTZ_U1IICVYFp9RyP3ndynetXlXetMmo4JC3qc_9fdsMpZLYjiJaHv4EJTYFR5K1XH4KiW6B4-jnCzF2vXvBgsmoUouXF3WZVSHCaeuJOobRNGF8qHTwLPuGyhzPd0XoM"]}, -1], ["cr:4300", [], {"__rc": [null, "Aa0TTnVagAzMtvV6Bmd0tzJYPfbM_9D8OgufFlVTZ_U1IICVYFp9RyP3ndynetXlXetMmo4JC3qc_9fdsMpZLYjiJaHv4EJTYFR5K1XH4KiW6B4-jnCzF2vXvBgsmoUouXF3WZVSHCaeuJOobRNGF8qHTwLPuGyhzPd0XoM"]}, -1], ["cr:8218", ["FDSWowReaction_v7"], {"__rc": ["FDSWowReaction_v7", "Aa3BKGn9DE82mhvcCyukCmGRohT5CDKOXSLCf-o4SqD6O-gJ9hZ3ONuzooSlbe1ytjyOLCRJLvWtXOMbo6fglh8AJ0obZLhtemEfftBm3hw9NMnkvidxmpODDM-Co9pwFgHJWUqp8rafj8A"]}, -1], ["cr:8195", ["FDSAngerReaction_v7"], {"__rc": ["FDSAngerReaction_v7", "Aa3BKGn9DE82mhvcCyukCmGRohT5CDKOXSLCf-o4SqD6O-gJ9hZ3ONuzooSlbe1ytjyOLCRJLvWtXOMbo6fglh8AJ0obZLhtemEfftBm3hw9NMnkvidxmpODDM-Co9pwFgHJWUqp8rafj8A"]}, -1], ["cr:8196", ["FDSAngerReaction_v7"], {"__rc": ["FDSAngerReaction_v7", "Aa3BKGn9DE82mhvcCyukCmGRohT5CDKOXSLCf-o4SqD6O-gJ9hZ3ONuzooSlbe1ytjyOLCRJLvWtXOMbo6fglh8AJ0obZLhtemEfftBm3hw9NMnkvidxmpODDM-Co9pwFgHJWUqp8rafj8A"]}, -1], ["cr:8198", ["FDSHahaReaction_v7"], {"__rc": ["FDSHahaReaction_v7", "Aa3BKGn9DE82mhvcCyukCmGRohT5CDKOXSLCf-o4SqD6O-gJ9hZ3ONuzooSlbe1ytjyOLCRJLvWtXOMbo6fglh8AJ0obZLhtemEfftBm3hw9NMnkvidxmpODDM-Co9pwFgHJWUqp8rafj8A"]}, -1], ["cr:8199", ["FDSHahaReaction_v7"], {"__rc": ["FDSHahaReaction_v7", "Aa3BKGn9DE82mhvcCyukCmGRohT5CDKOXSLCf-o4SqD6O-gJ9hZ3ONuzooSlbe1ytjyOLCRJLvWtXOMbo6fglh8AJ0obZLhtemEfftBm3hw9NMnkvidxmpODDM-Co9pwFgHJWUqp8rafj8A"]}, -1], ["cr:8201", ["FDSLikeReaction_v7"], {"__rc": ["FDSLikeReaction_v7", "Aa3BKGn9DE82mhvcCyukCmGRohT5CDKOXSLCf-o4SqD6O-gJ9hZ3ONuzooSlbe1ytjyOLCRJLvWtXOMbo6fglh8AJ0obZLhtemEfftBm3hw9NMnkvidxmpODDM-Co9pwFgHJWUqp8rafj8A"]}, -1], ["cr:8202", ["FDSLikeReaction_v7"], {"__rc": ["FDSLikeReaction_v7", "Aa3BKGn9DE82mhvcCyukCmGRohT5CDKOXSLCf-o4SqD6O-gJ9hZ3ONuzooSlbe1ytjyOLCRJLvWtXOMbo6fglh8AJ0obZLhtemEfftBm3hw9NMnkvidxmpODDM-Co9pwFgHJWUqp8rafj8A"]}, -1], ["cr:8203", ["FDSLoveReaction_v7"], {"__rc": ["FDSLoveReaction_v7", "Aa3BKGn9DE82mhvcCyukCmGRohT5CDKOXSLCf-o4SqD6O-gJ9hZ3ONuzooSlbe1ytjyOLCRJLvWtXOMbo6fglh8AJ0obZLhtemEfftBm3hw9NMnkvidxmpODDM-Co9pwFgHJWUqp8rafj8A"]}, -1], ["cr:8204", ["FDSLoveReaction_v7"], {"__rc": ["FDSLoveReaction_v7", "Aa3BKGn9DE82mhvcCyukCmGRohT5CDKOXSLCf-o4SqD6O-gJ9hZ3ONuzooSlbe1ytjyOLCRJLvWtXOMbo6fglh8AJ0obZLhtemEfftBm3hw9NMnkvidxmpODDM-Co9pwFgHJWUqp8rafj8A"]}, -1], ["cr:8205", ["FDSSorryReaction_v7"], {"__rc": ["FDSSorryReaction_v7", "Aa3BKGn9DE82mhvcCyukCmGRohT5CDKOXSLCf-o4SqD6O-gJ9hZ3ONuzooSlbe1ytjyOLCRJLvWtXOMbo6fglh8AJ0obZLhtemEfftBm3hw9NMnkvidxmpODDM-Co9pwFgHJWUqp8rafj8A"]}, -1], ["cr:8206", ["FDSSorryReaction_v7"], {"__rc": ["FDSSorryReaction_v7", "Aa3BKGn9DE82mhvcCyukCmGRohT5CDKOXSLCf-o4SqD6O-gJ9hZ3ONuzooSlbe1ytjyOLCRJLvWtXOMbo6fglh8AJ0obZLhtemEfftBm3hw9NMnkvidxmpODDM-Co9pwFgHJWUqp8rafj8A"]}, -1], ["cr:8207", ["FDSSupportReaction_v7"], {"__rc": ["FDSSupportReaction_v7", "Aa3BKGn9DE82mhvcCyukCmGRohT5CDKOXSLCf-o4SqD6O-gJ9hZ3ONuzooSlbe1ytjyOLCRJLvWtXOMbo6fglh8AJ0obZLhtemEfftBm3hw9NMnkvidxmpODDM-Co9pwFgHJWUqp8rafj8A"]}, -1], ["cr:8208", ["FDSSupportReaction_v7"], {"__rc": ["FDSSupportReaction_v7", "Aa3BKGn9DE82mhvcCyukCmGRohT5CDKOXSLCf-o4SqD6O-gJ9hZ3ONuzooSlbe1ytjyOLCRJLvWtXOMbo6fglh8AJ0obZLhtemEfftBm3hw9NMnkvidxmpODDM-Co9pwFgHJWUqp8rafj8A"]}, -1], ["cr:8215", ["FDSWowReaction_v7"], {"__rc": ["FDSWowReaction_v7", "Aa3BKGn9DE82mhvcCyukCmGRohT5CDKOXSLCf-o4SqD6O-gJ9hZ3ONuzooSlbe1ytjyOLCRJLvWtXOMbo6fglh8AJ0obZLhtemEfftBm3hw9NMnkvidxmpODDM-Co9pwFgHJWUqp8rafj8A"]}, -1], ["FbtQTOverrides", [], {"overrides": {}}, 551], ["DynamicUFIReactionTypes", [], {"reactions": {"****************": {"color": "#0866FF", "display_name": "Like", "is_deprecated": false, "is_visible": true, "name": "like", "type": 1}, "1678524932434102": {"color": "#f33e58", "display_name": "Love", "is_deprecated": false, "is_visible": true, "name": "love", "type": 2}, "869508936487422": {"color": "#f0ba15", "display_name": "<PERSON><PERSON>", "is_deprecated": false, "is_visible": false, "name": "selfie", "type": 13}, "1663186627268800": {"color": "#7e64c4", "display_name": "Grateful", "is_deprecated": false, "is_visible": true, "name": "dorothy", "type": 11}, "899779720071651": {"color": "#ec7ebd", "display_name": "Pride", "is_deprecated": false, "is_visible": true, "name": "toto", "type": 12}, "115940658764963": {"color": "#f7b125", "display_name": "<PERSON><PERSON>", "is_deprecated": false, "is_visible": true, "name": "haha", "type": 4}, "1667835766830853": {"color": "#f0ba15", "display_name": "Yay", "is_deprecated": true, "is_visible": false, "name": "yay", "type": 5}, "478547315650144": {"color": "#f7b125", "display_name": "Wow", "is_deprecated": false, "is_visible": true, "name": "wow", "type": 3}, "1536130110011063": {"color": "#f0ba15", "display_name": "Confused", "is_deprecated": true, "is_visible": false, "name": "confused", "type": 10}, "613557422527858": {"color": "#f7b125", "display_name": "Care", "is_deprecated": false, "is_visible": true, "name": "support", "type": 16}, "908563459236466": {"color": "#f7b125", "display_name": "Sad", "is_deprecated": false, "is_visible": true, "name": "sorry", "type": 7}, "444813342392137": {"color": "#e9710f", "display_name": "Angry", "is_deprecated": false, "is_visible": true, "name": "anger", "type": 8}, "938644726258608": {"color": "#f0ba15", "display_name": "React", "is_deprecated": true, "is_visible": false, "name": "flame", "type": 14}, "1609920819308489": {"color": "#f0ba15", "display_name": "React", "is_deprecated": true, "is_visible": false, "name": "plane", "type": 15}}, "ordering": ["****************", "1678524932434102", "115940658764963", "478547315650144", "908563459236466", "444813342392137"]}, 6492], ["LSD", [], {"token": "AVpUK7LRnxM"}, 323], ["InitialCookieConsent", [], {"deferCookies": false, "initialConsent": [1, 2], "noCookies": false, "shouldShowCookieBanner": false, "shouldWaitForDeferredDatrCookie": false, "optedInIntegrations": ["adobe_marketo_rest_api", "blings_io_video", "chili_piper_api", "cloudfront_cdn", "giphy_media", "google_ads_pixel_frame_legacy", "google_ads_pixel_img_legacy", "google_ads_pixel_legacy", "google_ads_remarketing_tag", "google_ads_services", "google_analytics_4_tag", "google_analytics_img", "google_cached_img", "google_double_click_loading", "google_double_click_redirecting", "google_double_click_uri_connect", "google_double_click_uri_frame", "google_double_click_uri_img", "google_fonts", "google_fonts_font", "google_maps", "google_paid_ads_frame", "google_paid_ads_img", "google_translate", "google_universal_analytics_legacy", "google_universal_analytics_legacy_img", "google_universal_analytics_legacy_script", "jio", "linkedin_insight", "linkedin_insight_img", "mapbox_maps_api", "medallia_digital_experience_analytics", "microsoft_exchange", "nytimes_oembed", "reachtheworld_s3", "soundcloud_oembed", "spotify_oembed", "spreaker_oembed", "ted_oembed", "tenor_api", "tenor_images", "tenor_media", "tiktok_oembed", "twitter_analytics_pixel", "twitter_analytics_pixel_img", "twitter_legacy_embed", "vimeo_oembed", "youtube_embed", "youtube_oembed", "advertiser_hosted_pixel", "airbus_sat", "amazon_media", "apps_for_office", "ark<PERSON>_captcha", "aspnet_cdn", "autodesk_fusion", "bing_maps", "bing_widget", "boku_wallet", "bootstrap", "box", "cardinal_centinel_api", "chromecast_extensions", "cloudflare_cdnjs", "cloudflare_datatables", "cloudflare_relay", "conversions_api_gateway", "demandbase_api", "digitalglobe_maps_api", "dlocal", "dropbox", "esri_sat", "facebook_sdk", "fastly_relay", "gmg_pulse_embed_iframe", "google_ads_conversions_tag", "google_drive", "google_fonts_legacy", "google_hosted_libraries", "google_oauth_api", "google_oauth_api_v2", "google_recaptcha", "here_map_ext", "hive_streaming_video", "iproov", "isptoolbox", "j<PERSON>y", "js_delivr", "kbank", "mathjax", "meshy", "meta_pixel", "metacdn", "microsoft_excel", "microsoft_office_addin", "microsoft_onedrive", "microsoft_speech", "microsoft_teams", "mmi_tiles", "oculus", "open_street_map", "paypal_billing_agreement", "paypal_oauth_api", "payu", "payu_india", "plaid", "platformized_adyen_checkout", "plotly", "pydata", "razorpay", "recruitics", "rstudio", "salesforce_lighting", "shopify_app_bridge", "stripe", "team_center", "tripshot", "trustly_direct_debit_ach", "twilio_voice", "unifier", "unpkg", "unsplash_api", "unsplash_image_loading", "vega", "yoti_api", "youtube_oembed_api", "google_apis", "google_apis_scripts", "google_img", "google_tag", "google_uri_frame", "google_uri_script"], "hasGranularThirdPartyCookieConsent": true, "exemptedIntegrations": ["advertiser_hosted_pixel", "airbus_sat", "amazon_media", "apps_for_office", "ark<PERSON>_captcha", "aspnet_cdn", "autodesk_fusion", "bing_maps", "bing_widget", "boku_wallet", "bootstrap", "box", "cardinal_centinel_api", "chromecast_extensions", "cloudflare_cdnjs", "cloudflare_datatables", "cloudflare_relay", "conversions_api_gateway", "demandbase_api", "digitalglobe_maps_api", "dlocal", "dropbox", "esri_sat", "facebook_sdk", "fastly_relay", "gmg_pulse_embed_iframe", "google_ads_conversions_tag", "google_drive", "google_fonts_legacy", "google_hosted_libraries", "google_oauth_api", "google_oauth_api_v2", "google_recaptcha", "here_map_ext", "hive_streaming_video", "iproov", "isptoolbox", "j<PERSON>y", "js_delivr", "kbank", "mathjax", "meshy", "meta_pixel", "metacdn", "microsoft_excel", "microsoft_office_addin", "microsoft_onedrive", "microsoft_speech", "microsoft_teams", "mmi_tiles", "oculus", "open_street_map", "paypal_billing_agreement", "paypal_oauth_api", "payu", "payu_india", "plaid", "platformized_adyen_checkout", "plotly", "pydata", "razorpay", "recruitics", "rstudio", "salesforce_lighting", "shopify_app_bridge", "stripe", "team_center", "tripshot", "trustly_direct_debit_ach", "twilio_voice", "unifier", "unpkg", "unsplash_api", "unsplash_image_loading", "vega", "yoti_api", "youtube_oembed_api"]}, 4328], ["DynamicUFIReactionsKeyframesAssets", [], {"likeAction": "https://static.xx.fbcdn.net/rsrc.php/yg/r/DUjm2v1u572.kf", "reactions": {"****************": "https://scontent.fdac145-1.fna.fbcdn.net/m1/v/t6/An_KOWvmE8xXhWbKcEDamiAQ14ZKS7T_w5aOTf-M8Krv4ls-f63eecRjpMEIRfwoMQw0XjeM4Q2PKoqtUSQc0_q66s2ahAGa0OTb.kf?_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&_nc_oc=AdmVyq_X7P6rS0jbEPvXFH6KYKHqzSErvV_UkOrgCx3e8n_licn7yTWHZH6jPzauYT0&ccb=10-5&oh=00_AfSKwX6iadfhJhqBjWmLyqSJ9nKw2YEtydHVeMtykqzB9A&oe=689C7F80&_nc_sid=7da55a", "1678524932434102": "https://scontent.fdac145-1.fna.fbcdn.net/m1/v/t6/An-V1eo7VQ3O9lqK2f9nfo3p019W0b_BTfQk8m_WliRJfm8Ss0I6dO39oKJmajreEZ2Oy5vSrTbDg0GpAgj0NSk-Eu5PWtSAd1gv.kf?_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&_nc_oc=AdmhVcGRjkbiQp6NvGkd6u94HZ6zd5OjcQ8zMJ1VM_JW89MxOcofk68Vwj5VkPPGhOc&ccb=10-5&oh=00_AfQk2VG2_L_PGk48w0nxX3EBVyqFKpD67jiLudxy0QEPuA&oe=689C7BF1&_nc_sid=7da55a", "869508936487422": "https://scontent.fdac145-1.fna.fbcdn.net/m1/v/t6/An9Cuq6V87XCyHLJJY_ShXqNBW-2UyqhK_6ILdKkTwZUbkYibQgRyeACQQbFarakGTo9gsk12z5mO9pMjFSdmYj9afztT3jPu-wUddjp.kf?_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&_nc_oc=AdlIglvGfgdJN8Ru5M5H3MxzlMEylZJ88RxuyjAlWCaIEJK82rDr0iO5R--dRa1CDkY&ccb=10-5&oh=00_AfQxcBpF4ZUzyJ99RkJpU7Sv90E4VoxjxZWURDZ6JXBDLg&oe=689C8F03&_nc_sid=7da55a", "1663186627268800": "https://scontent.fdac145-1.fna.fbcdn.net/m1/v/t6/An8KSKhioMIUVSZ4NYh8JnAchGmGQVDA71Hulkn5JQZsO-ejSYcV2dWPMQo-ZhSa71MfFqgrDHlCASWFxgbtV3pYQqVdSus0zicQO6_n.kf?_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&_nc_oc=Adl4-oz1jcQftPLz1uWc8fWSgWnTsLn-B-No7TcYR-NJmVBfL1J8SGK83N9R4jVK_BQ&ccb=10-5&oh=00_AfTY98ypDLtyfjIsCxBF_Jt0MTVErBMjtJOjgisWtZ8UlA&oe=689C7F66&_nc_sid=7da55a", "899779720071651": "https://scontent.fdac145-1.fna.fbcdn.net/m1/v/t6/An8UpDtYTxwRfyFUAQkYpPLVzCcFZAyT58b4mM9QRn-9IZWdHfcWsX1lOuHAsupZu8HCFEX8uobe6VdR6H9B8v2Z_FzSMfB8MYN36w.kf?_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&_nc_oc=AdmTz_qpnzXWtDv7l6pmLlglVGW4a28vOiEaJ0u_dTq875YWePm8NNX0iYNfBtiZMcw&ccb=10-5&oh=00_AfSbEzyDIviGnT_nMuOKyuLlNVbOtKxQ95O-4v1k568Tpg&oe=689C9A30&_nc_sid=7da55a", "115940658764963": "https://scontent.fdac145-1.fna.fbcdn.net/m1/v/t6/An8CYKC8XqtM-fkmXlVHrp9oVgPgDVVlGZu_Cy7Q_5xW9yYY3lOzMNVDCq9D1ThbbsbnpjfFmGnfIPhKX6oePvUMOYOvShQauKw.kf?_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&_nc_oc=AdkZ2SVkGtbFyAtxcGZ_I-nxUygOiCJci_k96BOxYNVgYLlWz4xt4SNJ87nK2Wpe5ps&ccb=10-5&oh=00_AfQRUfdIJyIDJ2AvhQCaxTYcrPU7RDnibzycGuWEBKSoBw&oe=689C83CB&_nc_sid=7da55a", "478547315650144": "https://scontent.fdac145-1.fna.fbcdn.net/m1/v/t6/An_iJw3Cc3y5RtzEpR0M4CCznWN_ywjtFHZENvSbcomn6tH9EuRIjlfe7xaIpEOIEZAGHfQMVNlOPpkGNsG7fM8CslunKANTZ6ED.kf?_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&_nc_oc=AdlnuRb1ywoHlXRx6pErXNWOmJTWmSor0Fv2_NjaEeBZ5vKY2EBjGpsFD9trYAZijgQ&ccb=10-5&oh=00_AfQieJpkpsahZck3KOaIFynYQQq8UsDQj54a6ojHFvEhTA&oe=689C9018&_nc_sid=7da55a", "613557422527858": "https://scontent.fdac145-1.fna.fbcdn.net/m1/v/t6/An-epsBkaSJ4n9oYH6L6fSPKlfyvVNR_J7OfeV9-jqOZVx9-Fg73T8WyLTa1e_4iyoSboul40f-XChEvL3RtR-jyPuVfx7Umn4RlQyMS.kf?_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&_nc_oc=AdlBWwihMnsPTZk9Lo-4zKR0eN3JaEC6qjFxRiruk_vWkNjzpCAjJlz1QMBHtv3UIog&ccb=10-5&oh=00_AfQmRhe-oX7I0zRzLTB2vOb_7k7hyK_sC_mILJlvsMMrwQ&oe=689C7947&_nc_sid=7da55a", "908563459236466": "https://scontent.fdac145-1.fna.fbcdn.net/m1/v/t6/An-4eutqFWFfYhZPQR8tM2oP5E05jWW-J7tvfsD5wE23SUHSA2nXlg1n6fI_KpO3JoITC05bkD6cfuSRG3Fji57z_i1jO4Bw_K_ZEg.kf?_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&_nc_oc=AdmtcbIfQX6974YrpgkWfbaVVF-RXs2q-eDqdU1QaOQt9ibHc7HAdGNyGoMVnbFh-QI&ccb=10-5&oh=00_AfTaouFhVFad4FgAqsuMvRKogkKb3ZLF6TkcPywke7hdjQ&oe=689C7FB0&_nc_sid=7da55a", "444813342392137": "https://scontent.fdac145-1.fna.fbcdn.net/m1/v/t6/An-ltDiBj6BlExJAIyJiOGWs0CtdQwF9K9SyRSRhTIMgJd0MMzaw7ju3gnTsliPfba99uYjQem5sn3JzgpEnBVKOKfyfbcp-sMBJ.kf?_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&_nc_oc=AdnrWkp0PKMIg7rl3vnb-HK1cThv6TM3xMh6RWxa4n61p9uFaOL8c09fFZkZ5cz9rvI&ccb=10-5&oh=00_AfS4laK31JpUwBzEI7ao3pmGDESAOaUmbuOPTfq15IMS3w&oe=689C9C97&_nc_sid=7da55a"}, "initialProgress": {"115940658764963": 0.37, "908563459236466": 0.75}}, 6513], ["AnalyticsCoreData", [], {"device_id": "$^|AcbvJcCB3Hgr-cjniX6iW6s1KqaHX1umRNM-Ca92h8pdCsDGJRadlU9Zh4lZ7WMCO7KQzXeylhNtG-0bwLpKCoy_HoS_|fd.AcbPImQJIXmrqXjjf882AZO2sdI12gLRofG4F1wkfH0eW5dPc-ZCNygF6UkVV-85bygvdxmvUOYDseCC34KnBLHu", "app_id": "************", "enable_bladerunner": false, "enable_ack": true, "push_phase": "C3", "enable_observer": false, "enable_cmcd_observer": false, "enable_dataloss_timer": false, "enable_fallback_for_br": true, "queue_activation_experiment": false, "max_delay_br_queue": 60000, "max_delay_br_queue_immediate": 3, "max_delay_br_init_not_complete": 3000, "consents": {}, "app_universe": 1, "br_stateful_migration_on": true, "enable_non_fb_br_stateless_by_default": false, "use_falco_as_mutex_key": false, "is_intern": false, "enable_session_id_bug_fix": true}, 5237]], "require": [["CometFeedStoryDefaultContentStrategy_content$normalization.graphql"], ["CometFeedStoryDefaultContentStrategy.react"], ["CometFeedStoryDefaultLayoutStrategy_layout$normalization.graphql"], ["CometFeedStoryDefaultLayoutStrategy.react"], ["CometFeedStoryDefaultContextLayoutStrategy_contextLayout$normalization.graphql"], ["CometFeedStoryDefaultContextLayoutStrategy.react"], ["CometFeedStoryFeedbackUFIStrategy_feedback$normalization.graphql"], ["CometFeedStoryFeedbackUFIStrategy.react"], ["CometFeedStoryDefaultCallToActionStrategy_cta$normalization.graphql"], ["CometFeedStoryDefaultCallToActionStrategy.react"], ["CometFeedUnitStoryStrategy_feedUnit$normalization.graphql"], ["CometFeedUnitStoryStrategy.react"], ["CometFeedStoryTitleWithActorStrategy_contextTitle$normalization.graphql"], ["CometFeedStoryTitleWithActorStrategy.react"], ["CometUFIAdaptiveShareActionRenderer_story$normalization.graphql"], ["CometUFIAdaptiveShareActionRenderer.react"], ["CometSimplifiedUFIContainer_renderer$normalization.graphql"], ["CometSimplifiedUFIContainer.react"], ["CometFeedStoryMessageContainerRenderingStrategy_messageContainer$normalization.graphql"], ["CometFeedStoryMessageContainerRenderingStrategy.react"], ["CometFeedStoryMinimizedTimestampStrategy_timestamp$normalization.graphql"], ["CometFeedStoryMinimizedTimestampStrategy.react"], ["ProfileGeminiWeakReferenceLink_user$normalization.graphql"], ["ProfileGeminiWeakReferenceLink.react"], ["CometFeedStoryDefaultMessageRenderingStrategy_message$normalization.graphql"], ["CometFeedStoryDefaultMessageRenderingStrategy.react"], ["CometFeedStoryPhotoAttachmentStyle_styleTypeRenderer$normalization.graphql"], ["CometFeedStoryPhotoAttachmentStyle.react"], ["CometFeedStoryActorPhotoStrategy_actorPhoto$normalization.graphql"], ["CometFeedStoryActorPhotoStrategy.react"], ["CometUFIReactionSentence_reactionDisplayConfig$normalization.graphql"], ["CometUFIReactionSentence.react"], ["CometUFISeenByCountText_feedback$normalization.graphql"], ["CometUFISeenByCountText.react"], ["CometUFICommentsCountRenderer_data$normalization.graphql"], ["CometUFICommentsCountRenderer.react"], ["CometUnauthenticatedUFISummaryAndActionsRenderer_feedback$normalization.graphql"], ["CometUnauthenticatedUFISummaryAndActionsRenderer.react"], ["cometUFIComposerMentionsPlugin_plugin$normalization.graphql"], ["cometUFIComposerMentionsPlugin"], ["cometUFIComposerHashtagPlugin_plugin$normalization.graphql"], ["cometUFIComposerHashtagPlugin"], ["cometUFIComposerEmojiPlugin_plugin$normalization.graphql"], ["cometUFIComposerEmojiPlugin"], ["cometUFIComposerEmoticonPlugin_plugin$normalization.graphql"], ["cometUFIComposerEmoticonPlugin"], ["cometUFIComposerPrefillMentionPlugin_plugin$normalization.graphql"], ["cometUFIComposerPrefillMentionPlugin"], ["cometUFIComposerAssociateReplyWithParentPlugin_plugin$normalization.graphql"], ["cometUFIComposerAssociateReplyWithParentPlugin"], ["cometUFIComposerSetReplyClickedPlugin_plugin$normalization.graphql"], ["cometUFIComposerSetReplyClickedPlugin"], ["cometUFIComposerStateSnapshotPlugin_plugin$normalization.graphql"], ["cometUFIComposerStateSnapshotPlugin"], ["cometUFIComposerCommentCharacterLimitPlugin_plugin$normalization.graphql"], ["cometUFIComposerCommentCharacterLimitPlugin"], ["cometUFIComposerWriteToComposerPlugin_plugin$normalization.graphql"], ["cometUFIComposerWriteToComposerPlugin"], ["CometGenericCommentDisableNotice_commentDisableNotice$normalization.graphql"], ["CometGenericCommentDisableNotice.react"], ["CometFeedStoryAudienceStrategy_audience$normalization.graphql"], ["CometFeedStoryAudienceStrategy.react"], ["CometUFICommentTimeStampActionLink_commentActionLink$normalization.graphql"], ["CometUFICommentTimeStampActionLink.react"], ["CometUFICommentBodyTextWithEntities_textWithEntities$normalization.graphql"], ["CometUFICommentBodyTextWithEntities.react"], ["emptyFunction", "thatReturns", ["RequireDeferredReference"], [[{"__dr": "CometFeedStoryDefaultContentStrategy_content$normalization.graphql"}, {"__dr": "CometFeedStoryDefaultContentStrategy.react"}, {"__dr": "CometFeedStoryDefaultLayoutStrategy_layout$normalization.graphql"}, {"__dr": "CometFeedStoryDefaultLayoutStrategy.react"}, {"__dr": "CometFeedStoryDefaultContextLayoutStrategy_contextLayout$normalization.graphql"}, {"__dr": "CometFeedStoryDefaultContextLayoutStrategy.react"}, {"__dr": "CometFeedStoryFeedbackUFIStrategy_feedback$normalization.graphql"}, {"__dr": "CometFeedStoryFeedbackUFIStrategy.react"}, {"__dr": "CometFeedStoryDefaultCallToActionStrategy_cta$normalization.graphql"}, {"__dr": "CometFeedStoryDefaultCallToActionStrategy.react"}, {"__dr": "CometFeedUnitStoryStrategy_feedUnit$normalization.graphql"}, {"__dr": "CometFeedUnitStoryStrategy.react"}, {"__dr": "CometFeedStoryTitleWithActorStrategy_contextTitle$normalization.graphql"}, {"__dr": "CometFeedStoryTitleWithActorStrategy.react"}, {"__dr": "CometUFIAdaptiveShareActionRenderer_story$normalization.graphql"}, {"__dr": "CometUFIAdaptiveShareActionRenderer.react"}, {"__dr": "CometSimplifiedUFIContainer_renderer$normalization.graphql"}, {"__dr": "CometSimplifiedUFIContainer.react"}, {"__dr": "CometFeedStoryMessageContainerRenderingStrategy_messageContainer$normalization.graphql"}, {"__dr": "CometFeedStoryMessageContainerRenderingStrategy.react"}, {"__dr": "CometFeedStoryMinimizedTimestampStrategy_timestamp$normalization.graphql"}, {"__dr": "CometFeedStoryMinimizedTimestampStrategy.react"}, {"__dr": "ProfileGeminiWeakReferenceLink_user$normalization.graphql"}, {"__dr": "ProfileGeminiWeakReferenceLink.react"}, {"__dr": "CometFeedStoryDefaultMessageRenderingStrategy_message$normalization.graphql"}, {"__dr": "CometFeedStoryDefaultMessageRenderingStrategy.react"}, {"__dr": "CometFeedStoryPhotoAttachmentStyle_styleTypeRenderer$normalization.graphql"}, {"__dr": "CometFeedStoryPhotoAttachmentStyle.react"}, {"__dr": "CometFeedStoryActorPhotoStrategy_actorPhoto$normalization.graphql"}, {"__dr": "CometFeedStoryActorPhotoStrategy.react"}, {"__dr": "CometUFIReactionSentence_reactionDisplayConfig$normalization.graphql"}, {"__dr": "CometUFIReactionSentence.react"}, {"__dr": "CometUFISeenByCountText_feedback$normalization.graphql"}, {"__dr": "CometUFISeenByCountText.react"}, {"__dr": "CometUFICommentsCountRenderer_data$normalization.graphql"}, {"__dr": "CometUFICommentsCountRenderer.react"}, {"__dr": "CometUnauthenticatedUFISummaryAndActionsRenderer_feedback$normalization.graphql"}, {"__dr": "CometUnauthenticatedUFISummaryAndActionsRenderer.react"}, {"__dr": "cometUFIComposerMentionsPlugin_plugin$normalization.graphql"}, {"__dr": "cometUFIComposerMentionsPlugin"}, {"__dr": "cometUFIComposerHashtagPlugin_plugin$normalization.graphql"}, {"__dr": "cometUFIComposerHashtagPlugin"}, {"__dr": "cometUFIComposerEmojiPlugin_plugin$normalization.graphql"}, {"__dr": "cometUFIComposerEmojiPlugin"}, {"__dr": "cometUFIComposerEmoticonPlugin_plugin$normalization.graphql"}, {"__dr": "cometUFIComposerEmoticonPlugin"}, {"__dr": "cometUFIComposerPrefillMentionPlugin_plugin$normalization.graphql"}, {"__dr": "cometUFIComposerPrefillMentionPlugin"}, {"__dr": "cometUFIComposerAssociateReplyWithParentPlugin_plugin$normalization.graphql"}, {"__dr": "cometUFIComposerAssociateReplyWithParentPlugin"}, {"__dr": "cometUFIComposerSetReplyClickedPlugin_plugin$normalization.graphql"}, {"__dr": "cometUFIComposerSetReplyClickedPlugin"}, {"__dr": "cometUFIComposerStateSnapshotPlugin_plugin$normalization.graphql"}, {"__dr": "cometUFIComposerStateSnapshotPlugin"}, {"__dr": "cometUFIComposerCommentCharacterLimitPlugin_plugin$normalization.graphql"}, {"__dr": "cometUFIComposerCommentCharacterLimitPlugin"}, {"__dr": "cometUFIComposerWriteToComposerPlugin_plugin$normalization.graphql"}, {"__dr": "cometUFIComposerWriteToComposerPlugin"}, {"__dr": "CometGenericCommentDisableNotice_commentDisableNotice$normalization.graphql"}, {"__dr": "CometGenericCommentDisableNotice.react"}, {"__dr": "CometFeedStoryAudienceStrategy_audience$normalization.graphql"}, {"__dr": "CometFeedStoryAudienceStrategy.react"}, {"__dr": "CometUFICommentTimeStampActionLink_commentActionLink$normalization.graphql"}, {"__dr": "CometUFICommentTimeStampActionLink.react"}, {"__dr": "CometUFICommentBodyTextWithEntities_textWithEntities$normalization.graphql"}, {"__dr": "CometUFICommentBodyTextWithEntities.react"}]]], ["ghlTestUBT"], ["CometFeedStoriesStrategyErrorsTypedLoggerLite"], ["FbtLogging"], ["CometSuspenseFalcoEvent"], ["IntlQtEventFalcoEvent"], ["FDSTooltipDeferredImpl.react"], ["ContextualConfig"], ["BladeRunnerClient"], ["QpActionFalcoEvent"], ["QpImpressionFalcoEvent"], ["ContentAccessAndControlEventType"], ["ContentAccessAndControlFalcoEvent"], ["DGWRequestStreamClient"], ["CometToast.react"], ["bumpVultureJSHash"], ["CometRelayEF"], ["DelightsTextTriggerPostTappedFalcoEvent"], ["CometUFIFunnelLogger"], ["CometGroupsPathingFunnelLogger"], ["MLCInstrumentationPlugin__INTERNAL.react"], ["GroupAnonymousPostUserEventsFalcoEvent"], ["CometUFICommentErrorFooter.react"], ["CometUFIReactionsAnimationPreloader"], ["CometUFIThreadedComponentDecorator.react"], ["WhatsappDeeplinkClickedFalcoEvent"], ["WhatsappDeeplinkRenderedFalcoEvent"], ["ActnSellerTagProductPillClickFalcoEvent"], ["ConsumptionFocusPointVpvFalcoEvent"], ["CommerceTabFeedImpressionFalcoEvent"], ["CommerceStorefrontImpressionFalcoEvent"], ["CommercePdpProductTagClickFalcoEvent"], ["CometUFIVoteCountButton.react"], ["FbSharingEventFalcoEvent"], ["CometUFIFeedbackReactSubscription"], ["CometTooltipDeferredImpl.react"], ["createUpgradedUFI2MentionsComposerPluginForLexical"], ["getUpgradedCometHashtagComposerHandler"], ["getUpgradedCometEmojiComposerHandler"], ["getUpgradedCometUFIEmoticonsComposerHandler"], ["UFIOD<PERSON>og<PERSON>"], ["FDSAlertDialogImpl.react"], ["CometUfiCommentActionLinksFalcoEvent"], ["MqttLongPollingRunner"], ["CometExceptionDialog.react"], ["KeyframesRenderer"], ["FBKeyframesLoggedSession"], ["KeyframesAssetDecoder"], ["cometPushToast"], ["ODS"], ["groupsCometFetchStory"], ["CometFeedStoryOptimisticWithStory.react"], ["CometUFICommentActorConstituentBadge.react"], ["CometUFICommentActorVerifiedBadge.react"], ["CometUFICommentInlineFollowCTA.react"], ["CometFeedCommentsDialogCloseRefreshFalcoEvent"], ["cometComposerQPLLogger"], ["InlineFollowCtaEventFalcoEvent"], ["json-bigint"], ["RequireDeferredReference", "unblock", [], [["CometFeedStoryDefaultContentStrategy_content$normalization.graphql", "CometFeedStoryDefaultContentStrategy.react", "CometFeedStoryDefaultLayoutStrategy_layout$normalization.graphql", "CometFeedStoryDefaultLayoutStrategy.react", "CometFeedStoryDefaultContextLayoutStrategy_contextLayout$normalization.graphql", "CometFeedStoryDefaultContextLayoutStrategy.react", "CometFeedStoryFeedbackUFIStrategy_feedback$normalization.graphql", "CometFeedStoryFeedbackUFIStrategy.react", "CometFeedStoryDefaultCallToActionStrategy_cta$normalization.graphql", "CometFeedStoryDefaultCallToActionStrategy.react", "CometFeedUnitStoryStrategy_feedUnit$normalization.graphql", "CometFeedUnitStoryStrategy.react", "CometFeedStoryTitleWithActorStrategy_contextTitle$normalization.graphql", "CometFeedStoryTitleWithActorStrategy.react", "CometUFIAdaptiveShareActionRenderer_story$normalization.graphql", "CometUFIAdaptiveShareActionRenderer.react", "CometSimplifiedUFIContainer_renderer$normalization.graphql", "CometSimplifiedUFIContainer.react", "CometFeedStoryMessageContainerRenderingStrategy_messageContainer$normalization.graphql", "CometFeedStoryMessageContainerRenderingStrategy.react", "CometFeedStoryMinimizedTimestampStrategy_timestamp$normalization.graphql", "CometFeedStoryMinimizedTimestampStrategy.react", "ProfileGeminiWeakReferenceLink_user$normalization.graphql", "ProfileGeminiWeakReferenceLink.react", "CometFeedStoryDefaultMessageRenderingStrategy_message$normalization.graphql", "CometFeedStoryDefaultMessageRenderingStrategy.react", "CometFeedStoryPhotoAttachmentStyle_styleTypeRenderer$normalization.graphql", "CometFeedStoryPhotoAttachmentStyle.react", "CometFeedStoryActorPhotoStrategy_actorPhoto$normalization.graphql", "CometFeedStoryActorPhotoStrategy.react", "CometUFIReactionSentence_reactionDisplayConfig$normalization.graphql", "CometUFIReactionSentence.react", "CometUFISeenByCountText_feedback$normalization.graphql", "CometUFISeenByCountText.react", "CometUFICommentsCountRenderer_data$normalization.graphql", "CometUFICommentsCountRenderer.react", "CometUnauthenticatedUFISummaryAndActionsRenderer_feedback$normalization.graphql", "CometUnauthenticatedUFISummaryAndActionsRenderer.react", "cometUFIComposerMentionsPlugin_plugin$normalization.graphql", "cometUFIComposerMentionsPlugin", "cometUFIComposerHashtagPlugin_plugin$normalization.graphql", "cometUFIComposerHashtagPlugin", "cometUFIComposerEmojiPlugin_plugin$normalization.graphql", "cometUFIComposerEmojiPlugin", "cometUFIComposerEmoticonPlugin_plugin$normalization.graphql", "cometUFIComposerEmoticonPlugin", "cometUFIComposerPrefillMentionPlugin_plugin$normalization.graphql", "cometUFIComposerPrefillMentionPlugin", "cometUFIComposerAssociateReplyWithParentPlugin_plugin$normalization.graphql", "cometUFIComposerAssociateReplyWithParentPlugin", "cometUFIComposerSetReplyClickedPlugin_plugin$normalization.graphql", "cometUFIComposerSetReplyClickedPlugin", "cometUFIComposerStateSnapshotPlugin_plugin$normalization.graphql", "cometUFIComposerStateSnapshotPlugin", "cometUFIComposerCommentCharacterLimitPlugin_plugin$normalization.graphql", "cometUFIComposerCommentCharacterLimitPlugin", "cometUFIComposerWriteToComposerPlugin_plugin$normalization.graphql", "cometUFIComposerWriteToComposerPlugin", "CometGenericCommentDisableNotice_commentDisableNotice$normalization.graphql", "CometGenericCommentDisableNotice.react", "CometFeedStoryAudienceStrategy_audience$normalization.graphql", "CometFeedStoryAudienceStrategy.react", "CometUFICommentTimeStampActionLink_commentActionLink$normalization.graphql", "CometUFICommentTimeStampActionLink.react", "CometUFICommentBodyTextWithEntities_textWithEntities$normalization.graphql", "CometUFICommentBodyTextWithEntities.react", "FDSTooltipDeferredImpl.react", "CometUFIThreadedComponentDecorator.react", "CometTooltipDeferredImpl.react"], "sd"]], ["RequireDeferredReference", "unblock", [], [["ghlTestUBT", "CometFeedStoriesStrategyErrorsTypedLoggerLite", "FbtLogging", "CometSuspenseFalcoEvent", "IntlQtEventFalcoEvent", "ContextualConfig", "BladeRunnerClient", "QpActionFalcoEvent", "QpImpressionFalcoEvent", "ContentAccessAndControlEventType", "ContentAccessAndControlFalcoEvent", "DGWRequestStreamClient", "CometToast.react", "bumpVultureJSHash", "CometRelayEF", "DelightsTextTriggerPostTappedFalcoEvent", "CometUFIFunnelLogger", "CometGroupsPathingFunnelLogger", "MLCInstrumentationPlugin__INTERNAL.react", "GroupAnonymousPostUserEventsFalcoEvent", "CometUFICommentErrorFooter.react", "CometUFIReactionsAnimationPreloader", "WhatsappDeeplinkClickedFalcoEvent", "WhatsappDeeplinkRenderedFalcoEvent", "ActnSellerTagProductPillClickFalcoEvent", "ConsumptionFocusPointVpvFalcoEvent", "CommerceTabFeedImpressionFalcoEvent", "CommerceStorefrontImpressionFalcoEvent", "CommercePdpProductTagClickFalcoEvent", "CometUFIVoteCountButton.react", "FbSharingEventFalcoEvent", "CometUFIFeedbackReactSubscription", "createUpgradedUFI2MentionsComposerPluginForLexical", "getUpgradedCometHashtagComposerHandler", "getUpgradedCometEmojiComposerHandler", "getUpgradedCometUFIEmoticonsComposerHandler", "UFIOD<PERSON>og<PERSON>", "FDSAlertDialogImpl.react", "CometUfiCommentActionLinksFalcoEvent", "MqttLongPollingRunner", "CometExceptionDialog.react", "KeyframesRenderer", "FBKeyframesLoggedSession", "KeyframesAssetDecoder", "cometPushToast", "ODS", "groupsCometFetchStory", "CometFeedStoryOptimisticWithStory.react", "CometUFICommentActorConstituentBadge.react", "CometUFICommentActorVerifiedBadge.react", "CometUFICommentInlineFollowCTA.react", "CometFeedCommentsDialogCloseRefreshFalcoEvent", "cometComposerQPLLogger", "InlineFollowCtaEventFalcoEvent", "json-bigint"], "sd"]], ["RequireDeferredReference", "unblock", [], [["CometFeedStoryDefaultContentStrategy_content$normalization.graphql", "CometFeedStoryDefaultContentStrategy.react", "CometFeedStoryDefaultLayoutStrategy_layout$normalization.graphql", "CometFeedStoryDefaultLayoutStrategy.react", "CometFeedStoryDefaultContextLayoutStrategy_contextLayout$normalization.graphql", "CometFeedStoryDefaultContextLayoutStrategy.react", "CometFeedStoryFeedbackUFIStrategy_feedback$normalization.graphql", "CometFeedStoryFeedbackUFIStrategy.react", "CometFeedStoryDefaultCallToActionStrategy_cta$normalization.graphql", "CometFeedStoryDefaultCallToActionStrategy.react", "CometFeedUnitStoryStrategy_feedUnit$normalization.graphql", "CometFeedUnitStoryStrategy.react", "CometFeedStoryTitleWithActorStrategy_contextTitle$normalization.graphql", "CometFeedStoryTitleWithActorStrategy.react", "CometUFIAdaptiveShareActionRenderer_story$normalization.graphql", "CometUFIAdaptiveShareActionRenderer.react", "CometSimplifiedUFIContainer_renderer$normalization.graphql", "CometSimplifiedUFIContainer.react", "CometFeedStoryMessageContainerRenderingStrategy_messageContainer$normalization.graphql", "CometFeedStoryMessageContainerRenderingStrategy.react", "CometFeedStoryMinimizedTimestampStrategy_timestamp$normalization.graphql", "CometFeedStoryMinimizedTimestampStrategy.react", "ProfileGeminiWeakReferenceLink_user$normalization.graphql", "ProfileGeminiWeakReferenceLink.react", "CometFeedStoryDefaultMessageRenderingStrategy_message$normalization.graphql", "CometFeedStoryDefaultMessageRenderingStrategy.react", "CometFeedStoryPhotoAttachmentStyle_styleTypeRenderer$normalization.graphql", "CometFeedStoryPhotoAttachmentStyle.react", "CometFeedStoryActorPhotoStrategy_actorPhoto$normalization.graphql", "CometFeedStoryActorPhotoStrategy.react", "CometUFIReactionSentence_reactionDisplayConfig$normalization.graphql", "CometUFIReactionSentence.react", "CometUFISeenByCountText_feedback$normalization.graphql", "CometUFISeenByCountText.react", "CometUFICommentsCountRenderer_data$normalization.graphql", "CometUFICommentsCountRenderer.react", "CometUnauthenticatedUFISummaryAndActionsRenderer_feedback$normalization.graphql", "CometUnauthenticatedUFISummaryAndActionsRenderer.react", "cometUFIComposerMentionsPlugin_plugin$normalization.graphql", "cometUFIComposerMentionsPlugin", "cometUFIComposerHashtagPlugin_plugin$normalization.graphql", "cometUFIComposerHashtagPlugin", "cometUFIComposerEmojiPlugin_plugin$normalization.graphql", "cometUFIComposerEmojiPlugin", "cometUFIComposerEmoticonPlugin_plugin$normalization.graphql", "cometUFIComposerEmoticonPlugin", "cometUFIComposerPrefillMentionPlugin_plugin$normalization.graphql", "cometUFIComposerPrefillMentionPlugin", "cometUFIComposerAssociateReplyWithParentPlugin_plugin$normalization.graphql", "cometUFIComposerAssociateReplyWithParentPlugin", "cometUFIComposerSetReplyClickedPlugin_plugin$normalization.graphql", "cometUFIComposerSetReplyClickedPlugin", "cometUFIComposerStateSnapshotPlugin_plugin$normalization.graphql", "cometUFIComposerStateSnapshotPlugin", "cometUFIComposerCommentCharacterLimitPlugin_plugin$normalization.graphql", "cometUFIComposerCommentCharacterLimitPlugin", "cometUFIComposerWriteToComposerPlugin_plugin$normalization.graphql", "cometUFIComposerWriteToComposerPlugin", "CometGenericCommentDisableNotice_commentDisableNotice$normalization.graphql", "CometGenericCommentDisableNotice.react", "CometFeedStoryAudienceStrategy_audience$normalization.graphql", "CometFeedStoryAudienceStrategy.react", "CometUFICommentTimeStampActionLink_commentActionLink$normalization.graphql", "CometUFICommentTimeStampActionLink.react", "CometUFICommentBodyTextWithEntities_textWithEntities$normalization.graphql", "CometUFICommentBodyTextWithEntities.react", "FDSTooltipDeferredImpl.react", "CometUFIThreadedComponentDecorator.react", "CometTooltipDeferredImpl.react"], "css"]], ["RequireDeferredReference", "unblock", [], [["ghlTestUBT", "CometFeedStoriesStrategyErrorsTypedLoggerLite", "FbtLogging", "CometSuspenseFalcoEvent", "IntlQtEventFalcoEvent", "ContextualConfig", "BladeRunnerClient", "QpActionFalcoEvent", "QpImpressionFalcoEvent", "ContentAccessAndControlEventType", "ContentAccessAndControlFalcoEvent", "DGWRequestStreamClient", "CometToast.react", "bumpVultureJSHash", "CometRelayEF", "DelightsTextTriggerPostTappedFalcoEvent", "CometUFIFunnelLogger", "CometGroupsPathingFunnelLogger", "MLCInstrumentationPlugin__INTERNAL.react", "GroupAnonymousPostUserEventsFalcoEvent", "CometUFICommentErrorFooter.react", "CometUFIReactionsAnimationPreloader", "WhatsappDeeplinkClickedFalcoEvent", "WhatsappDeeplinkRenderedFalcoEvent", "ActnSellerTagProductPillClickFalcoEvent", "ConsumptionFocusPointVpvFalcoEvent", "CommerceTabFeedImpressionFalcoEvent", "CommerceStorefrontImpressionFalcoEvent", "CommercePdpProductTagClickFalcoEvent", "CometUFIVoteCountButton.react", "FbSharingEventFalcoEvent", "CometUFIFeedbackReactSubscription", "createUpgradedUFI2MentionsComposerPluginForLexical", "getUpgradedCometHashtagComposerHandler", "getUpgradedCometEmojiComposerHandler", "getUpgradedCometUFIEmoticonsComposerHandler", "UFIOD<PERSON>og<PERSON>", "FDSAlertDialogImpl.react", "CometUfiCommentActionLinksFalcoEvent", "MqttLongPollingRunner", "CometExceptionDialog.react", "KeyframesRenderer", "FBKeyframesLoggedSession", "KeyframesAssetDecoder", "cometPushToast", "ODS", "groupsCometFetchStory", "CometFeedStoryOptimisticWithStory.react", "CometUFICommentActorConstituentBadge.react", "CometUFICommentActorVerifiedBadge.react", "CometUFICommentInlineFollowCTA.react", "CometFeedCommentsDialogCloseRefreshFalcoEvent", "cometComposerQPLLogger", "InlineFollowCtaEventFalcoEvent", "json-bigint"], "css"]]]}, "allResources": ["eejXjh7", "c1LU2Ag", "zJl4N4d", "WzW470F", "fQlL/9u", "04RdAeA", "Sr6Aahz", "dEY7RHQ", "8WrV1qw", "+zxVnNA", "tRo7x53", "xIwLeog", "0cNVXD5", "r0yfBAv", "3bxzL8e", "OoD1C8w", "+UR2TQl", "UneIqIg", "8ItDKbY", "XMP8fLK", "kQeX049", "+qnDr4D", "fmlBfb1", "F3OAUNE", "b0aAln3", "6a0VWiV", "tYlWRD4", "rpZd0S1", "rHjQ135", "CPtfj1t", "BqErpsc", "8jW6Z3P", "/ABZdos", "SBqDt8y", "BSUsW4j", "BWxeAad", "cl/gDMu", "9bdSDSi", "3e1KAxr", "Al+X9aL", "eaBodOx", "yPjOCCT", "igSbXP4", "S3gsgXq", "B4wSUBY", "TcDdWQV", "dhV7BOC", "vhI7dSU", "w6R9/0W", "SLiUPR7", "YLJ6WbM", "yUqVmZ2", "HLK1Yuz", "H6f46ct", "HLyjvhV", "C9n16BW", "1W5pzvT", "KQ+loWk", "ZD+S2f8"], "tieredResources": {"r": ["eejXjh7", "c1LU2Ag", "zJl4N4d", "WzW470F", "fQlL/9u", "04RdAeA", "Sr6Aahz", "dEY7RHQ", "8WrV1qw", "+zxVnNA", "tRo7x53", "xIwLeog", "0cNVXD5", "r0yfBAv", "3bxzL8e", "OoD1C8w", "+UR2TQl", "UneIqIg", "8ItDKbY", "XMP8fLK", "kQeX049", "+qnDr4D", "fmlBfb1", "F3OAUNE", "b0aAln3", "6a0VWiV", "tYlWRD4", "rpZd0S1", "rHjQ135", "CPtfj1t", "BqErpsc", "8jW6Z3P", "/ABZdos", "SBqDt8y", "BSUsW4j", "BWxeAad", "cl/gDMu", "9bdSDSi", "3e1KAxr", "Al+X9aL", "eaBodOx", "yPjOCCT", "igSbXP4", "S3gsgXq", "B4wSUBY", "TcDdWQV"], "rdfds": [], "rds": ["dhV7BOC", "vhI7dSU", "w6R9/0W", "SLiUPR7", "YLJ6WbM", "yUqVmZ2", "HLK1Yuz", "H6f46ct", "HLyjvhV", "C9n16BW", "1W5pzvT", "KQ+loWk", "ZD+S2f8"]}}}}}, {"label": "ProfileCometTimelineFeed_user$defer$ProfileCometTimelineFeed_user_timeline_list_feed_units$page_info", "path": ["node", "timeline_list_feed_units"], "data": {"page_info": {"has_next_page": true, "end_cursor": "Cg8Ob3JnYW5pY19jdXJzb3IJAAABWkFRSFItWExGMlRFNzdNd3NscVdBbUI5QUJuamF3dXhERWFMV3FVVlNfLUdIWG9BUjVzc012WWlia0ZWazlTN3JHVjJfQ1VBSFh1bTcxdFlWYmFMU2FoUXpjakswbWFyWS16T3gyblI3b0lSbkR0OEZqazEzWXhPYktqcUtVLWJQN3hCbENPNkJaemJoZDVTQk5Lam9Zb2tvbzBJbEh0UFlJTkpLd1Q5cGw0RjIzY3QwZ3N5Sm1XSnc4d3ZCVDFlbUxHMnY1Snl1LWNuTk5ZdnhDMDhFQzRkOXNSNHFlX1BUdm9wS1RUM2FwR3NjZjY4YVNPbS0zSE5GR2xuNmE2cm1oYnA3QkdkQURtS2lCU29UcnlOcGVqdGN5Q0F1c2VmVWd6cW5jN05qWVZvMTdWb011UTM5ODAwd2NRM2FQVURMTGlPNXhiLThzd2t2X2I4dzFVWE8yNG9BbWcPCWFkX2N1cnNvcg4PD2dsb2JhbF9wb3NpdGlvbgIADwZvZmZzZXQCAA8QbGFzdF9hZF9wb3NpdGlvbgL/AQ=="}}, "extensions": {"prefetch_uris": ["https://scontent.fdac145-1.fna.fbcdn.net/v/t39.30808-6/509600711_713436828311872_7457334169256797299_n.jpg?stp=dst-jpg_p526x296_tt6&_nc_cat=105&ccb=1-7&_nc_sid=833d8c&_nc_ohc=EMr3XDCNE34Q7kNvwEJHwin&_nc_oc=AdlEo20DiHFyB-eYTeJAwbdw3LBDEtYtxodcRBCVdK3rb1N4Kb4bM7Leif5iXe6JKHo&_nc_zt=23&_nc_ht=scontent.fdac145-1.fna&_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&oh=00_AfRlIL36ILO6DQfljG0XsV_v-eWpwZXfwOrbW2uzBEdDbA&oe=687AFC26", "https://scontent.fdac145-1.fna.fbcdn.net/v/t39.30808-1/472737163_585277764461113_2559430034335871640_n.jpg?stp=cp0_dst-jpg_s40x40_tt6&_nc_cat=108&ccb=1-7&_nc_sid=1d2534&_nc_ohc=u_KqJyrAl9EQ7kNvwHO6rR-&_nc_oc=AdlrwCu--dwJXhVx1DMmRzd9steZduRwZyiqz7LAlrVjivJiYCCX20S7ngNOwPs2fjE&_nc_zt=24&_nc_ht=scontent.fdac145-1.fna&_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&oh=00_AfTVVIBc9LcIR2D8XXRKaTzIWg7eLIezbD0CoLV1kF27xw&oe=687AF2F5"], "prefetch_uris_v2": [{"uri": "https://scontent.fdac145-1.fna.fbcdn.net/v/t39.30808-6/509600711_713436828311872_7457334169256797299_n.jpg?stp=dst-jpg_p526x296_tt6&_nc_cat=105&ccb=1-7&_nc_sid=833d8c&_nc_ohc=EMr3XDCNE34Q7kNvwEJHwin&_nc_oc=AdlEo20DiHFyB-eYTeJAwbdw3LBDEtYtxodcRBCVdK3rb1N4Kb4bM7Leif5iXe6JKHo&_nc_zt=23&_nc_ht=scontent.fdac145-1.fna&_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&oh=00_AfRlIL36ILO6DQfljG0XsV_v-eWpwZXfwOrbW2uzBEdDbA&oe=687AFC26", "label": null}, {"uri": "https://scontent.fdac145-1.fna.fbcdn.net/v/t39.30808-1/472737163_585277764461113_2559430034335871640_n.jpg?stp=cp0_dst-jpg_s40x40_tt6&_nc_cat=108&ccb=1-7&_nc_sid=1d2534&_nc_ohc=u_KqJyrAl9EQ7kNvwHO6rR-&_nc_oc=AdlrwCu--dwJXhVx1DMmRzd9steZduRwZyiqz7LAlrVjivJiYCCX20S7ngNOwPs2fjE&_nc_zt=24&_nc_ht=scontent.fdac145-1.fna&_nc_gid=KxBeMFeOZbkKq0Om3xoFfA&oh=00_AfTVVIBc9LcIR2D8XXRKaTzIWg7eLIezbD0CoLV1kF27xw&oe=687AF2F5", "label": null}], "is_final": true}}]