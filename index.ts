import fs from 'fs/promises';
import NodeFetchCache, { FileSystemCache } from 'node-fetch-cache';

const fetch = NodeFetchCache.create({
  cache: new FileSystemCache({
    cacheDirectory: 'fb_cache',
    ttl: 1000 * 60 * 60 * 24 * 7,
  }),
  shouldCacheResponse: (response) => response.ok,
});

function extractAttachments(node) {
  const attachments = [];
  const attachmentsToProcess = Array.isArray(node)
    ? node
    : Array.isArray(node?.attachments)
    ? node.attachments
    : [];

  for (const att of attachmentsToProcess) {
    const info = {};

    if (att.media && att.media.__typename !== 'GenericAttachmentMedia') {
      info.type = att.media.__typename;
      info.id = att.media.id;
    }

    const photo = att?.styles?.attachment?.media?.photo_image;
    if (photo?.uri) {
      info.uri = photo.uri;
      info.height = photo.height;
      info.width = photo.width;
    }

    if (Object.keys(info).length > 0) attachments.push(info);
  }

  return attachments;
}

function extractFacebookPostData(jsonArray) {
  const posts = [];

  jsonArray.forEach((dataBlob) => {
    const edges = dataBlob?.data?.node?.timeline_list_feed_units?.edges || [];

    for (const edge of edges) {
      const node = edge.node;

      const post = {
        postId: node.post_id,
        author: node.actors?.[0]
          ? {
              name: node.actors[0].name,
              id: node.actors[0].id,
              url: node.actors[0].url,
              profilePicture: node.actors[0].profile_picture?.uri || null,
            }
          : {},
        postText:
          node.message?.text ||
          node?.comet_sections?.content?.story?.comet_sections?.message?.story
            ?.message?.text ||
          null,
        sharedPostText: null,
        sharedPostInformation: null,
        attachments: [],
        timestamp: null,
        postUrl: node?.comet_sections?.content?.story?.wwwURL || null,
        reactions: {},
        totalComments: 0,
        totalShares: 0,
        otherImportantInfo: {},
        feedbackId: node?.feedback?.id,
      };

      // Shared post info
      const shared = node.attached_story;
      if (shared) {
        post.sharedPostInformation = {
          postId: shared.post_id,
          url: shared?.comet_sections?.content?.story?.wwwURL || null,
          author: shared?.actors?.[0]?.name || null,
          text:
            shared?.comet_sections?.content?.story?.comet_sections?.message
              ?.story?.message?.text || null,
          attachments: extractAttachments(
            shared?.comet_sections?.content?.story?.attachments
          ),
        };
      }

      if (node.attachments?.length) {
        post.attachments = extractAttachments(node);
      }

      const ts =
        node.timestamp?.story?.creation_time ||
        node?.comet_sections?.context_layout?.story?.comet_sections?.metadata?.find(
          (m) =>
            m.__typename === 'CometFeedStoryMinimizedTimestampStrategy' &&
            m.story?.creation_time
        )?.story?.creation_time;
      post.timestamp = ts ? new Date(ts * 1000).toISOString() : null;

      // Feedback
      const feedback =
        node?.comet_sections?.feedback?.story?.story_ufi_container?.story
          ?.feedback_context?.feedback_target_with_context
          ?.comet_ufi_summary_and_actions_renderer?.feedback ||
        node?.feedback?.story?.feedback_context?.feedback_target_with_context
          ?.comet_ufi_summary_and_actions_renderer?.feedback;

      if (feedback) {
        post.reactions.totalReactions = feedback.reaction_count?.count || 0;

        for (const r of feedback.top_reactions?.edges || []) {
          if (r.node?.localized_name) {
            post.reactions[r.node.localized_name.toLowerCase()] =
              r.reaction_count;
          }
        }

        post.totalComments =
          feedback?.comments_count_summary_renderer?.feedback
            ?.comment_rendering_instance?.comments?.total_count || 0;

        post.totalShares = feedback.share_count?.count || 0;
      }

      if (node.sponsored_data) {
        post.otherImportantInfo.isSponsored = true;
      }

      posts.push(post);
    }
  });

  return posts;
}

async function fetchAndExtractFacebookData(
  cursor = null,
  requestCount = 1,
  maxRequests = 5
) {
  if (requestCount > maxRequests) {
    console.log(`Reached request limit (${maxRequests})`);
    return [];
  }

  console.log(`Request ${requestCount}...`);

  const data = {
    fb_api_caller_class: 'RelayModern',
    fb_api_req_friendly_name: 'ProfileCometTimelineFeedRefetchQuery',
    variables: JSON.stringify({
      count: 1,
      cursor,
      feedLocation: 'TIMELINE',
      feedbackSource: 0,
      id: '100089366829069',
      privacySelectorRenderLocation: 'COMET_STREAM',
      renderLocation: 'timeline',
      scale: 1,
      stream_count: 1,
    }),
    server_timestamps: 'false',
    doc_id: '9455104801257585',
  };

  const formData = new URLSearchParams();
  for (const [key, val] of Object.entries(data)) {
    formData.append(key, val);
  }

  try {
    const response = await fetch('https://www.facebook.com/api/graphql/', {
      method: 'POST',
      headers: {
        accept: '*/*',
        'content-type': 'application/x-www-form-urlencoded',
        'x-fb-friendly-name': 'ProfileCometTimelineFeedRefetchQuery',
        'x-fb-lsd': 'AVpulFPjB1U',
      },
      body: formData.toString(),
    });

    const lines = (await response.text()).split('\n').filter(Boolean);
    const jsonObjects = [];

    let nextCursor = null;

    for (const line of lines) {
      try {
        const json = JSON.parse(line);
        jsonObjects.push(json);

        const endCursor =
          json.data?.page_info?.end_cursor ||
          json.data?.node?.timeline_list_feed_units?.page_info?.end_cursor;
        if (endCursor) nextCursor = endCursor;
      } catch (err) {
        console.error('Parse error:', err);
      }
    }

    await fs.writeFile(
      `raw_page${requestCount}.json`,
      JSON.stringify(jsonObjects)
    );

    const extracted = extractFacebookPostData(jsonObjects);
    await fs.writeFile(
      `posts_page${requestCount}.json`,
      JSON.stringify(extracted, null, 2)
    );

    if (nextCursor && requestCount < maxRequests) {
      const next = await fetchAndExtractFacebookData(
        nextCursor,
        requestCount + 1,
        maxRequests
      );
      return [...extracted, ...next];
    }

    return extracted;
  } catch (err) {
    console.error('Fetch error:', err);
    return [];
  }
}

// Run and save all
const allPosts = await fetchAndExtractFacebookData(null, 1, 5);
console.log(`Extracted ${allPosts.length} posts total`);
await fs.writeFile(
  'all_facebook_posts.json',
  JSON.stringify(allPosts, null, 2)
);
